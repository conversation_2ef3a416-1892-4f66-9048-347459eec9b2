import { InputType } from "@prisma/client";
import { prisma } from "../utils/prisma";

/**
 * TableQuestionRepository
 *
 * Handles all database operations related to table questions.
 * Responsible for:
 * - Creating, reading, updating, and deleting table questions
 * - Managing table columns and rows
 * - Handling cell values
 * - Maintaining parent-child relationships between columns
 */
class TableQuestionRepository {
  /**
   * Create a new table question with columns and rows
   *
   * @param data - Object containing table question data
   * @param data.label - Label for the table question
   * @param data.projectId - ID of the project this table belongs to
   * @param data.columns - Array of column objects with names and optional parent IDs
   * @param data.rows - Array of row objects with names
   * @returns The created table question with its columns and rows
   * @throws Error if validation fails or database operation fails
   */
  async createTableQuestion(data: {
    label: string;
    projectId: number;
    columns: { columnName: string; parentColumnId?: number }[];
    rows?: {
      rowsName: string;
      defaultValues?: { columnId: number; value: string; code?: string }[];
    }[];
  }) {
    try {
      // Validate input data
      if (!data.label || !data.label.trim()) {
        throw new Error("Table label is required");
      }

      if (!data.projectId || isNaN(data.projectId)) {
        throw new Error("Valid project ID is required");
      }

      if (
        !data.columns ||
        !Array.isArray(data.columns) ||
        data.columns.length === 0
      ) {
        throw new Error("At least one column is required");
      }

      // Rows are now optional - validate only if provided
      if (data.rows && !Array.isArray(data.rows)) {
        throw new Error("Rows must be an array if provided");
      }

      // Validate column data
      for (const column of data.columns) {
        if (!column.columnName || !column.columnName.trim()) {
          throw new Error("All columns must have valid names");
        }

        // If parentColumnId is provided, make sure it's a valid number
        if (column.parentColumnId !== undefined) {
          if (isNaN(column.parentColumnId)) {
            throw new Error("Invalid parent column ID");
          }

          // Check if the parent column exists
          const parentColumn = data.columns.find(
            (_, idx) => idx + 1 === column.parentColumnId
          );
          if (!parentColumn) {
            throw new Error(
              `Parent column with position ${column.parentColumnId} not found`
            );
          }

          // Check if the parent column is already a child column
          // We don't want children to have their own children (no grandchildren)
          if (parentColumn.parentColumnId !== undefined) {
            throw new Error(
              "Child columns cannot have their own children. Only parent columns can have children."
            );
          }
        }
      }

      // Count child columns per parent to enforce maximum of 2 children per parent
      const parentChildCount = new Map<number, number>();

      // Count how many children each parent has
      data.columns.forEach((column) => {
        if (column.parentColumnId !== undefined) {
          const parentPos = column.parentColumnId;
          parentChildCount.set(
            parentPos,
            (parentChildCount.get(parentPos) || 0) + 1
          );

          // Check if any parent has more than 2 children
          if (parentChildCount.get(parentPos)! > 2) {
            const parentColumn = data.columns.find(
              (_, i) => i + 1 === parentPos
            );
            throw new Error(
              `Parent column "${
                parentColumn?.columnName || "Unknown"
              }" cannot have more than 2 child columns`
            );
          }
        }
      });

      // Validate row data if rows are provided
      if (data.rows) {
        for (const row of data.rows) {
          if (!row.rowsName || !row.rowsName.trim()) {
            throw new Error("All rows must have valid names");
          }
        }
      }

      // Use a transaction to ensure data consistency
      return await prisma.$transaction(async (tx) => {
        // First, create the question
        const question = await tx.question.create({
          data: {
            label: data.label.trim(),
            inputType: "table" as InputType,
            projectId: data.projectId,
            position: 1, // Adding required position field
            // Create rows only if they are provided
            ...(data.rows &&
              data.rows.length > 0 && {
                tableRows: {
                  create: data.rows.map((row) => ({
                    rowsName: row.rowsName.trim(),
                  })),
                },
              }),
          },
          include: {
            tableRows: true,
          },
        });

        // Separate columns into parent columns (no parentColumnId) and child columns (with parentColumnId)
        const parentColumns = data.columns.filter(
          (col) => col.parentColumnId === undefined
        );
        const childColumns = data.columns.filter(
          (col) => col.parentColumnId !== undefined
        );

        // Create parent columns first - supports unlimited parent columns
        const createdParentColumns = await Promise.all(
          parentColumns.map((col) =>
            tx.tableColumn.create({
              data: {
                columnName: col.columnName.trim(),
                questionId: question.id,
              },
            })
          )
        );

        // Create a simple mapping of column names to IDs for the create function
        const columnNameMapping = new Map<string, number>();
        parentColumns.forEach((col, index) => {
          columnNameMapping.set(
            col.columnName.trim(),
            createdParentColumns[index].id
          );
        });

        // Now create child columns with the correct parent column IDs
        if (childColumns.length > 0) {
          // Create a map to store the created column IDs
          // This will map from the position in the original array (1-based) to the created column ID
          const createdColumnMap = new Map<number, number>();

          // Add parent columns to the map
          parentColumns.forEach((_, index) => {
            // Map the position in the original array (1-based) to the created column ID
            const position = data.columns.indexOf(parentColumns[index]) + 1;
            createdColumnMap.set(position, createdParentColumns[index].id);
          });

          // Process all child columns (all are direct children of parent columns)

          for (const col of childColumns) {
            // Get the parent column's position in the original array
            const parentPosition = col.parentColumnId;

            // Get the actual ID of the parent column that was created
            const actualParentId =
              parentPosition !== undefined
                ? createdColumnMap.get(parentPosition)
                : undefined;

            if (actualParentId) {
              // Create the column
              const createdColumn = await tx.tableColumn.create({
                data: {
                  columnName: col.columnName.trim(),
                  questionId: question.id,
                  parentColumnId: actualParentId,
                },
              });

              // Add the created column to the map
              // Use the position in the original array (1-based) as the key
              const position = data.columns.indexOf(col) + 1;
              createdColumnMap.set(position, createdColumn.id);
            } else {
              console.warn(
                `Could not find parent ID for column "${col.columnName}" with parent position ${parentPosition}`
              );
            }
          }

          // Check if any columns were not processed
          const processedPositions = Array.from(createdColumnMap.keys());
          const allPositions = data.columns.map((_, idx) => idx + 1);
          const unprocessedPositions = allPositions.filter(
            (pos) => !processedPositions.includes(pos)
          );

          if (unprocessedPositions.length > 0) {
            console.warn(
              `${unprocessedPositions.length} columns were not processed:`,
              unprocessedPositions.map((pos) => {
                const col = data.columns[pos - 1];
                return `${col.columnName} (position: ${pos}, parent: ${col.parentColumnId})`;
              })
            );
          }
        }

        // Process default values for cells if provided
        if (data.rows) {
          const defaultCellValues: {
            columnId: number;
            rowsId: number;
            value: string;
            code?: string;
            isDefault: boolean;
          }[] = [];

          // Create a combined column mapping that includes both parent and child columns
          const allColumnMapping = new Map<number, number>();

          // Add parent columns to the mapping
          parentColumns.forEach((col, index) => {
            const position = data.columns.indexOf(col) + 1; // 1-based index
            allColumnMapping.set(position, createdParentColumns[index].id);
          });

          // Process each row that has default values
          for (let i = 0; i < data.rows.length; i++) {
            const row = data.rows[i];
            const createdRow = question.tableRows[i];

            // If this row has default values
            if (
              row.defaultValues &&
              Array.isArray(row.defaultValues) &&
              row.defaultValues.length > 0
            ) {
              // Process each default value
              for (const defaultValue of row.defaultValues) {
                // For new columns, we need to map the position-based column ID to the actual created column ID
                let actualColumnId = defaultValue.columnId;

                // Check if this is a position-based ID (1-based index)
                if (
                  defaultValue.columnId > 0 &&
                  defaultValue.columnId <= data.columns.length
                ) {
                  // Try to get the actual column ID from the mapping
                  const mappedColumnId = allColumnMapping.get(
                    defaultValue.columnId
                  );
                  if (mappedColumnId) {
                    actualColumnId = mappedColumnId;
                  }
                }

                // Make sure the column exists
                const columnExistsCheck = await tx.tableColumn.findUnique({
                  where: { id: actualColumnId },
                });

                if (columnExistsCheck) {
                  defaultCellValues.push({
                    columnId: actualColumnId,
                    rowsId: createdRow.id,
                    value: defaultValue.value,
                    code: defaultValue.code,
                    isDefault: true,
                  });
                } else {
                  console.warn(
                    `Column with ID ${actualColumnId} not found when processing default value`
                  );
                }
              }
            }
          }

          // Create default cell values if any
          if (defaultCellValues.length > 0) {
            // Validate IDs are within safe integer range for PostgreSQL INT4
            for (const cellValue of defaultCellValues) {
              if (
                cellValue.columnId > 2147483647 ||
                cellValue.rowsId > 2147483647
              ) {
                throw new Error(
                  `ID values too large for database: columnId=${cellValue.columnId}, rowId=${cellValue.rowsId}. Maximum allowed value is 2147483647.`
                );
              }
            }

            await tx.columnRow.createMany({
              data: defaultCellValues,
              skipDuplicates: true,
            });
          }
        }

        // Return the complete question with all related data
        return tx.question.findUnique({
          where: { id: question.id },
          include: {
            tableColumns: {
              include: {
                childColumns: true, // Include only direct child columns
              },
            },
            tableRows: true,
          },
        });
      });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get a table question by ID with all its columns and rows
   *
   * @param id - ID of the table question to retrieve
   * @returns The table question with its columns and rows, or null if not found
   */
  async getTableQuestionById(id: number) {
    // First get all columns for this question
    const columns = await prisma.tableColumn.findMany({
      where: {
        questionId: id,
        parentColumnId: null, // Get only parent columns
      },
      include: {
        childColumns: {
          orderBy: {
            id: "asc",
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    });

    // Get the question with its rows
    const question = await prisma.question.findUnique({
      where: { id },
      include: {
        tableRows: {
          orderBy: {
            id: "asc",
          },
        },
      },
    });

    if (!question) {
      return null;
    }

    // Get default values (cell values marked as default)
    const defaultValues = await prisma.columnRow.findMany({
      where: {
        column: {
          questionId: id,
        },
        isDefault: true,
      },
    });

    // Group default values by row ID
    const defaultValuesByRow = new Map();
    defaultValues.forEach((dv) => {
      if (!defaultValuesByRow.has(dv.rowsId)) {
        defaultValuesByRow.set(dv.rowsId, []);
      }
      defaultValuesByRow.get(dv.rowsId).push({
        columnId: dv.columnId,
        value: dv.value,
        code: dv.code,
      });
    });

    // Add default values to each row
    const rowsWithDefaultValues = question.tableRows.map((row) => {
      const defaultValuesForRow = defaultValuesByRow.get(row.id) || [];
      return {
        ...row,
        defaultValues: defaultValuesForRow,
      };
    });

    // Get all cell values for this question
    const cellValues = await this.getCellValues(id);

    // Combine the results
    return {
      ...question,
      tableColumns: columns,
      tableRows: rowsWithDefaultValues,
      cellValues: cellValues,
    };
  }

  /**
   * Save cell values for a table question
   *
   * @param data - Object containing cell values data
   * @param data.questionId - ID of the table question
   * @param data.cellValues - Array of cell value objects
   * @returns The created or updated cell values
   * @throws Error if the table question is not found
   */
  async saveCellValues(data: {
    questionId: number;
    cellValues: {
      columnId: number;
      rowsId: number;
      value: string;
      code?: string;
      isDefault?: boolean;
    }[];
  }) {
    // First check if the question exists
    const question = await prisma.question.findUnique({
      where: { id: data.questionId },
      include: { tableColumns: true, tableRows: true },
    });

    if (!question) {
      throw new Error("Table question not found");
    }

    // Validate IDs are within safe integer range for PostgreSQL INT4
    for (const cell of data.cellValues) {
      if (cell.columnId > 2147483647 || cell.rowsId > 2147483647) {
        throw new Error(
          `ID values too large for database: columnId=${cell.columnId}, rowId=${cell.rowsId}. Maximum allowed value is 2147483647.`
        );
      }
    }

    // Create or update cell values
    const cellUpdates = data.cellValues.map((cell) => {
      return prisma.columnRow.upsert({
        where: {
          columnId_rowsId: {
            columnId: cell.columnId,
            rowsId: cell.rowsId,
          },
        },
        update: {
          value: cell.value,
          code: cell.code,
          isDefault: cell.isDefault ?? false,
        },
        create: {
          columnId: cell.columnId,
          rowsId: cell.rowsId,
          value: cell.value,
          code: cell.code,
          isDefault: cell.isDefault ?? false,
        },
      });
    });

    return prisma.$transaction(cellUpdates);
  }

  /**
   * Get all cell values for a table question
   *
   * @param questionId - ID of the table question
   * @returns A map of cell values indexed by columnId_rowsId
   */
  async getCellValues(questionId: number) {
    const cellValues = await prisma.columnRow.findMany({
      where: {
        column: {
          questionId,
        },
      },
      include: {
        column: true,
        row: true,
      },
    });

    // Format the cell values as a map for easier access
    const cellValueMap: Record<
      string,
      { value?: string; code?: string; isDefault?: boolean }
    > = {};

    cellValues.forEach((cell) => {
      cellValueMap[`${cell.columnId}_${cell.rowsId}`] = {
        value: cell.value || "",
        code: cell.code || "",
        isDefault: cell.isDefault || false,
      };
    });

    return cellValueMap;
  }

  /**
   * Update a table question with simplified column mapping approach
   *
   * @param id - ID of the table question to update
   * @param data - Updated table question data
   * @returns The updated table question with all related data
   * @throws Error if the table question is not found or validation fails
   */
  async updateTableQuestion(
    id: number,
    data: {
      label: string;
      columns: { id?: number; columnName: string; parentColumnId?: number }[];
      rows?: {
        id?: number;
        rowsName: string;
        defaultValues?: { columnId: number; value: string; code?: string }[];
      }[];
      projectId: number;
    }
  ) {
    try {
      console.log(`Starting simplified update for table question ${id}`);

      // Basic validation
      if (!data.label || !data.label.trim()) {
        throw new Error("Table label is required");
      }

      if (!data.columns || data.columns.length === 0) {
        throw new Error("At least one column is required");
      }

      // Validate column names
      for (const col of data.columns) {
        if (!col.columnName || !col.columnName.trim()) {
          throw new Error("All columns must have valid names");
        }
      }

      // Simple row validation if provided
      if (data.rows) {
        for (const row of data.rows) {
          if (!row.rowsName || !row.rowsName.trim()) {
            throw new Error("All rows must have valid names");
          }
        }
      }

      // Use a simplified transaction approach
      return await prisma.$transaction(async (tx) => {
        console.log(`Updating question ${id} with simplified approach`);

        // Update the question label and projectId
        await tx.question.update({
          where: { id },
          data: {
            label: data.label.trim(),
            projectId: data.projectId,
          },
        });

        // Get existing data for reference
        const existingColumns = await tx.tableColumn.findMany({
          where: { questionId: id },
        });

        const existingRows = await tx.tableRow.findMany({
          where: { questionId: id },
        });

        // Get existing cell values to preserve them
        const existingCellValues = await tx.columnRow.findMany({
          where: {
            column: {
              questionId: id,
            },
          },
        });

        console.log(`Found ${existingCellValues.length} existing cell values`);

        // Create a mapping from old column names to their cell values
        const cellValuesByColumnName = new Map<string, any[]>();

        for (const cellValue of existingCellValues) {
          const column = existingColumns.find(
            (col) => col.id === cellValue.columnId
          );
          if (column) {
            const columnName = column.columnName.trim();
            if (!cellValuesByColumnName.has(columnName)) {
              cellValuesByColumnName.set(columnName, []);
            }
            cellValuesByColumnName.get(columnName)!.push(cellValue);
          }
        }

        // STEP 1: Delete all existing columns (this will cascade delete cell values)
        await tx.tableColumn.deleteMany({
          where: { questionId: id },
        });

        console.log(`Deleted existing columns for question ${id}`);

        // STEP 2: Create new columns with simplified approach
        const columnNameToIdMapping = new Map<string, number>();

        // Create parent columns first
        const parentColumns = data.columns.filter(
          (col) => col.parentColumnId === undefined
        );

        for (const col of parentColumns) {
          const createdColumn = await tx.tableColumn.create({
            data: {
              columnName: col.columnName.trim(),
              questionId: id,
            },
          });

          columnNameToIdMapping.set(col.columnName.trim(), createdColumn.id);
          console.log(
            `Created parent column "${col.columnName}" with ID ${createdColumn.id}`
          );
        }

        // Create child columns
        const childColumns = data.columns.filter(
          (col) => col.parentColumnId !== undefined
        );

        for (const col of childColumns) {
          // Find parent column by position
          if (col.parentColumnId && col.parentColumnId > 0) {
            const parentColumn = data.columns[col.parentColumnId - 1];
            if (parentColumn) {
              const parentColumnId = columnNameToIdMapping.get(
                parentColumn.columnName.trim()
              );

              if (parentColumnId) {
                const createdColumn = await tx.tableColumn.create({
                  data: {
                    columnName: col.columnName.trim(),
                    questionId: id,
                    parentColumnId: parentColumnId,
                  },
                });

                columnNameToIdMapping.set(
                  col.columnName.trim(),
                  createdColumn.id
                );
                console.log(
                  `Created child column "${col.columnName}" with ID ${createdColumn.id}`
                );
              }
            }
          }
        }

        // STEP 3: Restore existing cell values using column name mapping
        if (cellValuesByColumnName.size > 0) {
          console.log(
            `Restoring cell values for ${cellValuesByColumnName.size} columns`
          );

          for (const [columnName, cellValues] of cellValuesByColumnName) {
            const newColumnId = columnNameToIdMapping.get(columnName);

            if (!newColumnId) {
              console.warn(
                `Could not find new column ID for column "${columnName}"`
              );
              continue;
            }

            for (const cellValue of cellValues) {
              // Check if the row still exists
              const rowExists =
                existingRows.some((row) => row.id === cellValue.rowsId) ||
                (data.rows &&
                  data.rows.some((row) => row.id === cellValue.rowsId));

              if (!rowExists) {
                console.warn(
                  `Row ${cellValue.rowsId} no longer exists, skipping cell value for column "${columnName}"`
                );
                continue;
              }

              // Create the cell value
              try {
                await tx.columnRow.create({
                  data: {
                    columnId: newColumnId,
                    rowsId: cellValue.rowsId,
                    value: cellValue.value,
                    code: cellValue.code,
                    isDefault: cellValue.isDefault,
                  },
                });
              } catch (error) {
                console.warn(
                  `Failed to restore cell value for column "${columnName}":`,
                  error
                );
              }
            }
          }
        }

        // STEP 4: Handle rows (if provided)

        // Track all cell values that should exist after the update
        const expectedCellValues = new Set<string>();

        if (data.rows) {
          console.log(`Processing ${data.rows.length} rows`);

          // Update existing rows
          const rowsToUpdate = data.rows.filter((row) => row.id !== undefined);
          for (const row of rowsToUpdate) {
            await tx.tableRow.update({
              where: { id: row.id! },
              data: { rowsName: row.rowsName.trim() },
            });

            // Handle default values for this row
            if (row.defaultValues && row.defaultValues.length > 0) {
              console.log(
                `Processing ${row.defaultValues.length} default values for row ${row.id}`
              );

              for (const dv of row.defaultValues) {
                console.log(`Processing default value:`, dv);

                // The columnId from frontend is the actual database column ID
                // We need to map it to the new column ID after recreation
                const oldColumnId = dv.columnId;

                // Find the old column that had this ID
                const oldColumn = existingColumns.find(
                  (col) => col.id === oldColumnId
                );

                if (oldColumn) {
                  // Find the new column ID by column name
                  const columnName = oldColumn.columnName.trim();
                  const newColumnId = columnNameToIdMapping.get(columnName);

                  if (newColumnId) {
                    // Track this cell value as expected to exist
                    expectedCellValues.add(`${newColumnId}_${row.id}`);

                    try {
                      await tx.columnRow.upsert({
                        where: {
                          columnId_rowsId: {
                            columnId: newColumnId,
                            rowsId: row.id!,
                          },
                        },
                        update: {
                          value: dv.value,
                          code: dv.code,
                          isDefault: true,
                        },
                        create: {
                          columnId: newColumnId,
                          rowsId: row.id!,
                          value: dv.value,
                          code: dv.code,
                          isDefault: true,
                        },
                      });
                      console.log(
                        `Updated default value for column "${columnName}" (old ID: ${oldColumnId}, new ID: ${newColumnId}), row ${row.id}: ${dv.value}`
                      );
                    } catch (error) {
                      console.warn(
                        `Failed to upsert cell value for row ${row.id}:`,
                        error
                      );
                    }
                  } else {
                    console.warn(
                      `Could not find new column ID for column "${columnName}" (old ID: ${oldColumnId})`
                    );
                  }
                } else {
                  console.warn(
                    `Could not find old column with ID ${oldColumnId} for default value`
                  );
                }
              }
            }
          }

          // Create new rows
          const rowsToAdd = data.rows.filter((row) => row.id === undefined);
          for (const row of rowsToAdd) {
            const createdRow = await tx.tableRow.create({
              data: {
                rowsName: row.rowsName.trim(),
                questionId: id,
              },
            });

            // Handle default values for new row
            if (row.defaultValues && row.defaultValues.length > 0) {
              console.log(
                `Processing ${row.defaultValues.length} default values for new row ${createdRow.id}`
              );

              for (const dv of row.defaultValues) {
                console.log(`Processing default value for new row:`, dv);

                // The columnId from frontend is the actual database column ID
                // We need to map it to the new column ID after recreation
                const oldColumnId = dv.columnId;

                // Find the old column that had this ID
                const oldColumn = existingColumns.find(
                  (col) => col.id === oldColumnId
                );

                if (oldColumn) {
                  // Find the new column ID by column name
                  const columnName = oldColumn.columnName.trim();
                  const newColumnId = columnNameToIdMapping.get(columnName);

                  if (newColumnId) {
                    // Track this cell value as expected to exist
                    expectedCellValues.add(`${newColumnId}_${createdRow.id}`);

                    try {
                      await tx.columnRow.create({
                        data: {
                          columnId: newColumnId,
                          rowsId: createdRow.id,
                          value: dv.value,
                          code: dv.code,
                          isDefault: true,
                        },
                      });
                      console.log(
                        `Created default value for column "${columnName}" (old ID: ${oldColumnId}, new ID: ${newColumnId}), row ${createdRow.id}: ${dv.value}`
                      );
                    } catch (error) {
                      console.warn(
                        `Failed to create cell value for new row ${createdRow.id}:`,
                        error
                      );
                    }
                  } else {
                    console.warn(
                      `Could not find new column ID for column "${columnName}" (old ID: ${oldColumnId})`
                    );
                  }
                } else {
                  console.warn(
                    `Could not find old column with ID ${oldColumnId} for default value`
                  );
                }
              }
            }
          }

          // STEP 5: Clean up cell values that are no longer needed
          // Get all current cell values for this question
          const currentCellValues = await tx.columnRow.findMany({
            where: {
              column: {
                questionId: id,
              },
            },
          });

          // Delete cell values that are not in the expected set
          const cellValuesToDelete = currentCellValues.filter((cellValue) => {
            const key = `${cellValue.columnId}_${cellValue.rowsId}`;
            return !expectedCellValues.has(key);
          });

          if (cellValuesToDelete.length > 0) {
            console.log(
              `Deleting ${cellValuesToDelete.length} obsolete cell values`
            );

            for (const cellValue of cellValuesToDelete) {
              try {
                await tx.columnRow.delete({
                  where: {
                    columnId_rowsId: {
                      columnId: cellValue.columnId,
                      rowsId: cellValue.rowsId,
                    },
                  },
                });
                console.log(
                  `Deleted cell value for column ${cellValue.columnId}, row ${cellValue.rowsId}`
                );
              } catch (error) {
                console.warn(`Failed to delete cell value:`, error);
              }
            }
          }

          // Delete rows that are no longer needed
          const rowIdsToKeep = data.rows
            .filter((row) => row.id !== undefined)
            .map((row) => row.id!);
          const rowIdsToDelete = existingRows
            .filter((row) => !rowIdsToKeep.includes(row.id))
            .map((row) => row.id);

          if (rowIdsToDelete.length > 0) {
            await tx.tableRow.deleteMany({
              where: { id: { in: rowIdsToDelete } },
            });
          }
        } else {
          // If no rows provided, delete all existing rows and cell values
          await tx.tableRow.deleteMany({
            where: { questionId: id },
          });

          // Also delete all cell values since there are no rows
          await tx.columnRow.deleteMany({
            where: {
              column: {
                questionId: id,
              },
            },
          });
          console.log("Deleted all cell values since no rows were provided");
        }

        console.log(`Successfully updated table question ${id}`);

        // Get the updated question with all related data
        const updatedQuestion = await tx.question.findUnique({
          where: { id },
          include: {
            tableColumns: {
              include: {
                childColumns: true,
              },
            },
            tableRows: true,
          },
        });

        if (!updatedQuestion) {
          throw new Error("Failed to retrieve updated question");
        }

        // Get the updated cell values
        const cellValues = await this.getCellValues(id);

        // Get default values (cell values marked as default)
        const defaultValues = await tx.columnRow.findMany({
          where: {
            column: {
              questionId: id,
            },
            isDefault: true,
          },
        });

        // Group default values by row ID
        const defaultValuesByRow = new Map();
        defaultValues.forEach((dv) => {
          if (!defaultValuesByRow.has(dv.rowsId)) {
            defaultValuesByRow.set(dv.rowsId, []);
          }
          defaultValuesByRow.get(dv.rowsId).push({
            columnId: dv.columnId,
            value: dv.value,
            code: dv.code,
          });
        });

        // Add default values to each row
        const rowsWithDefaultValues = updatedQuestion.tableRows.map((row) => {
          const defaultValuesForRow = defaultValuesByRow.get(row.id) || [];
          return {
            ...row,
            defaultValues: defaultValuesForRow,
          };
        });

        // Return the complete updated question with cell values
        return {
          ...updatedQuestion,
          tableRows: rowsWithDefaultValues,
          cellValues: cellValues,
        };
      });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Delete a table question and all related data
   *
   * @param id - ID of the table question to delete
   * @returns The deleted table question
   * @throws Error if the table question is not found
   */
  async deleteTableQuestion(id: number) {
    // This will cascade delete all related columns, rows, and cell values
    return prisma.question.delete({
      where: { id },
    });
  }

  /**
   * Get all table questions for a project
   *
   * @param projectId - ID of the project
   * @returns Array of table questions with their columns and rows
   */
  async getTableQuestionsByProjectId(projectId: number) {
    // First get all table questions for this project
    const questions = await prisma.question.findMany({
      where: {
        projectId,
        inputType: "table",
        tableColumns: {
          some: {},
        },
      },
      include: {
        tableRows: {
          orderBy: {
            id: "asc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // If no questions found, return empty array
    if (questions.length === 0) {
      return [];
    }

    // Get all question IDs
    const questionIds = questions.map((q) => q.id);

    // Get all parent columns for these questions with their children
    const allParentColumns = await prisma.tableColumn.findMany({
      where: {
        questionId: { in: questionIds },
        parentColumnId: null, // Only get parent columns
      },
      include: {
        childColumns: {
          orderBy: {
            id: "asc",
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    });

    // Group columns by question ID
    const columnsByQuestionId = new Map<number, any[]>();
    allParentColumns.forEach((column) => {
      const questionId = column.questionId;
      if (!columnsByQuestionId.has(questionId)) {
        columnsByQuestionId.set(questionId, []);
      }
      columnsByQuestionId.get(questionId)!.push(column);
    });

    // Combine the results
    return questions.map((question) => ({
      ...question,
      tableColumns: columnsByQuestionId.get(question.id) || [],
    }));
  }
}

export default new TableQuestionRepository();
