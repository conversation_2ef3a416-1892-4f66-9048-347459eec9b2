import { InputType } from "@prisma/client";
import { prisma } from "../utils/prisma";

/**
 * TableQuestionRepository - Clean Implementation
 *
 * Simplified version with clean column mapping approach
 */
class TableQuestionRepository {
  /**
   * Update a table question with simplified column mapping approach
   *
   * @param id - ID of the table question to update
   * @param data - Updated table question data
   * @returns The updated table question with all related data
   * @throws Error if the table question is not found or validation fails
   */
  async updateTableQuestion(
    id: number,
    data: {
      label: string;
      columns: { id?: number; columnName: string; parentColumnId?: number }[];
      rows?: {
        id?: number;
        rowsName: string;
        defaultValues?: { columnId: number; value: string; code?: string }[];
      }[];
      projectId: number;
    }
  ) {
    try {
      console.log(`Starting simplified update for table question ${id}`);

      // Basic validation
      if (!data.label || !data.label.trim()) {
        throw new Error("Table label is required");
      }

      if (!data.columns || data.columns.length === 0) {
        throw new Error("At least one column is required");
      }

      // Validate column names
      for (const col of data.columns) {
        if (!col.columnName || !col.columnName.trim()) {
          throw new Error("All columns must have valid names");
        }
      }

      // Simple row validation if provided
      if (data.rows) {
        for (const row of data.rows) {
          if (!row.rowsName || !row.rowsName.trim()) {
            throw new Error("All rows must have valid names");
          }
        }
      }

      // Use a simplified transaction approach
      return await prisma.$transaction(async (tx) => {
        console.log(`Updating question ${id} with simplified approach`);

        // Update the question label and projectId
        await tx.question.update({
          where: { id },
          data: {
            label: data.label.trim(),
            projectId: data.projectId,
          },
        });

        // Get existing data for reference
        const existingColumns = await tx.tableColumn.findMany({
          where: { questionId: id },
        });

        const existingRows = await tx.tableRow.findMany({
          where: { questionId: id },
        });

        // Get existing cell values to preserve them
        const existingCellValues = await tx.columnRow.findMany({
          where: {
            column: {
              questionId: id,
            },
          },
        });

        console.log(`Found ${existingCellValues.length} existing cell values`);

        // Create a mapping from old column names to their cell values
        const cellValuesByColumnName = new Map<string, any[]>();

        for (const cellValue of existingCellValues) {
          const column = existingColumns.find(
            (col) => col.id === cellValue.columnId
          );
          if (column) {
            const columnName = column.columnName.trim();
            if (!cellValuesByColumnName.has(columnName)) {
              cellValuesByColumnName.set(columnName, []);
            }
            cellValuesByColumnName.get(columnName)!.push(cellValue);
          }
        }

        // STEP 1: Delete all existing columns (this will cascade delete cell values)
        await tx.tableColumn.deleteMany({
          where: { questionId: id },
        });

        console.log(`Deleted existing columns for question ${id}`);

        // STEP 2: Create new columns with simplified approach
        const columnNameToIdMapping = new Map<string, number>();

        // Create parent columns first
        const parentColumns = data.columns.filter(
          (col) => col.parentColumnId === undefined
        );

        for (const col of parentColumns) {
          const createdColumn = await tx.tableColumn.create({
            data: {
              columnName: col.columnName.trim(),
              questionId: id,
            },
          });

          columnNameToIdMapping.set(col.columnName.trim(), createdColumn.id);
          console.log(
            `Created parent column "${col.columnName}" with ID ${createdColumn.id}`
          );
        }

        // Create child columns
        const childColumns = data.columns.filter(
          (col) => col.parentColumnId !== undefined
        );

        for (const col of childColumns) {
          // Find parent column by position
          if (col.parentColumnId && col.parentColumnId > 0) {
            const parentColumn = data.columns[col.parentColumnId - 1];
            if (parentColumn) {
              const parentColumnId = columnNameToIdMapping.get(
                parentColumn.columnName.trim()
              );

              if (parentColumnId) {
                const createdColumn = await tx.tableColumn.create({
                  data: {
                    columnName: col.columnName.trim(),
                    questionId: id,
                    parentColumnId: parentColumnId,
                  },
                });

                columnNameToIdMapping.set(
                  col.columnName.trim(),
                  createdColumn.id
                );
                console.log(
                  `Created child column "${col.columnName}" with ID ${createdColumn.id}`
                );
              }
            }
          }
        }

        // STEP 3: Restore existing cell values using column name mapping
        if (cellValuesByColumnName.size > 0) {
          console.log(
            `Restoring cell values for ${cellValuesByColumnName.size} columns`
          );

          for (const [columnName, cellValues] of cellValuesByColumnName) {
            const newColumnId = columnNameToIdMapping.get(columnName);

            if (!newColumnId) {
              console.warn(
                `Could not find new column ID for column "${columnName}"`
              );
              continue;
            }

            for (const cellValue of cellValues) {
              // Check if the row still exists
              const rowExists =
                existingRows.some((row) => row.id === cellValue.rowsId) ||
                (data.rows &&
                  data.rows.some((row) => row.id === cellValue.rowsId));

              if (!rowExists) {
                console.warn(
                  `Row ${cellValue.rowsId} no longer exists, skipping cell value for column "${columnName}"`
                );
                continue;
              }

              // Create the cell value
              try {
                await tx.columnRow.create({
                  data: {
                    columnId: newColumnId,
                    rowsId: cellValue.rowsId,
                    value: cellValue.value,
                    code: cellValue.code,
                    isDefault: cellValue.isDefault,
                  },
                });
              } catch (error) {
                console.warn(
                  `Failed to restore cell value for column "${columnName}":`,
                  error
                );
              }
            }
          }
        }

        // STEP 4: Handle rows (if provided)
        if (data.rows) {
          console.log(`Processing ${data.rows.length} rows`);

          // Update existing rows
          const rowsToUpdate = data.rows.filter((row) => row.id !== undefined);
          for (const row of rowsToUpdate) {
            await tx.tableRow.update({
              where: { id: row.id! },
              data: { rowsName: row.rowsName.trim() },
            });

            // Handle default values for this row using position-based mapping
            if (row.defaultValues && row.defaultValues.length > 0) {
              for (const dv of row.defaultValues) {
                // Use position-based mapping: columnId represents position in frontend array
                const columnPosition = dv.columnId;
                const matchingColumn = data.columns[columnPosition - 1];
                
                if (matchingColumn) {
                  const columnName = matchingColumn.columnName.trim();
                  const actualColumnId = columnNameToIdMapping.get(columnName);

                  if (actualColumnId) {
                    try {
                      await tx.columnRow.upsert({
                        where: {
                          columnId_rowsId: {
                            columnId: actualColumnId,
                            rowsId: row.id!,
                          },
                        },
                        update: {
                          value: dv.value,
                          code: dv.code,
                          isDefault: true,
                        },
                        create: {
                          columnId: actualColumnId,
                          rowsId: row.id!,
                          value: dv.value,
                          code: dv.code,
                          isDefault: true,
                        },
                      });
                      console.log(
                        `Updated default value for column "${columnName}" (position ${columnPosition}), row ${row.id}: ${dv.value}`
                      );
                    } catch (error) {
                      console.warn(
                        `Failed to upsert cell value for row ${row.id}:`,
                        error
                      );
                    }
                  } else {
                    console.warn(
                      `Could not find column ID for column "${columnName}" at position ${columnPosition}`
                    );
                  }
                } else {
                  console.warn(
                    `Could not find column at position ${columnPosition} for default value`
                  );
                }
              }
            }
          }

          // Create new rows
          const rowsToAdd = data.rows.filter((row) => row.id === undefined);
          for (const row of rowsToAdd) {
            const createdRow = await tx.tableRow.create({
              data: {
                rowsName: row.rowsName.trim(),
                questionId: id,
              },
            });

            // Handle default values for new row using position-based mapping
            if (row.defaultValues && row.defaultValues.length > 0) {
              for (const dv of row.defaultValues) {
                // Use position-based mapping: columnId represents position in frontend array
                const columnPosition = dv.columnId;
                const matchingColumn = data.columns[columnPosition - 1];
                
                if (matchingColumn) {
                  const columnName = matchingColumn.columnName.trim();
                  const actualColumnId = columnNameToIdMapping.get(columnName);

                  if (actualColumnId) {
                    try {
                      await tx.columnRow.create({
                        data: {
                          columnId: actualColumnId,
                          rowsId: createdRow.id,
                          value: dv.value,
                          code: dv.code,
                          isDefault: true,
                        },
                      });
                      console.log(
                        `Created default value for column "${columnName}" (position ${columnPosition}), row ${createdRow.id}: ${dv.value}`
                      );
                    } catch (error) {
                      console.warn(
                        `Failed to create cell value for new row ${createdRow.id}:`,
                        error
                      );
                    }
                  } else {
                    console.warn(
                      `Could not find column ID for column "${columnName}" at position ${columnPosition}`
                    );
                  }
                } else {
                  console.warn(
                    `Could not find column at position ${columnPosition} for default value`
                  );
                }
              }
            }
          }

          // Delete rows that are no longer needed
          const rowIdsToKeep = data.rows
            .filter((row) => row.id !== undefined)
            .map((row) => row.id!);
          const rowIdsToDelete = existingRows
            .filter((row) => !rowIdsToKeep.includes(row.id))
            .map((row) => row.id);

          if (rowIdsToDelete.length > 0) {
            await tx.tableRow.deleteMany({
              where: { id: { in: rowIdsToDelete } },
            });
          }
        } else {
          // If no rows provided, delete all existing rows
          await tx.tableRow.deleteMany({
            where: { questionId: id },
          });
        }

        console.log(`Successfully updated table question ${id}`);

        // Return the updated question with all related data
        return tx.question.findUnique({
          where: { id },
          include: {
            tableColumns: {
              include: {
                childColumns: true,
              },
            },
            tableRows: true,
          },
        });
      });
    } catch (error: any) {
      throw error;
    }
  }
}

export default new TableQuestionRepository();
