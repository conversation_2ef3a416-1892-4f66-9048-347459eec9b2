"use client";

import React, {
  useState,
  useEffect,
  use<PERSON><PERSON>back,
  useMemo,
  useRef,
} from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow as UITableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
  fetchTableStructure,
  TableRow as TableRowType,
  CellValue,
  DefaultValue,
} from "../../lib/api/table";
import { Plus, Trash2 } from "lucide-react";
import debounce from "lodash/debounce";
import { useQuery } from "@tanstack/react-query";

// Define TableColumn interface to ensure childColumns is optional
interface TableColumn {
  id: number;
  columnName: string;
  parentColumnId?: number | null;
  childColumns?: TableColumn[];
}

interface TableInputProps {
  questionId: number;
  value: string | CellValue[];
  onChange: (value: CellValue[]) => void;
  required?: boolean;
}

// Memoize the TableInput component to prevent unnecessary re-renders
export const TableInput = React.memo(
  ({ questionId, value, onChange, required = false }: TableInputProps) => {
    // State declarations
    const [columns, setColumns] = useState<TableColumn[]>([]);
    const [rows, setRows] = useState<TableRowType[]>([]);
    const [cellValues, setCellValues] = useState<Record<string, string>>({});
    const [userInputValues, setUserInputValues] = useState<
      Record<string, string>
    >({});

    // Ref to track cellValues for responsive input
    const cellValuesRef = useRef<Record<string, string>>(cellValues);

    // Update ref whenever cellValues changes
    useEffect(() => {
      cellValuesRef.current = cellValues;
    }, [cellValues]);

    // Process columns to create a flat structure with parent-child relationships
    const processColumns = useCallback(
      (tableData: { tableColumns?: TableColumn[] }): TableColumn[] => {
        if (!tableData?.tableColumns) return [];

        const flattenedColumns: TableColumn[] = [];
        const parentColumns = tableData.tableColumns.filter(
          (col: TableColumn) =>
            col.parentColumnId === null || col.parentColumnId === undefined
        );

        parentColumns.forEach((parentCol: TableColumn) => {
          flattenedColumns.push(parentCol);
          const childColumns = parentCol.childColumns;
          if (
            childColumns &&
            Array.isArray(childColumns) &&
            childColumns.length > 0
          ) {
            childColumns.forEach((childCol: TableColumn) => {
              flattenedColumns.push({
                id: childCol.id,
                columnName: childCol.columnName,
                parentColumnId: childCol.parentColumnId,
              });
            });
          }
        });

        return flattenedColumns;
      },
      []
    );

    // Memoize grouped columns to avoid recomputation on every render
    const groupedColumns = useMemo(() => {
      if (!columns.length) {
        return {
          parentColumns: [],
          columnMap: new Map<number, TableColumn[]>(),
          hasChildColumns: false,
        };
      }

      const parentColumns = columns.filter((col) => !col.parentColumnId);
      const columnMap = new Map<number, TableColumn[]>();

      parentColumns.forEach((parentCol) => {
        const childColumns = columns.filter(
          (col) => col.parentColumnId === parentCol.id
        );
        columnMap.set(parentCol.id, childColumns);
      });

      const hasChildColumns = parentColumns.some(
        (p) => (columnMap.get(p.id) ?? []).length > 0
      );

      return { parentColumns, columnMap, hasChildColumns };
    }, [columns]);

    // Use React Query to fetch table structure with automatic refetching
    const {
      data: tableData,
      isLoading: loading,
      error: queryError,
    } = useQuery({
      queryKey: ["tableStructure", questionId],
      queryFn: () => fetchTableStructure(questionId),
      enabled: questionId > 0,
      staleTime: 0, // Always consider data stale to ensure fresh fetches
      gcTime: 0, // Don't cache the data
    });

    // Set error state based on query error
    const error = queryError ? "Failed to load table structure" : null;

    // Process table data when it changes
    useEffect(() => {
      if (!tableData) {
        setColumns([]);
        setRows([]);
        setCellValues({});
        return;
      }

      console.log("Processing table data:", tableData);
      setColumns(processColumns(tableData));
      setRows(tableData.tableRows || []);

      // Process default values from rows and cell values
      const defaultCellValues: Record<string, string> = {};
      const defaultCellKeys: string[] = [];

      // Define the type for cell values
      interface CellValueWithDefault {
        value: string;
        isDefault: boolean;
      }

      // Check if we have cell values in the response
      if (tableData.cellValues) {
        console.log("Cell values found in response:", tableData.cellValues);
        // Process cell values from the response
        (
          Object.entries(tableData.cellValues) as [
            string,
            string | CellValueWithDefault
          ][]
        ).forEach(([key, value]) => {
          if (typeof value === "object" && value !== null) {
            // Handle object format with value property
            const cellValue = value.value || "";
            if (cellValue.trim() !== "") {
              defaultCellValues[key] = cellValue;
              defaultCellKeys.push(key);
              console.log(
                `Loading cell value from object for ${key}: ${cellValue} (isDefault: ${value.isDefault})`
              );
            }
          } else if (typeof value === "string" && value.trim() !== "") {
            // Handle string format
            defaultCellValues[key] = value;
            defaultCellKeys.push(key);
            console.log(`Loading cell value from string for ${key}: ${value}`);
          }
        });
      }

      // Also check if tableRows have defaultValues (for backward compatibility)
      if (tableData.tableRows && tableData.tableRows.length > 0) {
        tableData.tableRows.forEach((row: TableRowType) => {
          // Check if defaultValues exists on the row
          // In some API responses, rows might not have the defaultValues property
          if (
            row.defaultValues &&
            Array.isArray(row.defaultValues) &&
            row.defaultValues.length > 0
          ) {
            row.defaultValues.forEach((dv: DefaultValue) => {
              const key = `${dv.columnId}_${row.id}`;
              defaultCellValues[key] = dv.value;
              defaultCellKeys.push(key);
              console.log(
                `Loading default value from row for ${key}: ${dv.value}`
              );
            });
          } else {
            console.log(
              `Row ${row.id} has no defaultValues property or it's empty`
            );
          }
        });
      }

      // Set default values in the cellValues state
      if (Object.keys(defaultCellValues).length > 0) {
        console.log("Setting cell values:", defaultCellValues);
        console.log("Default cell keys:", defaultCellKeys);

        // Force a re-render by creating a new object reference
        const newCellValues = { ...defaultCellValues };

        // Log each cell value for debugging
        Object.entries(newCellValues).forEach(([key, value]) => {
          console.log(`Setting cell value for ${key}: ${value}`);
        });

        // Set the cell values directly without merging with previous state
        setCellValues(newCellValues);

        // Wait a moment and then set the values again to ensure they're applied
        setTimeout(() => {
          console.log("Re-applying cell values to ensure they are set");
          setCellValues((prevValues) => ({ ...prevValues }));
        }, 100);
      } else {
        console.warn("No default or cell values found for this table");
        setCellValues({});
      }
    }, [tableData, processColumns]);

    // Handle initial value parsing and form reset
    useEffect(() => {
      if (loading) return;

      const initialCellValues: Record<string, string> = {};
      let cellData: CellValue[] = [];

      if (typeof value === "string" && value.trim()) {
        try {
          cellData = JSON.parse(value);
        } catch {
          cellData = [];
        }
      } else if (Array.isArray(value)) {
        cellData = value;
      }

      cellData.forEach((cell) => {
        initialCellValues[`${cell.columnId}_${cell.rowsId}`] = cell.value;
      });

      const isFormReset = !value || (Array.isArray(value) && !value.length);

      if (isFormReset) {
        setUserInputValues({});
      } else if (Object.keys(initialCellValues).length) {
        setUserInputValues((prev) => ({ ...prev, ...initialCellValues }));
      }
    }, [value, loading]);

    // Effect to ensure default values are always included in form submission
    useEffect(() => {
      if (!loading && Object.keys(cellValues).length > 0) {
        // Combine default values and user input values
        const allValues = { ...cellValues, ...userInputValues };

        const updatedCellValues: CellValue[] = Object.entries(allValues)
          .filter(([, value]) => value.trim())
          .map(([key, value]) => {
            const [colId, rowId] = key.split("_").map(Number);
            return { columnId: colId, rowsId: rowId, value };
          });

        // Only update if there are values to submit
        if (updatedCellValues.length > 0) {
          onChange(updatedCellValues);
        }
      }
    }, [cellValues, userInputValues, loading, onChange]);

    // Debounced cell change handler to reduce rapid state updates
    const handleCellChange = useCallback(
      debounce(
        (columnId: number, rowId: number | string, newValue: string) => {
          // Always combine default values and user input values for submission
          const allValues = { ...cellValues, ...userInputValues };
          allValues[`${columnId}_${rowId}`] = newValue;

          const updatedCellValues: CellValue[] = Object.entries(allValues)
            .filter(([, value]) => value.trim())
            .map(([key, value]) => {
              const [colId, rowId] = key.split("_").map(Number);
              return { columnId: colId, rowsId: rowId, value };
            });

          onChange(updatedCellValues);
        },
        300,
        { leading: false, trailing: true }
      ),
      [onChange, cellValues, userInputValues]
    );

    // Handle input change with immediate local state update for responsiveness
    const handleInputChange = useCallback(
      (columnId: number, rowId: number | string, newValue: string) => {
        // Update user input values (separate from default values)
        setUserInputValues((prev) => ({
          ...prev,
          [`${columnId}_${rowId}`]: newValue,
        }));
        // Trigger debounced update
        handleCellChange(columnId, rowId, newValue);
      },
      [handleCellChange]
    );

    // Cleanup debounce on unmount
    useEffect(() => {
      return () => {
        handleCellChange.cancel();
      };
    }, [handleCellChange]);

    // Add a new row
    const handleAddRow = useCallback((e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      setRows((prevRows) => [
        ...prevRows,
        {
          id: -(prevRows.length + 1),
          rowsName: (prevRows.length + 1).toString(),
        },
      ]);
    }, []);

    // Remove a row and clean up cell values
    const handleRemoveRow = useCallback(
      (rowIndex: number) => {
        if (rows.length <= 1) return;

        const rowToRemove = rows[rowIndex];

        setRows((prevRows) =>
          prevRows
            .filter((_, index) => index !== rowIndex)
            .map((row, idx) => ({
              ...row,
              rowsName: (idx + 1).toString(),
            }))
        );

        setUserInputValues((prevValues) => {
          const newValues = { ...prevValues };
          Object.keys(newValues).forEach((key) => {
            const [_, rowId] = key.split("_");
            if (rowId === rowToRemove.id.toString()) {
              delete newValues[key];
            }
          });

          // Combine with default values for submission
          const allValues = { ...cellValues, ...newValues };
          const updatedCellValues: CellValue[] = Object.entries(allValues)
            .filter(([, value]) => value.trim())
            .map(([key, value]) => {
              const [colId, rowId] = key.split("_").map(Number);
              return { columnId: colId, rowsId: rowId, value };
            });

          onChange(updatedCellValues);
          return newValues;
        });
      },
      [rows, onChange, cellValues]
    );

    // Memoize the table body to prevent re-rendering unchanged rows
    // Note: For large tables (>50 rows), consider using react-window for virtualization:
    // import { FixedSizeList } from "react-window";
    // <FixedSizeList height={400} itemCount={rows.length} itemSize={48} width="100%">
    //   {({ index, style }) => <Row index={index} style={style} />}
    // </FixedSizeList>
    const tableBody = useMemo(() => {
      if (!rows.length) {
        return (
          <UITableRow>
            <TableCell className="border p-1 text-center font-medium bg-blue-50/50">
              Row 1
            </TableCell>
            {groupedColumns.parentColumns.map((parentCol) => {
              const childColumns =
                groupedColumns.columnMap.get(parentCol.id) || [];
              if (!childColumns.length) {
                const cellValue = cellValues[`${parentCol.id}_no_row`] || "";
                const hasDefaultValue = cellValue.trim() !== "";

                return (
                  <TableCell
                    key={`cell-${parentCol.id}-no-row`}
                    className="border p-1"
                  >
                    {hasDefaultValue ? (
                      // Show default value as static text with normal input styling
                      <div className="w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900">
                        {cellValue}
                      </div>
                    ) : (
                      // Show input field for user entry
                      <Input
                        value={userInputValues[`${parentCol.id}_no_row`] || ""}
                        onChange={(e) =>
                          handleInputChange(
                            parentCol.id,
                            "no_row",
                            e.target.value
                          )
                        }
                        placeholder="Enter value"
                        className="w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500"
                      />
                    )}
                  </TableCell>
                );
              }
              return childColumns.map((childCol) => {
                const cellValue = cellValues[`${childCol.id}_no_row`] || "";
                const hasDefaultValue = cellValue.trim() !== "";

                return (
                  <TableCell
                    key={`cell-${childCol.id}-no-row`}
                    className="border p-1"
                  >
                    {hasDefaultValue ? (
                      // Show default value as static text with normal input styling
                      <div className="w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900">
                        {cellValue}
                      </div>
                    ) : (
                      // Show input field for user entry
                      <Input
                        value={userInputValues[`${childCol.id}_no_row`] || ""}
                        onChange={(e) =>
                          handleInputChange(
                            childCol.id,
                            "no_row",
                            e.target.value
                          )
                        }
                        placeholder="Enter value"
                        className="w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500"
                      />
                    )}
                  </TableCell>
                );
              });
            })}
            <TableCell className="border p-1 w-10"></TableCell>
          </UITableRow>
        );
      }

      return rows.map((row, rowIndex) => (
        <UITableRow
          key={row.id}
          className={rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}
        >
          <TableCell className="border p-1 text-center font-medium bg-blue-50/50">
            {rowIndex + 1}
          </TableCell>
          {groupedColumns.parentColumns.map((parentCol) => {
            const childColumns =
              groupedColumns.columnMap.get(parentCol.id) || [];
            if (!childColumns.length) {
              // Make sure we're getting the cell value correctly
              const cellKey = `${parentCol.id}_${row.id}`;
              const cellValue = cellValues[cellKey] || "";
              const hasDefaultValue = cellValue.trim() !== "";

              return (
                <TableCell
                  key={`cell-${parentCol.id}-${row.id}`}
                  className="border p-1"
                >
                  {hasDefaultValue ? (
                    // Show default value as static text with normal input styling
                    <div className="w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900">
                      {cellValue}
                    </div>
                  ) : (
                    // Show input field for user entry
                    <Input
                      value={userInputValues[`${parentCol.id}_${row.id}`] || ""}
                      onChange={(e) =>
                        handleInputChange(parentCol.id, row.id, e.target.value)
                      }
                      placeholder="Enter value"
                      className="w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500"
                    />
                  )}
                </TableCell>
              );
            }
            return childColumns.map((childCol) => {
              const cellKey = `${childCol.id}_${row.id}`;
              const cellValue = cellValues[cellKey] || "";
              const hasDefaultValue = cellValue.trim() !== "";

              return (
                <TableCell
                  key={`cell-${childCol.id}-${row.id}`}
                  className="border p-1"
                >
                  {hasDefaultValue ? (
                    // Show default value as static text with normal input styling
                    <div className="w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900">
                      {cellValue}
                    </div>
                  ) : (
                    // Show input field for user entry
                    <Input
                      value={userInputValues[`${childCol.id}_${row.id}`] || ""}
                      onChange={(e) =>
                        handleInputChange(childCol.id, row.id, e.target.value)
                      }
                      placeholder="Enter value"
                      className="w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500"
                    />
                  )}
                </TableCell>
              );
            });
          })}
          <TableCell className="border p-1 w-10">
            <button
              type="button"
              className={`${
                rows.length <= 1
                  ? "text-gray-300 cursor-not-allowed"
                  : "text-red-500 hover:text-red-700"
              }`}
              onClick={() => handleRemoveRow(rowIndex)}
              disabled={rows.length <= 1}
              aria-label="Delete Row"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </TableCell>
        </UITableRow>
      ));
    }, [
      rows,
      cellValues,
      userInputValues,
      groupedColumns,
      required,
      handleInputChange,
      handleRemoveRow,
    ]);

    const hasNoColumns = !columns.length;

    return (
      <div className="overflow-x-auto">
        {loading ? (
          <div className="py-4 text-center">Loading table...</div>
        ) : error ? (
          <div className="py-4 text-center text-red-500">{error}</div>
        ) : hasNoColumns ? (
          <div className="py-4 text-center text-amber-600">
            This table has no columns defined. Please configure the table
            question first.
          </div>
        ) : (
          <>
            <Table className="border-collapse">
              <TableHeader>
                <UITableRow>
                  <TableHead
                    className="text-center border bg-blue-50 font-medium"
                    rowSpan={groupedColumns.hasChildColumns ? 2 : 1}
                  >
                    S.No.
                  </TableHead>
                  {groupedColumns.parentColumns.map((parentCol) => {
                    const childColumns =
                      groupedColumns.columnMap.get(parentCol.id) || [];
                    const colSpan = childColumns.length || 1;
                    return (
                      <TableHead
                        key={parentCol.id}
                        colSpan={colSpan}
                        className="text-center border bg-blue-50 font-medium"
                        rowSpan={childColumns.length === 0 ? 2 : 1}
                      >
                        {parentCol.columnName}
                      </TableHead>
                    );
                  })}
                </UITableRow>
                {groupedColumns.hasChildColumns && (
                  <UITableRow>
                    {groupedColumns.parentColumns.map((parentCol) => {
                      const childColumns =
                        groupedColumns.columnMap.get(parentCol.id) || [];
                      if (!childColumns.length) return null;
                      return childColumns.map((childCol) => (
                        <TableHead
                          key={childCol.id}
                          className="border bg-blue-50/50 text-sm"
                        >
                          {childCol.columnName}
                        </TableHead>
                      ));
                    })}
                  </UITableRow>
                )}
              </TableHeader>
              <TableBody>{tableBody}</TableBody>
            </Table>
            <div className="mt-2 flex justify-end">
              <button
                type="button"
                onClick={handleAddRow}
                className="border rounded px-3 py-1 text-sm flex items-center gap-1 hover:bg-blue-100"
              >
                <Plus className="h-4 w-4" /> Add Row
              </button>
            </div>
          </>
        )}
      </div>
    );
  }
);
