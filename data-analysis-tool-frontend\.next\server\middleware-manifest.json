{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_24e2925a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lUJu64f0BENfp9iP7T2/TsE5/VqtGlGCMzTGnjEa7hA=", "__NEXT_PREVIEW_MODE_ID": "e00833a3097b080902671c4acfaf7267", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "df777acf4cc232051ce00d42d3550c6f21bc0af8c47d9bac22db7e3ff9288563", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "43366d9ea07ace31cf225375d964551f41875c8ab3a1b2fc0bc7f9bf991311f6"}}}, "sortedMiddleware": ["/"], "functions": {}}