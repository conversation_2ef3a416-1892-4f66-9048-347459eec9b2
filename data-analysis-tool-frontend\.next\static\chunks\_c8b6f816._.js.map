{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/api/table.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\nexport interface TableColumn {\r\n  id: number;\r\n  columnName: string;\r\n  parentColumnId?: number;\r\n  childColumns?: TableColumn[];\r\n}\r\n\r\nexport interface DefaultValue {\r\n  columnId: number;\r\n  value: string;\r\n  code?: string;\r\n}\r\n\r\nexport interface TableRow {\r\n  id: number;\r\n  rowsName: string;\r\n  defaultValues?: DefaultValue[];\r\n}\r\n\r\nexport interface CellValue {\r\n  columnId: number;\r\n  rowsId: number;\r\n  value: string;\r\n  code?: string;\r\n  isDefault?: boolean;\r\n}\r\n\r\nexport interface TableQuestion {\r\n  id: number;\r\n  label: string;\r\n  inputType: string;\r\n  tableColumns: TableColumn[];\r\n  tableRows: TableRow[];\r\n}\r\n\r\n// Fetch table structure (columns and rows)\r\nexport const fetchTableStructure = async (questionId: number) => {\r\n  try {\r\n    console.log(`Fetching table structure for questionId: ${questionId}`);\r\n\r\n    if (!questionId || isNaN(questionId)) {\r\n      console.error(\"Invalid questionId:\", questionId);\r\n      throw new Error(\"Invalid question ID provided\");\r\n    }\r\n\r\n    // First try the table-questions endpoint\r\n    try {\r\n      const response = await axios.get(`/table-questions/${questionId}`);\r\n      console.log(\"Response from /table-questions/:\", response.data);\r\n\r\n      // Check if the response has the expected structure\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        console.log(\"Using question from data.data.question\");\r\n        // Include both question and cellValues in the response\r\n        const tableData = response.data.data.question;\r\n        const cellValues = response.data.data.cellValues || {};\r\n\r\n        // Merge cellValues into the table data for backward compatibility\r\n        return {\r\n          ...tableData,\r\n          cellValues: cellValues,\r\n        };\r\n      } else if (response.data && response.data.data) {\r\n        console.log(\"Using data from data.data\");\r\n        return response.data.data;\r\n      } else if (response.data && response.data.success) {\r\n        console.log(\"Using data from response.data\");\r\n        return response.data;\r\n      }\r\n    } catch (err) {\r\n      console.log(\"Error from /table-questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the questions endpoint\r\n    try {\r\n      const response = await axios.get(`/questions/${questionId}`);\r\n      console.log(\"Response from /questions/:\", response.data);\r\n\r\n      if (response.data && response.data.data) {\r\n        console.log(\"Using data from questions endpoint\");\r\n        return response.data.data;\r\n      }\r\n    } catch (err) {\r\n      console.log(\"Error from /questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the tables endpoint as a last resort\r\n    try {\r\n      const response = await axios.get(`/tables/${questionId}`);\r\n      console.log(\"Response from /tables/:\", response.data);\r\n\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        console.log(\"Using question from tables endpoint\");\r\n        return response.data.data.question;\r\n      }\r\n    } catch (err) {\r\n      console.log(\"Error from /tables/ endpoint:\", err);\r\n    }\r\n\r\n    // If all endpoints fail, throw an error\r\n    console.error(\"All endpoints failed to return valid data\");\r\n    throw new Error(\"Failed to fetch table structure from any endpoint\");\r\n  } catch (error) {\r\n    console.error(\"Error fetching table structure:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Save cell values\r\nexport const saveCellValues = async (\r\n  questionId: number,\r\n  cellValues: CellValue[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.post(`/table-questions/cells`, {\r\n      questionId,\r\n      cellValues,\r\n    });\r\n    return data.data;\r\n  } catch (error) {\r\n    console.error(\"Error saving cell values:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Create a new table\r\n// IMPORTANT: When specifying parentColumnId for new columns, you need to use position-based indices\r\n// (1-based) that reference the position of the parent column in the array.\r\n// For example, if column B is a child of column A, and column A is the first column in the array,\r\n// then column B's parentColumnId should be 1.\r\n// This is different from updateTable, which uses actual database IDs.\r\nexport const createTable = async (\r\n  label: string,\r\n  projectId: number,\r\n  columns: { columnName: string; parentColumnId?: number }[],\r\n  rows?: { rowsName: string; defaultValues?: DefaultValue[] }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!projectId || isNaN(projectId)) {\r\n      throw new Error(\"Valid project ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n    console.log(\"Columns for create:\", columns);\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => ({\r\n      columnName: col.columnName,\r\n      parentColumnId: col.parentColumnId,\r\n    }));\r\n\r\n    // Log the columns being sent to the backend\r\n    console.log(\"Cleaned columns for backend:\", cleanedColumns);\r\n\r\n    // Log the rearranged columns\r\n    console.log(\"Rearranged columns for backend:\", cleanedColumns);\r\n\r\n    // Ensure rows is always an array, even if empty\r\n    const processedRows = rows || [];\r\n\r\n    // Process rows to ensure default values are properly formatted\r\n    const cleanedRows = processedRows.map((row) => {\r\n      const cleanedRow: {\r\n        rowsName: string;\r\n        defaultValues: DefaultValue[];\r\n      } = {\r\n        rowsName: row.rowsName.trim(),\r\n        defaultValues: [],\r\n      };\r\n\r\n      // Process default values if they exist\r\n      if (row.defaultValues && row.defaultValues.length > 0) {\r\n        console.log(\r\n          `Processing ${row.defaultValues.length} default values for row ${row.rowsName}:`,\r\n          row.defaultValues\r\n        );\r\n\r\n        // Filter out invalid default values but keep all valid ones\r\n        cleanedRow.defaultValues = row.defaultValues\r\n          .filter((dv) => {\r\n            const isValid =\r\n              dv.columnId &&\r\n              typeof dv.columnId === \"number\" &&\r\n              dv.columnId > 0 &&\r\n              dv.value !== undefined &&\r\n              dv.value !== null &&\r\n              String(dv.value).trim() !== \"\";\r\n\r\n            if (!isValid) {\r\n              console.warn(`Filtering out invalid default value:`, dv);\r\n            }\r\n            return isValid;\r\n          })\r\n          .map((dv) => {\r\n            const mappedValue = {\r\n              columnId: dv.columnId,\r\n              value: String(dv.value).trim(),\r\n              code: dv.code || String(dv.value).trim(),\r\n            };\r\n            console.log(`Mapped default value:`, mappedValue);\r\n            return mappedValue;\r\n          });\r\n      }\r\n\r\n      console.log(\r\n        `Row ${row.rowsName} has ${cleanedRow.defaultValues.length} default values after cleaning:`,\r\n        cleanedRow.defaultValues\r\n      );\r\n      return cleanedRow;\r\n    });\r\n\r\n    console.log(\"Creating table with data:\", {\r\n      label,\r\n      projectId,\r\n      columns: cleanedColumns,\r\n      rows: cleanedRows,\r\n    });\r\n\r\n    // Log the exact structure of rows with default values for debugging\r\n    console.log(\"Rows with default values:\");\r\n    cleanedRows.forEach((row, index) => {\r\n      console.log(`Row ${index + 1} (${row.rowsName}):`);\r\n      console.log(`  Default values: ${row.defaultValues.length}`);\r\n      row.defaultValues.forEach((dv, i) => {\r\n        console.log(\r\n          `    ${i + 1}. columnId: ${dv.columnId}, value: \"${\r\n            dv.value\r\n          }\", code: \"${dv.code || dv.value}\"`\r\n        );\r\n      });\r\n    });\r\n\r\n    // Use the table-questions endpoint which creates both a question and table structure\r\n    // Note: The axios instance is configured with baseURL that includes /api, so we don't need to add it here\r\n    const { data } = await axios.post(`/table-questions`, {\r\n      label,\r\n      projectId,\r\n      columns: cleanedColumns,\r\n      rows: cleanedRows, // Use the cleaned rows\r\n    });\r\n\r\n    console.log(\"Table created successfully:\", data);\r\n\r\n    if (!data || !data.success) {\r\n      throw new Error(data?.message || \"Failed to create table\");\r\n    }\r\n\r\n    return data.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error creating table:\", error);\r\n\r\n    // Enhance error message with response details if available\r\n    if (error.response) {\r\n      console.error(\"Response status:\", error.response.status);\r\n      console.error(\"Response data:\", error.response.data);\r\n\r\n      // If we have a more specific error message from the server, use it\r\n      if (error.response.data && error.response.data.message) {\r\n        error.message = error.response.data.message;\r\n      }\r\n    }\r\n\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete a table\r\nexport const deleteTable = async (tableId: number) => {\r\n  try {\r\n    const { data } = await axios.delete(`/table-questions/${tableId}`);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting table:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update an existing table\r\n// IMPORTANT: When specifying parentColumnId for existing columns, use the actual database ID of the parent column.\r\n// For new columns (without an ID), use the position (1-based index) of the parent column in the array.\r\n// For example:\r\n// - If column B is a child of existing column A with ID 123, then column B's parentColumnId should be 123.\r\n// - If column B is a child of new column A at position 1 in the array, then column B's parentColumnId should be 1.\r\nexport const updateTable = async (\r\n  tableId: number,\r\n  label: string,\r\n  columns: { id?: number; columnName: string; parentColumnId?: number }[],\r\n  rows?: { id?: number; rowsName: string; defaultValues?: DefaultValue[] }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!tableId || isNaN(tableId)) {\r\n      throw new Error(\"Valid table ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // Validate parent-child relationships\r\n    // Check for circular references or invalid parent IDs\r\n    const columnIdMap = new Map();\r\n    const columnPositionMap = new Map();\r\n\r\n    // Map columns by ID and position\r\n    columns.forEach((col, index) => {\r\n      if (col.id) {\r\n        columnIdMap.set(col.id, col);\r\n      }\r\n      // Store 1-based position\r\n      columnPositionMap.set(index + 1, col);\r\n    });\r\n\r\n    // Check each column with a parent\r\n    for (const col of columns) {\r\n      if (col.parentColumnId) {\r\n        // Ensure parentColumnId is a positive number\r\n        if (col.parentColumnId <= 0) {\r\n          throw new Error(\r\n            `Invalid parent column ID: ${col.parentColumnId}. Must be a positive number.`\r\n          );\r\n        }\r\n\r\n        // Try to find parent by ID first\r\n        let parentCol = columns.find((c) => c.id === col.parentColumnId);\r\n\r\n        // If not found by ID, try to find by position (for new columns)\r\n        if (!parentCol && col.parentColumnId <= columns.length) {\r\n          parentCol = columnPositionMap.get(col.parentColumnId);\r\n          console.log(\r\n            `Found parent by position ${col.parentColumnId}: ${parentCol?.columnName}`\r\n          );\r\n        }\r\n\r\n        // If we still can't find the parent, it's an error\r\n        if (!parentCol) {\r\n          throw new Error(\r\n            `Parent column with ID/position ${col.parentColumnId} not found in the columns array.`\r\n          );\r\n        }\r\n\r\n        // Check for circular references\r\n        // If this column has a parent, and that parent also has a parent,\r\n        // it would create a 3rd level, which we don't support\r\n        if (parentCol.parentColumnId) {\r\n          throw new Error(\r\n            \"Cannot create more than 2 levels of nested columns (parent → child → grandchild)\"\r\n          );\r\n        }\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n    console.log(\"Columns for update:\", columns);\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      id?: number;\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => {\r\n      const cleanCol: {\r\n        id?: number;\r\n        columnName: string;\r\n        parentColumnId?: number;\r\n      } = {\r\n        columnName: col.columnName.trim(),\r\n      };\r\n\r\n      if (col.id) {\r\n        cleanCol.id = col.id;\r\n      }\r\n\r\n      if (col.parentColumnId !== undefined) {\r\n        cleanCol.parentColumnId = col.parentColumnId;\r\n      }\r\n\r\n      return cleanCol;\r\n    });\r\n\r\n    // Log the columns being sent to the backend\r\n    console.log(\"Cleaned columns for backend:\", cleanedColumns);\r\n\r\n    console.log(\"Updating table with data:\", {\r\n      tableId,\r\n      label,\r\n      columns: cleanedColumns,\r\n      rows,\r\n    });\r\n\r\n    // Use the table-questions endpoint to update the table\r\n    try {\r\n      const { data } = await axios.patch(`/table-questions/${tableId}`, {\r\n        label: label.trim(),\r\n        columns: cleanedColumns,\r\n        rows: rows\r\n          ? rows.map((row) => ({\r\n              ...row,\r\n              rowsName: row.rowsName.trim(),\r\n            }))\r\n          : [],\r\n      });\r\n\r\n      console.log(\"Table updated successfully:\", data);\r\n\r\n      if (!data || !data.success) {\r\n        throw new Error(data?.message || \"Failed to update table\");\r\n      }\r\n\r\n      return data.data;\r\n    } catch (apiError: any) {\r\n      console.error(\"API error updating table:\", apiError);\r\n\r\n      // Enhance error message with response details if available\r\n      if (apiError.response) {\r\n        console.error(\"Response status:\", apiError.response.status);\r\n        console.error(\"Response data:\", apiError.response.data);\r\n\r\n        // If we have a more specific error message from the server, use it\r\n        if (apiError.response.data && apiError.response.data.message) {\r\n          throw new Error(apiError.response.data.message);\r\n        }\r\n      }\r\n\r\n      // If we don't have a specific error message, throw the original error\r\n      throw apiError;\r\n    }\r\n  } catch (error: any) {\r\n    console.error(\"Error updating table:\", error);\r\n\r\n    // Rethrow the error with a clear message\r\n    if (error.message) {\r\n      throw new Error(`Failed to update table: ${error.message}`);\r\n    } else {\r\n      throw new Error(\"Failed to update table due to an unknown error\");\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAsCO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,YAAY;QAEpE,IAAI,CAAC,cAAc,MAAM,aAAa;YACpC,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM;QAClB;QAEA,yCAAyC;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,YAAY;YACjE,QAAQ,GAAG,CAAC,oCAAoC,SAAS,IAAI;YAE7D,mDAAmD;YACnD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,QAAQ,GAAG,CAAC;gBACZ,uDAAuD;gBACvD,MAAM,YAAY,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;gBAC7C,MAAM,aAAa,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC;gBAErD,kEAAkE;gBAClE,OAAO;oBACL,GAAG,SAAS;oBACZ,YAAY;gBACd;YACF,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,QAAQ,GAAG,CAAC;gBACZ,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACjD,QAAQ,GAAG,CAAC;gBACZ,OAAO,SAAS,IAAI;YACtB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,0CAA0C;QACtD,oCAAoC;QACtC;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY;YAC3D,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YAEvD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,QAAQ,GAAG,CAAC;gBACZ,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,oCAAoC;QAChD,oCAAoC;QACtC;QAEA,0DAA0D;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY;YACxD,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;YAEpD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,QAAQ,GAAG,CAAC;gBACZ,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,iCAAiC;QAC/C;QAEA,wCAAwC;QACxC,QAAQ,KAAK,CAAC;QACd,MAAM,IAAI,MAAM;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAC5B,YACA;IAEA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,EAAE;YAC1D;YACA;QACF;QACA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,OACA,WACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,aAAa,MAAM,YAAY;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QACnD,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,+DAA+D;QAC/D,MAAM,iBAGA,QAAQ,GAAG,CAAC,CAAC,MAAQ,CAAC;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,gBAAgB,IAAI,cAAc;YACpC,CAAC;QAED,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,6BAA6B;QAC7B,QAAQ,GAAG,CAAC,mCAAmC;QAE/C,gDAAgD;QAChD,MAAM,gBAAgB,QAAQ,EAAE;QAEhC,+DAA+D;QAC/D,MAAM,cAAc,cAAc,GAAG,CAAC,CAAC;YACrC,MAAM,aAGF;gBACF,UAAU,IAAI,QAAQ,CAAC,IAAI;gBAC3B,eAAe,EAAE;YACnB;YAEA,uCAAuC;YACvC,IAAI,IAAI,aAAa,IAAI,IAAI,aAAa,CAAC,MAAM,GAAG,GAAG;gBACrD,QAAQ,GAAG,CACT,CAAC,WAAW,EAAE,IAAI,aAAa,CAAC,MAAM,CAAC,wBAAwB,EAAE,IAAI,QAAQ,CAAC,CAAC,CAAC,EAChF,IAAI,aAAa;gBAGnB,4DAA4D;gBAC5D,WAAW,aAAa,GAAG,IAAI,aAAa,CACzC,MAAM,CAAC,CAAC;oBACP,MAAM,UACJ,GAAG,QAAQ,IACX,OAAO,GAAG,QAAQ,KAAK,YACvB,GAAG,QAAQ,GAAG,KACd,GAAG,KAAK,KAAK,aACb,GAAG,KAAK,KAAK,QACb,OAAO,GAAG,KAAK,EAAE,IAAI,OAAO;oBAE9B,IAAI,CAAC,SAAS;wBACZ,QAAQ,IAAI,CAAC,CAAC,oCAAoC,CAAC,EAAE;oBACvD;oBACA,OAAO;gBACT,GACC,GAAG,CAAC,CAAC;oBACJ,MAAM,cAAc;wBAClB,UAAU,GAAG,QAAQ;wBACrB,OAAO,OAAO,GAAG,KAAK,EAAE,IAAI;wBAC5B,MAAM,GAAG,IAAI,IAAI,OAAO,GAAG,KAAK,EAAE,IAAI;oBACxC;oBACA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,CAAC,EAAE;oBACrC,OAAO;gBACT;YACJ;YAEA,QAAQ,GAAG,CACT,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,KAAK,EAAE,WAAW,aAAa,CAAC,MAAM,CAAC,+BAA+B,CAAC,EAC3F,WAAW,aAAa;YAE1B,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,6BAA6B;YACvC;YACA;YACA,SAAS;YACT,MAAM;QACR;QAEA,oEAAoE;QACpE,QAAQ,GAAG,CAAC;QACZ,YAAY,OAAO,CAAC,CAAC,KAAK;YACxB,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC;YACjD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,aAAa,CAAC,MAAM,EAAE;YAC3D,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI;gBAC7B,QAAQ,GAAG,CACT,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,QAAQ,CAAC,UAAU,EAC/C,GAAG,KAAK,CACT,UAAU,EAAE,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;YAEvC;QACF;QAEA,qFAAqF;QACrF,0GAA0G;QAC1G,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACpD;YACA;YACA,SAAS;YACT,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,+BAA+B;QAE3C,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;YAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;QACnC;QAEA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,2DAA2D;QAC3D,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;YACvD,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YAEnD,mEAAmE;YACnE,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACtD,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C;QACF;QAEA,MAAM;IACR;AACF;AAGO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,SAAS;QACjE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,SACA,OACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,MAAM,UAAU;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,sCAAsC;QACtC,sDAAsD;QACtD,MAAM,cAAc,IAAI;QACxB,MAAM,oBAAoB,IAAI;QAE9B,iCAAiC;QACjC,QAAQ,OAAO,CAAC,CAAC,KAAK;YACpB,IAAI,IAAI,EAAE,EAAE;gBACV,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;YAC1B;YACA,yBAAyB;YACzB,kBAAkB,GAAG,CAAC,QAAQ,GAAG;QACnC;QAEA,kCAAkC;QAClC,KAAK,MAAM,OAAO,QAAS;YACzB,IAAI,IAAI,cAAc,EAAE;gBACtB,6CAA6C;gBAC7C,IAAI,IAAI,cAAc,IAAI,GAAG;oBAC3B,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,IAAI,cAAc,CAAC,4BAA4B,CAAC;gBAEjF;gBAEA,iCAAiC;gBACjC,IAAI,YAAY,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,cAAc;gBAE/D,gEAAgE;gBAChE,IAAI,CAAC,aAAa,IAAI,cAAc,IAAI,QAAQ,MAAM,EAAE;oBACtD,YAAY,kBAAkB,GAAG,CAAC,IAAI,cAAc;oBACpD,QAAQ,GAAG,CACT,CAAC,yBAAyB,EAAE,IAAI,cAAc,CAAC,EAAE,EAAE,WAAW,YAAY;gBAE9E;gBAEA,mDAAmD;gBACnD,IAAI,CAAC,WAAW;oBACd,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,IAAI,cAAc,CAAC,gCAAgC,CAAC;gBAE1F;gBAEA,gCAAgC;gBAChC,kEAAkE;gBAClE,sDAAsD;gBACtD,IAAI,UAAU,cAAc,EAAE;oBAC5B,MAAM,IAAI,MACR;gBAEJ;YACF;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QACnD,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,+DAA+D;QAC/D,MAAM,iBAIA,QAAQ,GAAG,CAAC,CAAC;YACjB,MAAM,WAIF;gBACF,YAAY,IAAI,UAAU,CAAC,IAAI;YACjC;YAEA,IAAI,IAAI,EAAE,EAAE;gBACV,SAAS,EAAE,GAAG,IAAI,EAAE;YACtB;YAEA,IAAI,IAAI,cAAc,KAAK,WAAW;gBACpC,SAAS,cAAc,GAAG,IAAI,cAAc;YAC9C;YAEA,OAAO;QACT;QAEA,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,QAAQ,GAAG,CAAC,6BAA6B;YACvC;YACA;YACA,SAAS;YACT;QACF;QAEA,uDAAuD;QACvD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE;gBAChE,OAAO,MAAM,IAAI;gBACjB,SAAS;gBACT,MAAM,OACF,KAAK,GAAG,CAAC,CAAC,MAAQ,CAAC;wBACjB,GAAG,GAAG;wBACN,UAAU,IAAI,QAAQ,CAAC,IAAI;oBAC7B,CAAC,KACD,EAAE;YACR;YAEA,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;gBAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;YACnC;YAEA,OAAO,KAAK,IAAI;QAClB,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,2DAA2D;YAC3D,IAAI,SAAS,QAAQ,EAAE;gBACrB,QAAQ,KAAK,CAAC,oBAAoB,SAAS,QAAQ,CAAC,MAAM;gBAC1D,QAAQ,KAAK,CAAC,kBAAkB,SAAS,QAAQ,CAAC,IAAI;gBAEtD,mEAAmE;gBACnE,IAAI,SAAS,QAAQ,CAAC,IAAI,IAAI,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBAC5D,MAAM,IAAI,MAAM,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAChD;YACF;YAEA,sEAAsE;YACtE,MAAM;QACR;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,yCAAyC;QACzC,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;QAC5D,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-inputs/TableInput.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, {\n  useState,\n  useEffect,\n  use<PERSON><PERSON>back,\n  useMemo,\n  useRef,\n} from \"react\";\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow as UITableRow,\n} from \"@/components/ui/table\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n  fetchTableStructure,\n  TableRow as TableRowType,\n  CellValue,\n  DefaultValue,\n} from \"../../lib/api/table\";\nimport { Plus, Trash2 } from \"lucide-react\";\nimport debounce from \"lodash/debounce\";\nimport { useQuery } from \"@tanstack/react-query\";\n\n// Define TableColumn interface to ensure childColumns is optional\ninterface TableColumn {\n  id: number;\n  columnName: string;\n  parentColumnId?: number | null;\n  childColumns?: TableColumn[];\n}\n\ninterface TableInputProps {\n  questionId: number;\n  value: string | CellValue[];\n  onChange: (value: CellValue[]) => void;\n  required?: boolean;\n}\n\n// Memoize the TableInput component to prevent unnecessary re-renders\nexport const TableInput = React.memo(\n  ({ questionId, value, onChange, required = false }: TableInputProps) => {\n    // State declarations\n    const [columns, setColumns] = useState<TableColumn[]>([]);\n    const [rows, setRows] = useState<TableRowType[]>([]);\n    const [cellValues, setCellValues] = useState<Record<string, string>>({});\n    const [userInputValues, setUserInputValues] = useState<\n      Record<string, string>\n    >({});\n\n    // Ref to track cellValues for responsive input\n    const cellValuesRef = useRef<Record<string, string>>(cellValues);\n\n    // Update ref whenever cellValues changes\n    useEffect(() => {\n      cellValuesRef.current = cellValues;\n    }, [cellValues]);\n\n    // Process columns to create a flat structure with parent-child relationships\n    const processColumns = useCallback(\n      (tableData: { tableColumns?: TableColumn[] }): TableColumn[] => {\n        if (!tableData?.tableColumns) return [];\n\n        const flattenedColumns: TableColumn[] = [];\n        const parentColumns = tableData.tableColumns.filter(\n          (col: TableColumn) =>\n            col.parentColumnId === null || col.parentColumnId === undefined\n        );\n\n        parentColumns.forEach((parentCol: TableColumn) => {\n          flattenedColumns.push(parentCol);\n          const childColumns = parentCol.childColumns;\n          if (\n            childColumns &&\n            Array.isArray(childColumns) &&\n            childColumns.length > 0\n          ) {\n            childColumns.forEach((childCol: TableColumn) => {\n              flattenedColumns.push({\n                id: childCol.id,\n                columnName: childCol.columnName,\n                parentColumnId: childCol.parentColumnId,\n              });\n            });\n          }\n        });\n\n        return flattenedColumns;\n      },\n      []\n    );\n\n    // Memoize grouped columns to avoid recomputation on every render\n    const groupedColumns = useMemo(() => {\n      if (!columns.length) {\n        return {\n          parentColumns: [],\n          columnMap: new Map<number, TableColumn[]>(),\n          hasChildColumns: false,\n        };\n      }\n\n      const parentColumns = columns.filter((col) => !col.parentColumnId);\n      const columnMap = new Map<number, TableColumn[]>();\n\n      parentColumns.forEach((parentCol) => {\n        const childColumns = columns.filter(\n          (col) => col.parentColumnId === parentCol.id\n        );\n        columnMap.set(parentCol.id, childColumns);\n      });\n\n      const hasChildColumns = parentColumns.some(\n        (p) => (columnMap.get(p.id) ?? []).length > 0\n      );\n\n      return { parentColumns, columnMap, hasChildColumns };\n    }, [columns]);\n\n    // Use React Query to fetch table structure with automatic refetching\n    const {\n      data: tableData,\n      isLoading: loading,\n      error: queryError,\n    } = useQuery({\n      queryKey: [\"tableStructure\", questionId],\n      queryFn: () => fetchTableStructure(questionId),\n      enabled: questionId > 0,\n      staleTime: 0, // Always consider data stale to ensure fresh fetches\n      gcTime: 0, // Don't cache the data\n    });\n\n    // Set error state based on query error\n    const error = queryError ? \"Failed to load table structure\" : null;\n\n    // Process table data when it changes\n    useEffect(() => {\n      if (!tableData) {\n        setColumns([]);\n        setRows([]);\n        setCellValues({});\n        return;\n      }\n\n      console.log(\"Processing table data:\", tableData);\n      setColumns(processColumns(tableData));\n      setRows(tableData.tableRows || []);\n\n      // Process default values from rows and cell values\n      const defaultCellValues: Record<string, string> = {};\n      const defaultCellKeys: string[] = [];\n\n      // Define the type for cell values\n      interface CellValueWithDefault {\n        value: string;\n        isDefault: boolean;\n      }\n\n      // Check if we have cell values in the response\n      if (tableData.cellValues) {\n        console.log(\"Cell values found in response:\", tableData.cellValues);\n        // Process cell values from the response\n        (\n          Object.entries(tableData.cellValues) as [\n            string,\n            string | CellValueWithDefault\n          ][]\n        ).forEach(([key, value]) => {\n          if (typeof value === \"object\" && value !== null) {\n            // Handle object format with value property\n            const cellValue = value.value || \"\";\n            if (cellValue.trim() !== \"\") {\n              defaultCellValues[key] = cellValue;\n              defaultCellKeys.push(key);\n              console.log(\n                `Loading cell value from object for ${key}: ${cellValue} (isDefault: ${value.isDefault})`\n              );\n            }\n          } else if (typeof value === \"string\" && value.trim() !== \"\") {\n            // Handle string format\n            defaultCellValues[key] = value;\n            defaultCellKeys.push(key);\n            console.log(`Loading cell value from string for ${key}: ${value}`);\n          }\n        });\n      }\n\n      // Also check if tableRows have defaultValues (for backward compatibility)\n      if (tableData.tableRows && tableData.tableRows.length > 0) {\n        tableData.tableRows.forEach((row: TableRowType) => {\n          // Check if defaultValues exists on the row\n          // In some API responses, rows might not have the defaultValues property\n          if (\n            row.defaultValues &&\n            Array.isArray(row.defaultValues) &&\n            row.defaultValues.length > 0\n          ) {\n            row.defaultValues.forEach((dv: DefaultValue) => {\n              const key = `${dv.columnId}_${row.id}`;\n              defaultCellValues[key] = dv.value;\n              defaultCellKeys.push(key);\n              console.log(\n                `Loading default value from row for ${key}: ${dv.value}`\n              );\n            });\n          } else {\n            console.log(\n              `Row ${row.id} has no defaultValues property or it's empty`\n            );\n          }\n        });\n      }\n\n      // Set default values in the cellValues state\n      if (Object.keys(defaultCellValues).length > 0) {\n        console.log(\"Setting cell values:\", defaultCellValues);\n        console.log(\"Default cell keys:\", defaultCellKeys);\n\n        // Force a re-render by creating a new object reference\n        const newCellValues = { ...defaultCellValues };\n\n        // Log each cell value for debugging\n        Object.entries(newCellValues).forEach(([key, value]) => {\n          console.log(`Setting cell value for ${key}: ${value}`);\n        });\n\n        // Set the cell values directly without merging with previous state\n        setCellValues(newCellValues);\n\n        // Wait a moment and then set the values again to ensure they're applied\n        setTimeout(() => {\n          console.log(\"Re-applying cell values to ensure they are set\");\n          setCellValues((prevValues) => ({ ...prevValues }));\n        }, 100);\n      } else {\n        console.warn(\"No default or cell values found for this table\");\n        setCellValues({});\n      }\n    }, [tableData, processColumns]);\n\n    // Handle initial value parsing and form reset\n    useEffect(() => {\n      if (loading) return;\n\n      const initialCellValues: Record<string, string> = {};\n      let cellData: CellValue[] = [];\n\n      if (typeof value === \"string\" && value.trim()) {\n        try {\n          cellData = JSON.parse(value);\n        } catch {\n          cellData = [];\n        }\n      } else if (Array.isArray(value)) {\n        cellData = value;\n      }\n\n      cellData.forEach((cell) => {\n        initialCellValues[`${cell.columnId}_${cell.rowsId}`] = cell.value;\n      });\n\n      const isFormReset = !value || (Array.isArray(value) && !value.length);\n\n      if (isFormReset) {\n        setUserInputValues({});\n      } else if (Object.keys(initialCellValues).length) {\n        setUserInputValues((prev) => ({ ...prev, ...initialCellValues }));\n      }\n    }, [value, loading]);\n\n    // Effect to ensure default values are always included in form submission\n    useEffect(() => {\n      if (!loading && Object.keys(cellValues).length > 0) {\n        // Combine default values and user input values\n        const allValues = { ...cellValues, ...userInputValues };\n\n        const updatedCellValues: CellValue[] = Object.entries(allValues)\n          .filter(([, value]) => value.trim())\n          .map(([key, value]) => {\n            const [colId, rowId] = key.split(\"_\").map(Number);\n            return { columnId: colId, rowsId: rowId, value };\n          });\n\n        // Only update if there are values to submit\n        if (updatedCellValues.length > 0) {\n          onChange(updatedCellValues);\n        }\n      }\n    }, [cellValues, userInputValues, loading, onChange]);\n\n    // Debounced cell change handler to reduce rapid state updates\n    const handleCellChange = useCallback(\n      debounce(\n        (columnId: number, rowId: number | string, newValue: string) => {\n          // Always combine default values and user input values for submission\n          const allValues = { ...cellValues, ...userInputValues };\n          allValues[`${columnId}_${rowId}`] = newValue;\n\n          const updatedCellValues: CellValue[] = Object.entries(allValues)\n            .filter(([, value]) => value.trim())\n            .map(([key, value]) => {\n              const [colId, rowId] = key.split(\"_\").map(Number);\n              return { columnId: colId, rowsId: rowId, value };\n            });\n\n          onChange(updatedCellValues);\n        },\n        300,\n        { leading: false, trailing: true }\n      ),\n      [onChange, cellValues, userInputValues]\n    );\n\n    // Handle input change with immediate local state update for responsiveness\n    const handleInputChange = useCallback(\n      (columnId: number, rowId: number | string, newValue: string) => {\n        // Update user input values (separate from default values)\n        setUserInputValues((prev) => ({\n          ...prev,\n          [`${columnId}_${rowId}`]: newValue,\n        }));\n        // Trigger debounced update\n        handleCellChange(columnId, rowId, newValue);\n      },\n      [handleCellChange]\n    );\n\n    // Cleanup debounce on unmount\n    useEffect(() => {\n      return () => {\n        handleCellChange.cancel();\n      };\n    }, [handleCellChange]);\n\n    // Add a new row\n    const handleAddRow = useCallback((e: React.MouseEvent) => {\n      e.preventDefault();\n      e.stopPropagation();\n\n      setRows((prevRows) => [\n        ...prevRows,\n        {\n          id: -(prevRows.length + 1),\n          rowsName: (prevRows.length + 1).toString(),\n        },\n      ]);\n    }, []);\n\n    // Remove a row and clean up cell values\n    const handleRemoveRow = useCallback(\n      (rowIndex: number) => {\n        if (rows.length <= 1) return;\n\n        const rowToRemove = rows[rowIndex];\n\n        // Don't allow deletion of rows that came from the table question builder (positive IDs)\n        // Only allow deletion of dynamically added rows (negative IDs)\n        if (rowToRemove.id > 0) {\n          console.log(\n            `Cannot delete row ${rowToRemove.id} - it's from the table question builder`\n          );\n          return;\n        }\n\n        setRows((prevRows) =>\n          prevRows\n            .filter((_, index) => index !== rowIndex)\n            .map((row, idx) => ({\n              ...row,\n              rowsName: (idx + 1).toString(),\n            }))\n        );\n\n        setUserInputValues((prevValues) => {\n          const newValues = { ...prevValues };\n          Object.keys(newValues).forEach((key) => {\n            const [_, rowId] = key.split(\"_\");\n            if (rowId === rowToRemove.id.toString()) {\n              delete newValues[key];\n            }\n          });\n\n          // Combine with default values for submission\n          const allValues = { ...cellValues, ...newValues };\n          const updatedCellValues: CellValue[] = Object.entries(allValues)\n            .filter(([, value]) => value.trim())\n            .map(([key, value]) => {\n              const [colId, rowId] = key.split(\"_\").map(Number);\n              return { columnId: colId, rowsId: rowId, value };\n            });\n\n          onChange(updatedCellValues);\n          return newValues;\n        });\n      },\n      [rows, onChange, cellValues]\n    );\n\n    // Memoize the table body to prevent re-rendering unchanged rows\n    // Note: For large tables (>50 rows), consider using react-window for virtualization:\n    // import { FixedSizeList } from \"react-window\";\n    // <FixedSizeList height={400} itemCount={rows.length} itemSize={48} width=\"100%\">\n    //   {({ index, style }) => <Row index={index} style={style} />}\n    // </FixedSizeList>\n    const tableBody = useMemo(() => {\n      if (!rows.length) {\n        return (\n          <UITableRow>\n            <TableCell className=\"border p-1 text-center font-medium bg-blue-50/50\">\n              Row 1\n            </TableCell>\n            {groupedColumns.parentColumns.map((parentCol) => {\n              const childColumns =\n                groupedColumns.columnMap.get(parentCol.id) || [];\n              if (!childColumns.length) {\n                const cellValue = cellValues[`${parentCol.id}_no_row`] || \"\";\n                const hasDefaultValue = cellValue.trim() !== \"\";\n\n                return (\n                  <TableCell\n                    key={`cell-${parentCol.id}-no-row`}\n                    className=\"border p-1\"\n                  >\n                    {hasDefaultValue ? (\n                      // Show default value as static text with normal input styling\n                      <div className=\"w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900\">\n                        {cellValue}\n                      </div>\n                    ) : (\n                      // Show input field for user entry\n                      <Input\n                        value={userInputValues[`${parentCol.id}_no_row`] || \"\"}\n                        onChange={(e) =>\n                          handleInputChange(\n                            parentCol.id,\n                            \"no_row\",\n                            e.target.value\n                          )\n                        }\n                        placeholder=\"Enter value\"\n                        className=\"w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500\"\n                      />\n                    )}\n                  </TableCell>\n                );\n              }\n              return childColumns.map((childCol) => {\n                const cellValue = cellValues[`${childCol.id}_no_row`] || \"\";\n                const hasDefaultValue = cellValue.trim() !== \"\";\n\n                return (\n                  <TableCell\n                    key={`cell-${childCol.id}-no-row`}\n                    className=\"border p-1\"\n                  >\n                    {hasDefaultValue ? (\n                      // Show default value as static text with normal input styling\n                      <div className=\"w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900\">\n                        {cellValue}\n                      </div>\n                    ) : (\n                      // Show input field for user entry\n                      <Input\n                        value={userInputValues[`${childCol.id}_no_row`] || \"\"}\n                        onChange={(e) =>\n                          handleInputChange(\n                            childCol.id,\n                            \"no_row\",\n                            e.target.value\n                          )\n                        }\n                        placeholder=\"Enter value\"\n                        className=\"w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500\"\n                      />\n                    )}\n                  </TableCell>\n                );\n              });\n            })}\n            <TableCell className=\"border p-1 w-10\"></TableCell>\n          </UITableRow>\n        );\n      }\n\n      return rows.map((row, rowIndex) => (\n        <UITableRow\n          key={row.id}\n          className={rowIndex % 2 === 0 ? \"bg-white\" : \"bg-gray-50\"}\n        >\n          <TableCell className=\"border p-1 text-center font-medium bg-blue-50/50\">\n            {rowIndex + 1}\n          </TableCell>\n          {groupedColumns.parentColumns.map((parentCol) => {\n            const childColumns =\n              groupedColumns.columnMap.get(parentCol.id) || [];\n            if (!childColumns.length) {\n              // Make sure we're getting the cell value correctly\n              const cellKey = `${parentCol.id}_${row.id}`;\n              const cellValue = cellValues[cellKey] || \"\";\n              const hasDefaultValue = cellValue.trim() !== \"\";\n\n              return (\n                <TableCell\n                  key={`cell-${parentCol.id}-${row.id}`}\n                  className=\"border p-1\"\n                >\n                  {hasDefaultValue ? (\n                    // Show default value as static text with normal input styling\n                    <div className=\"w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900\">\n                      {cellValue}\n                    </div>\n                  ) : (\n                    // Show input field for user entry\n                    <Input\n                      value={userInputValues[`${parentCol.id}_${row.id}`] || \"\"}\n                      onChange={(e) =>\n                        handleInputChange(parentCol.id, row.id, e.target.value)\n                      }\n                      placeholder=\"Enter value\"\n                      className=\"w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500\"\n                    />\n                  )}\n                </TableCell>\n              );\n            }\n            return childColumns.map((childCol) => {\n              const cellKey = `${childCol.id}_${row.id}`;\n              const cellValue = cellValues[cellKey] || \"\";\n              const hasDefaultValue = cellValue.trim() !== \"\";\n\n              return (\n                <TableCell\n                  key={`cell-${childCol.id}-${row.id}`}\n                  className=\"border p-1\"\n                >\n                  {hasDefaultValue ? (\n                    // Show default value as static text with normal input styling\n                    <div className=\"w-full border-0 bg-transparent p-2 min-h-[38px] text-gray-900\">\n                      {cellValue}\n                    </div>\n                  ) : (\n                    // Show input field for user entry\n                    <Input\n                      value={userInputValues[`${childCol.id}_${row.id}`] || \"\"}\n                      onChange={(e) =>\n                        handleInputChange(childCol.id, row.id, e.target.value)\n                      }\n                      placeholder=\"Enter value\"\n                      className=\"w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500\"\n                    />\n                  )}\n                </TableCell>\n              );\n            });\n          })}\n          <TableCell className=\"border p-1 w-10\">\n            <button\n              type=\"button\"\n              className={`${\n                rows.length <= 1 || row.id > 0\n                  ? \"text-gray-300 cursor-not-allowed\"\n                  : \"text-red-500 hover:text-red-700\"\n              }`}\n              onClick={() => handleRemoveRow(rowIndex)}\n              disabled={rows.length <= 1 || row.id > 0}\n              aria-label={\n                row.id > 0 ? \"Cannot delete table question row\" : \"Delete Row\"\n              }\n              title={\n                row.id > 0\n                  ? \"This row is from the table question and cannot be deleted\"\n                  : \"Delete this row\"\n              }\n            >\n              <Trash2 className=\"w-4 h-4\" />\n            </button>\n          </TableCell>\n        </UITableRow>\n      ));\n    }, [\n      rows,\n      cellValues,\n      userInputValues,\n      groupedColumns,\n      required,\n      handleInputChange,\n      handleRemoveRow,\n    ]);\n\n    const hasNoColumns = !columns.length;\n\n    return (\n      <div className=\"overflow-x-auto\">\n        {loading ? (\n          <div className=\"py-4 text-center\">Loading table...</div>\n        ) : error ? (\n          <div className=\"py-4 text-center text-red-500\">{error}</div>\n        ) : hasNoColumns ? (\n          <div className=\"py-4 text-center text-amber-600\">\n            This table has no columns defined. Please configure the table\n            question first.\n          </div>\n        ) : (\n          <>\n            <Table className=\"border-collapse\">\n              <TableHeader>\n                <UITableRow>\n                  <TableHead\n                    className=\"text-center border bg-blue-50 font-medium\"\n                    rowSpan={groupedColumns.hasChildColumns ? 2 : 1}\n                  >\n                    S.No.\n                  </TableHead>\n                  {groupedColumns.parentColumns.map((parentCol) => {\n                    const childColumns =\n                      groupedColumns.columnMap.get(parentCol.id) || [];\n                    const colSpan = childColumns.length || 1;\n                    return (\n                      <TableHead\n                        key={parentCol.id}\n                        colSpan={colSpan}\n                        className=\"text-center border bg-blue-50 font-medium\"\n                        rowSpan={childColumns.length === 0 ? 2 : 1}\n                      >\n                        {parentCol.columnName}\n                      </TableHead>\n                    );\n                  })}\n                </UITableRow>\n                {groupedColumns.hasChildColumns && (\n                  <UITableRow>\n                    {groupedColumns.parentColumns.map((parentCol) => {\n                      const childColumns =\n                        groupedColumns.columnMap.get(parentCol.id) || [];\n                      if (!childColumns.length) return null;\n                      return childColumns.map((childCol) => (\n                        <TableHead\n                          key={childCol.id}\n                          className=\"border bg-blue-50/50 text-sm\"\n                        >\n                          {childCol.columnName}\n                        </TableHead>\n                      ));\n                    })}\n                  </UITableRow>\n                )}\n              </TableHeader>\n              <TableBody>{tableBody}</TableBody>\n            </Table>\n            <div className=\"mt-2 flex justify-end\">\n              <button\n                type=\"button\"\n                onClick={handleAddRow}\n                className=\"border rounded px-3 py-1 text-sm flex items-center gap-1 hover:bg-blue-100\"\n              >\n                <Plus className=\"h-4 w-4\" /> Add Row\n              </button>\n            </div>\n          </>\n        )}\n      </div>\n    );\n  }\n);\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AAQA;AACA;AAMA;AAAA;AACA;AACA;;;AA1BA;;;;;;;;AA4CO,MAAM,2BAAa,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAClC,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAmB;;IACjE,qBAAqB;IACrB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEnD,CAAC;IAEH,+CAA+C;IAC/C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA0B;IAErD,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc,OAAO,GAAG;QAC1B;+BAAG;QAAC;KAAW;IAEf,6EAA6E;IAC7E,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAC/B,CAAC;YACC,IAAI,CAAC,WAAW,cAAc,OAAO,EAAE;YAEvC,MAAM,mBAAkC,EAAE;YAC1C,MAAM,gBAAgB,UAAU,YAAY,CAAC,MAAM;wEACjD,CAAC,MACC,IAAI,cAAc,KAAK,QAAQ,IAAI,cAAc,KAAK;;YAG1D,cAAc,OAAO;0DAAC,CAAC;oBACrB,iBAAiB,IAAI,CAAC;oBACtB,MAAM,eAAe,UAAU,YAAY;oBAC3C,IACE,gBACA,MAAM,OAAO,CAAC,iBACd,aAAa,MAAM,GAAG,GACtB;wBACA,aAAa,OAAO;sEAAC,CAAC;gCACpB,iBAAiB,IAAI,CAAC;oCACpB,IAAI,SAAS,EAAE;oCACf,YAAY,SAAS,UAAU;oCAC/B,gBAAgB,SAAS,cAAc;gCACzC;4BACF;;oBACF;gBACF;;YAEA,OAAO;QACT;iDACA,EAAE;IAGJ,iEAAiE;IACjE,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YAC7B,IAAI,CAAC,QAAQ,MAAM,EAAE;gBACnB,OAAO;oBACL,eAAe,EAAE;oBACjB,WAAW,IAAI;oBACf,iBAAiB;gBACnB;YACF;YAEA,MAAM,gBAAgB,QAAQ,MAAM;oEAAC,CAAC,MAAQ,CAAC,IAAI,cAAc;;YACjE,MAAM,YAAY,IAAI;YAEtB,cAAc,OAAO;sDAAC,CAAC;oBACrB,MAAM,eAAe,QAAQ,MAAM;2EACjC,CAAC,MAAQ,IAAI,cAAc,KAAK,UAAU,EAAE;;oBAE9C,UAAU,GAAG,CAAC,UAAU,EAAE,EAAE;gBAC9B;;YAEA,MAAM,kBAAkB,cAAc,IAAI;sEACxC,CAAC,IAAM,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG;;YAG9C,OAAO;gBAAE;gBAAe;gBAAW;YAAgB;QACrD;6CAAG;QAAC;KAAQ;IAEZ,qEAAqE;IACrE,MAAM,EACJ,MAAM,SAAS,EACf,WAAW,OAAO,EAClB,OAAO,UAAU,EAClB,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU;YAAC;YAAkB;SAAW;QACxC,OAAO;mCAAE,IAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;;QACnC,SAAS,aAAa;QACtB,WAAW;QACX,QAAQ;IACV;IAEA,uCAAuC;IACvC,MAAM,QAAQ,aAAa,mCAAmC;IAE9D,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,WAAW;gBACd,WAAW,EAAE;gBACb,QAAQ,EAAE;gBACV,cAAc,CAAC;gBACf;YACF;YAEA,QAAQ,GAAG,CAAC,0BAA0B;YACtC,WAAW,eAAe;YAC1B,QAAQ,UAAU,SAAS,IAAI,EAAE;YAEjC,mDAAmD;YACnD,MAAM,oBAA4C,CAAC;YACnD,MAAM,kBAA4B,EAAE;YAQpC,+CAA+C;YAC/C,IAAI,UAAU,UAAU,EAAE;gBACxB,QAAQ,GAAG,CAAC,kCAAkC,UAAU,UAAU;gBAClE,wCAAwC;gBAEtC,OAAO,OAAO,CAAC,UAAU,UAAU,EAInC,OAAO;4CAAC,CAAC,CAAC,KAAK,MAAM;wBACrB,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;4BAC/C,2CAA2C;4BAC3C,MAAM,YAAY,MAAM,KAAK,IAAI;4BACjC,IAAI,UAAU,IAAI,OAAO,IAAI;gCAC3B,iBAAiB,CAAC,IAAI,GAAG;gCACzB,gBAAgB,IAAI,CAAC;gCACrB,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,IAAI,EAAE,EAAE,UAAU,aAAa,EAAE,MAAM,SAAS,CAAC,CAAC,CAAC;4BAE7F;wBACF,OAAO,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAI;4BAC3D,uBAAuB;4BACvB,iBAAiB,CAAC,IAAI,GAAG;4BACzB,gBAAgB,IAAI,CAAC;4BACrB,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,IAAI,EAAE,EAAE,OAAO;wBACnE;oBACF;;YACF;YAEA,0EAA0E;YAC1E,IAAI,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,MAAM,GAAG,GAAG;gBACzD,UAAU,SAAS,CAAC,OAAO;4CAAC,CAAC;wBAC3B,2CAA2C;wBAC3C,wEAAwE;wBACxE,IACE,IAAI,aAAa,IACjB,MAAM,OAAO,CAAC,IAAI,aAAa,KAC/B,IAAI,aAAa,CAAC,MAAM,GAAG,GAC3B;4BACA,IAAI,aAAa,CAAC,OAAO;wDAAC,CAAC;oCACzB,MAAM,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;oCACtC,iBAAiB,CAAC,IAAI,GAAG,GAAG,KAAK;oCACjC,gBAAgB,IAAI,CAAC;oCACrB,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,IAAI,EAAE,EAAE,GAAG,KAAK,EAAE;gCAE5D;;wBACF,OAAO;4BACL,QAAQ,GAAG,CACT,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,4CAA4C,CAAC;wBAE/D;oBACF;;YACF;YAEA,6CAA6C;YAC7C,IAAI,OAAO,IAAI,CAAC,mBAAmB,MAAM,GAAG,GAAG;gBAC7C,QAAQ,GAAG,CAAC,wBAAwB;gBACpC,QAAQ,GAAG,CAAC,sBAAsB;gBAElC,uDAAuD;gBACvD,MAAM,gBAAgB;oBAAE,GAAG,iBAAiB;gBAAC;gBAE7C,oCAAoC;gBACpC,OAAO,OAAO,CAAC,eAAe,OAAO;4CAAC,CAAC,CAAC,KAAK,MAAM;wBACjD,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,IAAI,EAAE,EAAE,OAAO;oBACvD;;gBAEA,mEAAmE;gBACnE,cAAc;gBAEd,wEAAwE;gBACxE;4CAAW;wBACT,QAAQ,GAAG,CAAC;wBACZ;oDAAc,CAAC,aAAe,CAAC;oCAAE,GAAG,UAAU;gCAAC,CAAC;;oBAClD;2CAAG;YACL,OAAO;gBACL,QAAQ,IAAI,CAAC;gBACb,cAAc,CAAC;YACjB;QACF;+BAAG;QAAC;QAAW;KAAe;IAE9B,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,SAAS;YAEb,MAAM,oBAA4C,CAAC;YACnD,IAAI,WAAwB,EAAE;YAE9B,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI,IAAI;gBAC7C,IAAI;oBACF,WAAW,KAAK,KAAK,CAAC;gBACxB,EAAE,OAAM;oBACN,WAAW,EAAE;gBACf;YACF,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;gBAC/B,WAAW;YACb;YAEA,SAAS,OAAO;wCAAC,CAAC;oBAChB,iBAAiB,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,CAAC,GAAG,KAAK,KAAK;gBACnE;;YAEA,MAAM,cAAc,CAAC,SAAU,MAAM,OAAO,CAAC,UAAU,CAAC,MAAM,MAAM;YAEpE,IAAI,aAAa;gBACf,mBAAmB,CAAC;YACtB,OAAO,IAAI,OAAO,IAAI,CAAC,mBAAmB,MAAM,EAAE;gBAChD;4CAAmB,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,iBAAiB;wBAAC,CAAC;;YACjE;QACF;+BAAG;QAAC;QAAO;KAAQ;IAEnB,yEAAyE;IACzE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;gBAClD,+CAA+C;gBAC/C,MAAM,YAAY;oBAAE,GAAG,UAAU;oBAAE,GAAG,eAAe;gBAAC;gBAEtD,MAAM,oBAAiC,OAAO,OAAO,CAAC,WACnD,MAAM;8DAAC,CAAC,GAAG,MAAM,GAAK,MAAM,IAAI;6DAChC,GAAG;8DAAC,CAAC,CAAC,KAAK,MAAM;wBAChB,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;wBAC1C,OAAO;4BAAE,UAAU;4BAAO,QAAQ;4BAAO;wBAAM;oBACjD;;gBAEF,4CAA4C;gBAC5C,IAAI,kBAAkB,MAAM,GAAG,GAAG;oBAChC,SAAS;gBACX;YACF;QACF;+BAAG;QAAC;QAAY;QAAiB;QAAS;KAAS;IAEnD,8DAA8D;IAC9D,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EACjC,CAAA,GAAA,qIAAA,CAAA,UAAQ,AAAD;oDACL,CAAC,UAAkB,OAAwB;YACzC,qEAAqE;YACrE,MAAM,YAAY;gBAAE,GAAG,UAAU;gBAAE,GAAG,eAAe;YAAC;YACtD,SAAS,CAAC,GAAG,SAAS,CAAC,EAAE,OAAO,CAAC,GAAG;YAEpC,MAAM,oBAAiC,OAAO,OAAO,CAAC,WACnD,MAAM;8EAAC,CAAC,GAAG,MAAM,GAAK,MAAM,IAAI;6EAChC,GAAG;8EAAC,CAAC,CAAC,KAAK,MAAM;oBAChB,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;oBAC1C,OAAO;wBAAE,UAAU;wBAAO,QAAQ;wBAAO;oBAAM;gBACjD;;YAEF,SAAS;QACX;mDACA,KACA;QAAE,SAAS;QAAO,UAAU;IAAK,IAEnC;QAAC;QAAU;QAAY;KAAgB;IAGzC,2EAA2E;IAC3E,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAClC,CAAC,UAAkB,OAAwB;YACzC,0DAA0D;YAC1D;6DAAmB,CAAC,OAAS,CAAC;wBAC5B,GAAG,IAAI;wBACP,CAAC,GAAG,SAAS,CAAC,EAAE,OAAO,CAAC,EAAE;oBAC5B,CAAC;;YACD,2BAA2B;YAC3B,iBAAiB,UAAU,OAAO;QACpC;oDACA;QAAC;KAAiB;IAGpB,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;wCAAO;oBACL,iBAAiB,MAAM;gBACzB;;QACF;+BAAG;QAAC;KAAiB;IAErB,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAChC,EAAE,cAAc;YAChB,EAAE,eAAe;YAEjB;wDAAQ,CAAC,WAAa;2BACjB;wBACH;4BACE,IAAI,CAAC,CAAC,SAAS,MAAM,GAAG,CAAC;4BACzB,UAAU,CAAC,SAAS,MAAM,GAAG,CAAC,EAAE,QAAQ;wBAC1C;qBACD;;QACH;+CAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAChC,CAAC;YACC,IAAI,KAAK,MAAM,IAAI,GAAG;YAEtB,MAAM,cAAc,IAAI,CAAC,SAAS;YAElC,wFAAwF;YACxF,+DAA+D;YAC/D,IAAI,YAAY,EAAE,GAAG,GAAG;gBACtB,QAAQ,GAAG,CACT,CAAC,kBAAkB,EAAE,YAAY,EAAE,CAAC,uCAAuC,CAAC;gBAE9E;YACF;YAEA;2DAAQ,CAAC,WACP,SACG,MAAM;mEAAC,CAAC,GAAG,QAAU,UAAU;kEAC/B,GAAG;mEAAC,CAAC,KAAK,MAAQ,CAAC;gCAClB,GAAG,GAAG;gCACN,UAAU,CAAC,MAAM,CAAC,EAAE,QAAQ;4BAC9B,CAAC;;;YAGL;2DAAmB,CAAC;oBAClB,MAAM,YAAY;wBAAE,GAAG,UAAU;oBAAC;oBAClC,OAAO,IAAI,CAAC,WAAW,OAAO;mEAAC,CAAC;4BAC9B,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,CAAC;4BAC7B,IAAI,UAAU,YAAY,EAAE,CAAC,QAAQ,IAAI;gCACvC,OAAO,SAAS,CAAC,IAAI;4BACvB;wBACF;;oBAEA,6CAA6C;oBAC7C,MAAM,YAAY;wBAAE,GAAG,UAAU;wBAAE,GAAG,SAAS;oBAAC;oBAChD,MAAM,oBAAiC,OAAO,OAAO,CAAC,WACnD,MAAM;qFAAC,CAAC,GAAG,MAAM,GAAK,MAAM,IAAI;oFAChC,GAAG;qFAAC,CAAC,CAAC,KAAK,MAAM;4BAChB,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;4BAC1C,OAAO;gCAAE,UAAU;gCAAO,QAAQ;gCAAO;4BAAM;wBACjD;;oBAEF,SAAS;oBACT,OAAO;gBACT;;QACF;kDACA;QAAC;QAAM;QAAU;KAAW;IAG9B,gEAAgE;IAChE,qFAAqF;IACrF,gDAAgD;IAChD,kFAAkF;IAClF,gEAAgE;IAChE,mBAAmB;IACnB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE;YACxB,IAAI,CAAC,KAAK,MAAM,EAAE;gBAChB,qBACE,6LAAC,6HAAA,CAAA,WAAU;;sCACT,6LAAC,6HAAA,CAAA,YAAS;4BAAC,WAAU;sCAAmD;;;;;;wBAGvE,eAAe,aAAa,CAAC,GAAG;6DAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAClD,IAAI,CAAC,aAAa,MAAM,EAAE;oCACxB,MAAM,YAAY,UAAU,CAAC,GAAG,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;oCAC1D,MAAM,kBAAkB,UAAU,IAAI,OAAO;oCAE7C,qBACE,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;kDAET,kBACC,8DAA8D;sDAC9D,6LAAC;4CAAI,WAAU;sDACZ;;;;;mDAGH,kCAAkC;sDAClC,6LAAC,6HAAA,CAAA,QAAK;4CACJ,OAAO,eAAe,CAAC,GAAG,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;4CACpD,QAAQ;iFAAE,CAAC,IACT,kBACE,UAAU,EAAE,EACZ,UACA,EAAE,MAAM,CAAC,KAAK;;4CAGlB,aAAY;4CACZ,WAAU;;;;;;uCApBT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC;;;;;gCAyBxC;gCACA,OAAO,aAAa,GAAG;qEAAC,CAAC;wCACvB,MAAM,YAAY,UAAU,CAAC,GAAG,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;wCACzD,MAAM,kBAAkB,UAAU,IAAI,OAAO;wCAE7C,qBACE,6LAAC,6HAAA,CAAA,YAAS;4CAER,WAAU;sDAET,kBACC,8DAA8D;0DAC9D,6LAAC;gDAAI,WAAU;0DACZ;;;;;uDAGH,kCAAkC;0DAClC,6LAAC,6HAAA,CAAA,QAAK;gDACJ,OAAO,eAAe,CAAC,GAAG,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;gDACnD,QAAQ;qFAAE,CAAC,IACT,kBACE,SAAS,EAAE,EACX,UACA,EAAE,MAAM,CAAC,KAAK;;gDAGlB,aAAY;gDACZ,WAAU;;;;;;2CApBT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC;;;;;oCAyBvC;;4BACF;;sCACA,6LAAC,6HAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;YAG3B;YAEA,OAAO,KAAK,GAAG;iDAAC,CAAC,KAAK,yBACpB,6LAAC,6HAAA,CAAA,WAAU;wBAET,WAAW,WAAW,MAAM,IAAI,aAAa;;0CAE7C,6LAAC,6HAAA,CAAA,YAAS;gCAAC,WAAU;0CAClB,WAAW;;;;;;4BAEb,eAAe,aAAa,CAAC,GAAG;iEAAC,CAAC;oCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;oCAClD,IAAI,CAAC,aAAa,MAAM,EAAE;wCACxB,mDAAmD;wCACnD,MAAM,UAAU,GAAG,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;wCAC3C,MAAM,YAAY,UAAU,CAAC,QAAQ,IAAI;wCACzC,MAAM,kBAAkB,UAAU,IAAI,OAAO;wCAE7C,qBACE,6LAAC,6HAAA,CAAA,YAAS;4CAER,WAAU;sDAET,kBACC,8DAA8D;0DAC9D,6LAAC;gDAAI,WAAU;0DACZ;;;;;uDAGH,kCAAkC;0DAClC,6LAAC,6HAAA,CAAA,QAAK;gDACJ,OAAO,eAAe,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;gDACvD,QAAQ;qFAAE,CAAC,IACT,kBAAkB,UAAU,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;;gDAExD,aAAY;gDACZ,WAAU;;;;;;2CAhBT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;oCAqB3C;oCACA,OAAO,aAAa,GAAG;yEAAC,CAAC;4CACvB,MAAM,UAAU,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;4CAC1C,MAAM,YAAY,UAAU,CAAC,QAAQ,IAAI;4CACzC,MAAM,kBAAkB,UAAU,IAAI,OAAO;4CAE7C,qBACE,6LAAC,6HAAA,CAAA,YAAS;gDAER,WAAU;0DAET,kBACC,8DAA8D;8DAC9D,6LAAC;oDAAI,WAAU;8DACZ;;;;;2DAGH,kCAAkC;8DAClC,6LAAC,6HAAA,CAAA,QAAK;oDACJ,OAAO,eAAe,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;oDACtD,QAAQ;yFAAE,CAAC,IACT,kBAAkB,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;;oDAEvD,aAAY;oDACZ,WAAU;;;;;;+CAhBT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;wCAqB1C;;gCACF;;0CACA,6LAAC,6HAAA,CAAA,YAAS;gCAAC,WAAU;0CACnB,cAAA,6LAAC;oCACC,MAAK;oCACL,WAAW,GACT,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE,GAAG,IACzB,qCACA,mCACJ;oCACF,OAAO;yEAAE,IAAM,gBAAgB;;oCAC/B,UAAU,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE,GAAG;oCACvC,cACE,IAAI,EAAE,GAAG,IAAI,qCAAqC;oCAEpD,OACE,IAAI,EAAE,GAAG,IACL,8DACA;8CAGN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;uBAxFjB,IAAI,EAAE;;;;;;QA6FjB;wCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe,CAAC,QAAQ,MAAM;IAEpC,qBACE,6LAAC;QAAI,WAAU;kBACZ,wBACC,6LAAC;YAAI,WAAU;sBAAmB;;;;;mBAChC,sBACF,6LAAC;YAAI,WAAU;sBAAiC;;;;;mBAC9C,6BACF,6LAAC;YAAI,WAAU;sBAAkC;;;;;iCAKjD;;8BACE,6LAAC,6HAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,6LAAC,6HAAA,CAAA,cAAW;;8CACV,6LAAC,6HAAA,CAAA,WAAU;;sDACT,6LAAC,6HAAA,CAAA,YAAS;4CACR,WAAU;4CACV,SAAS,eAAe,eAAe,GAAG,IAAI;sDAC/C;;;;;;wCAGA,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;4CACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;4CAClD,MAAM,UAAU,aAAa,MAAM,IAAI;4CACvC,qBACE,6LAAC,6HAAA,CAAA,YAAS;gDAER,SAAS;gDACT,WAAU;gDACV,SAAS,aAAa,MAAM,KAAK,IAAI,IAAI;0DAExC,UAAU,UAAU;+CALhB,UAAU,EAAE;;;;;wCAQvB;;;;;;;gCAED,eAAe,eAAe,kBAC7B,6LAAC,6HAAA,CAAA,WAAU;8CACR,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;wCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;wCAClD,IAAI,CAAC,aAAa,MAAM,EAAE,OAAO;wCACjC,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;gDAER,WAAU;0DAET,SAAS,UAAU;+CAHf,SAAS,EAAE;;;;;oCAMtB;;;;;;;;;;;;sCAIN,6LAAC,6HAAA,CAAA,YAAS;sCAAE;;;;;;;;;;;;8BAEd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;;;AAO1C;;QA1hBM,8KAAA,CAAA,WAAQ;;;;QAAR,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/conditionalQuestions.ts"], "sourcesContent": ["import { Question } from \"@/types/formBuilder\";\r\n\r\n/**\r\n * Utility functions for handling conditional questions logic\r\n */\r\n\r\n/**\r\n * Get the next question ID based on the selected option\r\n */\r\nexport const getNextQuestionId = (\r\n  question: Question,\r\n  selectedValue: string | string[]\r\n): number | null => {\r\n  if (!question.questionOptions || question.questionOptions.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  // For selectone, selectedValue is a string\r\n  if (question.inputType === \"selectone\" && typeof selectedValue === \"string\") {\r\n    const selectedOption = question.questionOptions.find(\r\n      (option) => option.label === selectedValue\r\n    );\r\n    return selectedOption?.nextQuestionId || null;\r\n  }\r\n\r\n  // For selectmany, selectedValue is an array - return the first next question found\r\n  if (question.inputType === \"selectmany\" && Array.isArray(selectedValue)) {\r\n    for (const value of selectedValue) {\r\n      const selectedOption = question.questionOptions.find(\r\n        (option) => option.label === value\r\n      );\r\n      if (selectedOption?.nextQuestionId) {\r\n        return selectedOption.nextQuestionId;\r\n      }\r\n    }\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n/**\r\n * Get all possible next question IDs for a question (for dependency tracking)\r\n */\r\nexport const getAllNextQuestionIds = (question: Question): number[] => {\r\n  if (!question.questionOptions || question.questionOptions.length === 0) {\r\n    return [];\r\n  }\r\n\r\n  return question.questionOptions\r\n    .map((option) => option.nextQuestionId)\r\n    .filter((id): id is number => id !== null && id !== undefined);\r\n};\r\n\r\n/**\r\n * Determine which questions should be visible based on current answers\r\n */\r\nexport const getVisibleQuestions = (\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Question[] => {\r\n  const visibleQuestionIds = new Set<number>();\r\n  const conditionalQuestionIds = new Set<number>();\r\n\r\n  // First, collect all questions that are conditional (have a parent question)\r\n  allQuestions.forEach((question) => {\r\n    const nextQuestionIds = getAllNextQuestionIds(question);\r\n    nextQuestionIds.forEach((id) => conditionalQuestionIds.add(id));\r\n  });\r\n\r\n  // Start with all non-conditional questions (questions that are not triggered by other questions)\r\n  allQuestions.forEach((question) => {\r\n    if (!conditionalQuestionIds.has(question.id)) {\r\n      visibleQuestionIds.add(question.id);\r\n    }\r\n  });\r\n\r\n  // Process answers to determine which conditional questions should be visible\r\n  Object.entries(answers).forEach(([questionIdStr, answer]) => {\r\n    const questionId = parseInt(questionIdStr);\r\n    const question = allQuestions.find((q) => q.id === questionId);\r\n\r\n    if (question && answer) {\r\n      const nextQuestionId = getNextQuestionId(question, answer);\r\n      if (nextQuestionId) {\r\n        visibleQuestionIds.add(nextQuestionId);\r\n      }\r\n    }\r\n  });\r\n\r\n  // Return questions in their original order, filtered by visibility\r\n  return allQuestions.filter((question) => visibleQuestionIds.has(question.id));\r\n};\r\n\r\n/**\r\n * Check if a question should be visible based on current answers\r\n */\r\nexport const isQuestionVisible = (\r\n  question: Question,\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): boolean => {\r\n  const visibleQuestions = getVisibleQuestions(allQuestions, answers);\r\n  return visibleQuestions.some((q) => q.id === question.id);\r\n};\r\n\r\n/**\r\n * Get questions that depend on a specific question\r\n */\r\nexport const getDependentQuestions = (\r\n  parentQuestionId: number,\r\n  allQuestions: Question[]\r\n): Question[] => {\r\n  const parentQuestion = allQuestions.find((q) => q.id === parentQuestionId);\r\n  if (!parentQuestion) return [];\r\n\r\n  const nextQuestionIds = getAllNextQuestionIds(parentQuestion);\r\n  return allQuestions.filter((q) => nextQuestionIds.includes(q.id));\r\n};\r\n\r\n/**\r\n * Check if a question is a follow-up question (has a parent question)\r\n */\r\nexport const isFollowUpQuestion = (\r\n  questionId: number,\r\n  allQuestions: Question[]\r\n): boolean => {\r\n  return allQuestions.some((question) => {\r\n    const nextQuestionIds = getAllNextQuestionIds(question);\r\n    return nextQuestionIds.includes(questionId);\r\n  });\r\n};\r\n\r\n/**\r\n * Get the parent question for a follow-up question\r\n */\r\nexport const getParentQuestion = (\r\n  questionId: number,\r\n  allQuestions: Question[]\r\n): Question | null => {\r\n  return (\r\n    allQuestions.find((question) => {\r\n      const nextQuestionIds = getAllNextQuestionIds(question);\r\n      return nextQuestionIds.includes(questionId);\r\n    }) || null\r\n  );\r\n};\r\n\r\n/**\r\n * Get questions in nested structure for rendering\r\n * Returns questions grouped by parent-child relationships, maintaining order\r\n */\r\nexport const getNestedQuestions = (\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Array<{\r\n  question: Question;\r\n  isVisible: boolean;\r\n  isFollowUp: boolean;\r\n  parentQuestion?: Question;\r\n  followUps: Array<{\r\n    question: Question;\r\n    isVisible: boolean;\r\n  }>;\r\n}> => {\r\n  const visibleQuestions = getVisibleQuestions(allQuestions, answers);\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n\r\n  // Get all parent questions (questions that are not follow-ups themselves)\r\n  const parentQuestions = allQuestions.filter(\r\n    (question) => !isFollowUpQuestion(question.id, allQuestions)\r\n  );\r\n\r\n  // Sort parent questions by position to maintain order\r\n  const sortedParentQuestions = parentQuestions.sort(\r\n    (a, b) => a.position - b.position\r\n  );\r\n\r\n  return sortedParentQuestions\r\n    .map((parentQuestion) => {\r\n      const followUpQuestions = getDependentQuestions(\r\n        parentQuestion.id,\r\n        allQuestions\r\n      );\r\n      const sortedFollowUps = followUpQuestions.sort(\r\n        (a, b) => a.position - b.position\r\n      );\r\n\r\n      return {\r\n        question: parentQuestion,\r\n        isVisible: visibleQuestionIds.has(parentQuestion.id),\r\n        isFollowUp: false,\r\n        followUps: sortedFollowUps.map((followUp) => ({\r\n          question: followUp,\r\n          isVisible: visibleQuestionIds.has(followUp.id),\r\n        })),\r\n      };\r\n    })\r\n    .filter(\r\n      (group) => group.isVisible || group.followUps.some((f) => f.isVisible)\r\n    );\r\n};\r\n\r\n/**\r\n * Clean up answers for questions that are no longer visible\r\n */\r\nexport const cleanupHiddenAnswers = (\r\n  answers: Record<string, any>,\r\n  visibleQuestions: Question[]\r\n): Record<string, any> => {\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n  const cleanedAnswers: Record<string, any> = {};\r\n\r\n  Object.entries(answers).forEach(([questionId, answer]) => {\r\n    if (visibleQuestionIds.has(parseInt(questionId))) {\r\n      cleanedAnswers[questionId] = answer;\r\n    }\r\n  });\r\n\r\n  return cleanedAnswers;\r\n};\r\n\r\n/**\r\n * Validate that all visible required questions have answers\r\n */\r\nexport const validateVisibleQuestions = (\r\n  visibleQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Record<string, string> => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  visibleQuestions.forEach((question) => {\r\n    if (question.isRequired) {\r\n      const value = answers[question.id];\r\n      if (\r\n        (typeof value === \"string\" && !value.trim()) ||\r\n        (Array.isArray(value) && value.length === 0) ||\r\n        value === undefined ||\r\n        value === null\r\n      ) {\r\n        errors[question.id] = `${question.label} is required`;\r\n      }\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AASO,MAAM,oBAAoB,CAC/B,UACA;IAEA,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,KAAK,GAAG;QACtE,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,SAAS,SAAS,KAAK,eAAe,OAAO,kBAAkB,UAAU;QAC3E,MAAM,iBAAiB,SAAS,eAAe,CAAC,IAAI,CAClD,CAAC,SAAW,OAAO,KAAK,KAAK;QAE/B,OAAO,gBAAgB,kBAAkB;IAC3C;IAEA,mFAAmF;IACnF,IAAI,SAAS,SAAS,KAAK,gBAAgB,MAAM,OAAO,CAAC,gBAAgB;QACvE,KAAK,MAAM,SAAS,cAAe;YACjC,MAAM,iBAAiB,SAAS,eAAe,CAAC,IAAI,CAClD,CAAC,SAAW,OAAO,KAAK,KAAK;YAE/B,IAAI,gBAAgB,gBAAgB;gBAClC,OAAO,eAAe,cAAc;YACtC;QACF;IACF;IAEA,OAAO;AACT;AAKO,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,KAAK,GAAG;QACtE,OAAO,EAAE;IACX;IAEA,OAAO,SAAS,eAAe,CAC5B,GAAG,CAAC,CAAC,SAAW,OAAO,cAAc,EACrC,MAAM,CAAC,CAAC,KAAqB,OAAO,QAAQ,OAAO;AACxD;AAKO,MAAM,sBAAsB,CACjC,cACA;IAEA,MAAM,qBAAqB,IAAI;IAC/B,MAAM,yBAAyB,IAAI;IAEnC,6EAA6E;IAC7E,aAAa,OAAO,CAAC,CAAC;QACpB,MAAM,kBAAkB,sBAAsB;QAC9C,gBAAgB,OAAO,CAAC,CAAC,KAAO,uBAAuB,GAAG,CAAC;IAC7D;IAEA,iGAAiG;IACjG,aAAa,OAAO,CAAC,CAAC;QACpB,IAAI,CAAC,uBAAuB,GAAG,CAAC,SAAS,EAAE,GAAG;YAC5C,mBAAmB,GAAG,CAAC,SAAS,EAAE;QACpC;IACF;IAEA,6EAA6E;IAC7E,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,eAAe,OAAO;QACtD,MAAM,aAAa,SAAS;QAC5B,MAAM,WAAW,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QAEnD,IAAI,YAAY,QAAQ;YACtB,MAAM,iBAAiB,kBAAkB,UAAU;YACnD,IAAI,gBAAgB;gBAClB,mBAAmB,GAAG,CAAC;YACzB;QACF;IACF;IAEA,mEAAmE;IACnE,OAAO,aAAa,MAAM,CAAC,CAAC,WAAa,mBAAmB,GAAG,CAAC,SAAS,EAAE;AAC7E;AAKO,MAAM,oBAAoB,CAC/B,UACA,cACA;IAEA,MAAM,mBAAmB,oBAAoB,cAAc;IAC3D,OAAO,iBAAiB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE;AAC1D;AAKO,MAAM,wBAAwB,CACnC,kBACA;IAEA,MAAM,iBAAiB,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACzD,IAAI,CAAC,gBAAgB,OAAO,EAAE;IAE9B,MAAM,kBAAkB,sBAAsB;IAC9C,OAAO,aAAa,MAAM,CAAC,CAAC,IAAM,gBAAgB,QAAQ,CAAC,EAAE,EAAE;AACjE;AAKO,MAAM,qBAAqB,CAChC,YACA;IAEA,OAAO,aAAa,IAAI,CAAC,CAAC;QACxB,MAAM,kBAAkB,sBAAsB;QAC9C,OAAO,gBAAgB,QAAQ,CAAC;IAClC;AACF;AAKO,MAAM,oBAAoB,CAC/B,YACA;IAEA,OACE,aAAa,IAAI,CAAC,CAAC;QACjB,MAAM,kBAAkB,sBAAsB;QAC9C,OAAO,gBAAgB,QAAQ,CAAC;IAClC,MAAM;AAEV;AAMO,MAAM,qBAAqB,CAChC,cACA;IAWA,MAAM,mBAAmB,oBAAoB,cAAc;IAC3D,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IAEnE,0EAA0E;IAC1E,MAAM,kBAAkB,aAAa,MAAM,CACzC,CAAC,WAAa,CAAC,mBAAmB,SAAS,EAAE,EAAE;IAGjD,sDAAsD;IACtD,MAAM,wBAAwB,gBAAgB,IAAI,CAChD,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAGnC,OAAO,sBACJ,GAAG,CAAC,CAAC;QACJ,MAAM,oBAAoB,sBACxB,eAAe,EAAE,EACjB;QAEF,MAAM,kBAAkB,kBAAkB,IAAI,CAC5C,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAGnC,OAAO;YACL,UAAU;YACV,WAAW,mBAAmB,GAAG,CAAC,eAAe,EAAE;YACnD,YAAY;YACZ,WAAW,gBAAgB,GAAG,CAAC,CAAC,WAAa,CAAC;oBAC5C,UAAU;oBACV,WAAW,mBAAmB,GAAG,CAAC,SAAS,EAAE;gBAC/C,CAAC;QACH;IACF,GACC,MAAM,CACL,CAAC,QAAU,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,SAAS;AAE3E;AAKO,MAAM,uBAAuB,CAClC,SACA;IAEA,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IACnE,MAAM,iBAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO;QACnD,IAAI,mBAAmB,GAAG,CAAC,SAAS,cAAc;YAChD,cAAc,CAAC,WAAW,GAAG;QAC/B;IACF;IAEA,OAAO;AACT;AAKO,MAAM,2BAA2B,CACtC,kBACA;IAEA,MAAM,SAAiC,CAAC;IAExC,iBAAiB,OAAO,CAAC,CAAC;QACxB,IAAI,SAAS,UAAU,EAAE;YACvB,MAAM,QAAQ,OAAO,CAAC,SAAS,EAAE,CAAC;YAClC,IACE,AAAC,OAAO,UAAU,YAAY,CAAC,MAAM,IAAI,MACxC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,KAC1C,UAAU,aACV,UAAU,MACV;gBACA,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,SAAS,KAAK,CAAC,YAAY,CAAC;YACvD;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-inputs/NestedQuestionRenderer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { Label } from \"@/components/ui/label\";\r\n\r\ninterface NestedQuestionRendererProps {\r\n  questionGroup: {\r\n    question: Question;\r\n    isVisible: boolean;\r\n    isFollowUp: boolean;\r\n    followUps: Array<{\r\n      question: Question;\r\n      isVisible: boolean;\r\n    }>;\r\n  };\r\n  renderQuestionInput: (question: Question) => React.ReactNode;\r\n  errors: Record<number, string>;\r\n  className?: string;\r\n}\r\n\r\nconst NestedQuestionRenderer: React.FC<NestedQuestionRendererProps> = ({\r\n  questionGroup,\r\n  renderQuestionInput,\r\n  errors,\r\n  className = \"\",\r\n}) => {\r\n  const { question: parentQuestion, isVisible: isParentVisible, followUps } = questionGroup;\r\n  \r\n  // Don't render anything if neither parent nor any follow-ups are visible\r\n  if (!isParentVisible && !followUps.some(f => f.isVisible)) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className={`${className}`}>\r\n      {/* Parent Question */}\r\n      {isParentVisible && (\r\n        <div className=\"border border-neutral-500 dark:border-gray-700 rounded-md p-4 bg-neutral-100 dark:bg-gray-800\">\r\n          <div className=\"mb-2\">\r\n            <Label className=\"text-base font-medium\">\r\n              {parentQuestion.label}\r\n              {parentQuestion.isRequired && (\r\n                <span className=\"text-red-500 ml-1\">*</span>\r\n              )}\r\n            </Label>\r\n            {parentQuestion.hint && (\r\n              <p className=\"text-sm text-muted-foreground mt-1\">\r\n                {parentQuestion.hint}\r\n              </p>\r\n            )}\r\n            {errors[parentQuestion.id] && (\r\n              <p className=\"text-sm text-red-500 mt-1\">\r\n                {errors[parentQuestion.id]}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <div className=\"mt-2\">{renderQuestionInput(parentQuestion)}</div>\r\n          \r\n          {/* Follow-up Questions */}\r\n          {followUps.some(f => f.isVisible) && (\r\n            <div className=\"mt-4 ml-4 space-y-3 border-l-2 border-blue-200 dark:border-blue-700 pl-4\">\r\n              {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (\r\n                isFollowUpVisible && (\r\n                  <div\r\n                    key={followUpQuestion.id}\r\n                    className=\"border border-gray-100 dark:border-gray-600 rounded-md p-3 bg-blue-50 dark:bg-blue-900/20\"\r\n                  >\r\n                    <div className=\"mb-2\">\r\n                      <Label className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">\r\n                        {followUpQuestion.label}\r\n                        {followUpQuestion.isRequired && (\r\n                          <span className=\"text-red-500 ml-1\">*</span>\r\n                        )}\r\n                      </Label>\r\n                      {followUpQuestion.hint && (\r\n                        <p className=\"text-xs text-blue-700 dark:text-blue-300 mt-1\">\r\n                          {followUpQuestion.hint}\r\n                        </p>\r\n                      )}\r\n                      {errors[followUpQuestion.id] && (\r\n                        <p className=\"text-xs text-red-500 mt-1\">\r\n                          {errors[followUpQuestion.id]}\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"mt-2\">{renderQuestionInput(followUpQuestion)}</div>\r\n                  </div>\r\n                )\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      {/* Render follow-ups without parent if parent is not visible but follow-ups are */}\r\n      {!isParentVisible && followUps.some(f => f.isVisible) && (\r\n        <div className=\"space-y-3\">\r\n          {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (\r\n            isFollowUpVisible && (\r\n              <div\r\n                key={followUpQuestion.id}\r\n                className=\"border border-gray-200 dark:border-gray-700 rounded-md p-4 bg-white dark:bg-gray-800\"\r\n              >\r\n                <div className=\"mb-2\">\r\n                  <Label className=\"text-base font-medium\">\r\n                    {followUpQuestion.label}\r\n                    {followUpQuestion.isRequired && (\r\n                      <span className=\"text-red-500 ml-1\">*</span>\r\n                    )}\r\n                  </Label>\r\n                  {followUpQuestion.hint && (\r\n                    <p className=\"text-sm text-muted-foreground mt-1\">\r\n                      {followUpQuestion.hint}\r\n                    </p>\r\n                  )}\r\n                  {errors[followUpQuestion.id] && (\r\n                    <p className=\"text-sm text-red-500 mt-1\">\r\n                      {errors[followUpQuestion.id]}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n                <div className=\"mt-2\">{renderQuestionInput(followUpQuestion)}</div>\r\n              </div>\r\n            )\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NestedQuestionRenderer;\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAqBA,MAAM,yBAAgE,CAAC,EACrE,aAAa,EACb,mBAAmB,EACnB,MAAM,EACN,YAAY,EAAE,EACf;IACC,MAAM,EAAE,UAAU,cAAc,EAAE,WAAW,eAAe,EAAE,SAAS,EAAE,GAAG;IAE5E,yEAAyE;IACzE,IAAI,CAAC,mBAAmB,CAAC,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG;QACzD,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,GAAG,WAAW;;YAE3B,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;;oCACd,eAAe,KAAK;oCACpB,eAAe,UAAU,kBACxB,6LAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;4BAGvC,eAAe,IAAI,kBAClB,6LAAC;gCAAE,WAAU;0CACV,eAAe,IAAI;;;;;;4BAGvB,MAAM,CAAC,eAAe,EAAE,CAAC,kBACxB,6LAAC;gCAAE,WAAU;0CACV,MAAM,CAAC,eAAe,EAAE,CAAC;;;;;;;;;;;;kCAIhC,6LAAC;wBAAI,WAAU;kCAAQ,oBAAoB;;;;;;oBAG1C,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,mBAC9B,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,EAAE,UAAU,gBAAgB,EAAE,WAAW,iBAAiB,EAAE,GAC1E,mCACE,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;;oDACd,iBAAiB,KAAK;oDACtB,iBAAiB,UAAU,kBAC1B,6LAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;4CAGvC,iBAAiB,IAAI,kBACpB,6LAAC;gDAAE,WAAU;0DACV,iBAAiB,IAAI;;;;;;4CAGzB,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAC1B,6LAAC;gDAAE,WAAU;0DACV,MAAM,CAAC,iBAAiB,EAAE,CAAC;;;;;;;;;;;;kDAIlC,6LAAC;wCAAI,WAAU;kDAAQ,oBAAoB;;;;;;;+BArBtC,iBAAiB,EAAE;;;;;;;;;;;;;;;;YA+BrC,CAAC,mBAAmB,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,mBAClD,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,EAAE,UAAU,gBAAgB,EAAE,WAAW,iBAAiB,EAAE,GAC1E,mCACE,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;;4CACd,iBAAiB,KAAK;4CACtB,iBAAiB,UAAU,kBAC1B,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;;oCAGvC,iBAAiB,IAAI,kBACpB,6LAAC;wCAAE,WAAU;kDACV,iBAAiB,IAAI;;;;;;oCAGzB,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAC1B,6LAAC;wCAAE,WAAU;kDACV,MAAM,CAAC,iBAAiB,EAAE,CAAC;;;;;;;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;0CAAQ,oBAAoB;;;;;;;uBArBtC,iBAAiB,EAAE;;;;;;;;;;;;;;;;AA6BxC;KA7GM;uCA+GS", "debugId": null}}, {"offset": {"line": 1528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-preview/form-preview.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport {\r\n  X,\r\n  ChevronLeft,\r\n  Calendar,\r\n  Clock,\r\n  ChevronDown,\r\n  ChevronRight,\r\n} from \"lucide-react\";\r\nimport { TableInput } from \"@/components/form-inputs/TableInput\";\r\nimport {\r\n  getVisibleQuestions,\r\n  cleanupHiddenAnswers,\r\n  validateVisibleQuestions,\r\n  getNestedQuestions,\r\n} from \"@/lib/conditionalQuestions\";\r\nimport NestedQuestionRenderer from \"@/components/form-inputs/NestedQuestionRenderer\";\r\nimport { ContextType } from \"@/types\";\r\n\r\ninterface FormPreviewProps {\r\n  questions: Question[];\r\n  questionGroups?: QuestionGroup[];\r\n  contextType?: ContextType;\r\n  onClose: () => void;\r\n  hashedId?: string; // Add hashedId prop to pass to the test form\r\n}\r\n\r\nexport function FormPreview({\r\n  questions,\r\n  questionGroups = [],\r\n  contextType = \"project\",\r\n  onClose,\r\n  hashedId,\r\n}: FormPreviewProps) {\r\n  const [answers, setAnswers] = useState<Record<string, any>>({});\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);\r\n  const [nestedQuestions, setNestedQuestions] = useState<\r\n    Array<{\r\n      question: Question;\r\n      isVisible: boolean;\r\n      isFollowUp: boolean;\r\n      followUps: Array<{\r\n        question: Question;\r\n        isVisible: boolean;\r\n      }>;\r\n    }>\r\n  >([]);\r\n  const [expandedGroups, setExpandedGroups] = useState<Record<number, boolean>>(\r\n    {}\r\n  );\r\n\r\n  useEffect(() => {\r\n    const initialAnswers: Record<string, any> = {};\r\n    questions.forEach((question) => {\r\n      initialAnswers[question.id] =\r\n        question.inputType === \"selectmany\" ? [] : \"\";\r\n    });\r\n    setAnswers(initialAnswers);\r\n  }, [questions]);\r\n\r\n  // Update visible questions when answers or questions change\r\n  useEffect(() => {\r\n    if (questions) {\r\n      const newVisibleQuestions = getVisibleQuestions(questions, answers);\r\n      setVisibleQuestions(newVisibleQuestions);\r\n\r\n      // Calculate nested question structure\r\n      const newNestedQuestions = getNestedQuestions(questions, answers);\r\n      setNestedQuestions(newNestedQuestions);\r\n\r\n      // Clean up answers for questions that are no longer visible\r\n      const cleanedAnswers = cleanupHiddenAnswers(answers, newVisibleQuestions);\r\n      if (Object.keys(cleanedAnswers).length !== Object.keys(answers).length) {\r\n        setAnswers(cleanedAnswers);\r\n      }\r\n    }\r\n  }, [questions, answers]);\r\n\r\n  // Initialize all groups as expanded\r\n  useEffect(() => {\r\n    const initialExpandedState: Record<number, boolean> = {};\r\n    questionGroups.forEach((group) => {\r\n      initialExpandedState[group.id] = true;\r\n    });\r\n    setExpandedGroups(initialExpandedState);\r\n  }, [questionGroups]);\r\n\r\n  // Group questions by their group ID\r\n  const groupedQuestions = questionGroups.reduce(\r\n    (acc: Record<number, Question[]>, group: QuestionGroup) => {\r\n      acc[group.id] = questions.filter((q) => q.questionGroupId === group.id);\r\n      return acc;\r\n    },\r\n    {} as Record<number, Question[]>\r\n  );\r\n\r\n  // Get ungrouped questions\r\n  const ungroupedQuestions = questions.filter(\r\n    (q) => q.questionGroupId === null || q.questionGroupId === undefined\r\n  );\r\n\r\n  // Create a unified list of form items (groups and individual questions) for dynamic ordering\r\n  const unifiedFormItems = React.useMemo(() => {\r\n    const items: Array<{\r\n      type: \"group\" | \"question\";\r\n      data: QuestionGroup | Question;\r\n      order: number;\r\n      originalPosition?: number;\r\n    }> = [];\r\n\r\n    // Only add question groups for projects (not templates or question blocks)\r\n    if (contextType === \"project\") {\r\n      questionGroups.forEach((group: QuestionGroup) => {\r\n        // For groups, find the minimum position of questions in the group\r\n        const groupQuestions = questions.filter(\r\n          (q) => q.questionGroupId === group.id\r\n        );\r\n        const minQuestionPosition =\r\n          groupQuestions.length > 0\r\n            ? Math.min(...groupQuestions.map((q) => q.position))\r\n            : group.order;\r\n\r\n        items.push({\r\n          type: \"group\",\r\n          data: group,\r\n          order: minQuestionPosition,\r\n          originalPosition: minQuestionPosition,\r\n        });\r\n      });\r\n    }\r\n\r\n    // Add ungrouped questions (or all questions if not in project context)\r\n    const questionsToAdd =\r\n      contextType === \"project\" ? ungroupedQuestions : questions;\r\n    questionsToAdd.forEach((question: Question) => {\r\n      items.push({\r\n        type: \"question\",\r\n        data: question,\r\n        order: question.position,\r\n        originalPosition: question.position,\r\n      });\r\n    });\r\n\r\n    // Sort by order/position with secondary sort for consistency\r\n    return items.sort((a, b) => {\r\n      if (a.order === b.order) {\r\n        return (\r\n          (a.originalPosition || a.order) - (b.originalPosition || b.order)\r\n        );\r\n      }\r\n      return a.order - b.order;\r\n    });\r\n  }, [questionGroups, ungroupedQuestions, questions, contextType]);\r\n\r\n  // Toggle group expansion\r\n  const toggleGroupExpansion = (groupId: number) => {\r\n    setExpandedGroups((prev) => ({\r\n      ...prev,\r\n      [groupId]: !prev[groupId],\r\n    }));\r\n  };\r\n\r\n  const handleInputChange = (questionId: number, value: any) => {\r\n    setAnswers((prev) => ({\r\n      ...prev,\r\n      [questionId]: value,\r\n    }));\r\n    setErrors((prev) => ({\r\n      ...prev,\r\n      [questionId]: \"\",\r\n    }));\r\n  };\r\n\r\n  // Render a single question with its input\r\n  const renderQuestion = (question: Question) => (\r\n    <div\r\n      key={question.id}\r\n      className=\"border border-neutral-500 dark:border-neutral-700 rounded-md p-4\"\r\n    >\r\n      <div className=\"mb-2\">\r\n        <Label className=\"text-base font-medium\">\r\n          {question.label}\r\n          {question.isRequired && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </Label>\r\n        {question.hint && (\r\n          <p className=\"text-sm text-muted-foreground mt-1\">{question.hint}</p>\r\n        )}\r\n        {errors[question.id] && (\r\n          <p className=\"text-sm text-red-500 mt-1\">{errors[question.id]}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"mt-2\">{renderQuestionInput(question)}</div>\r\n    </div>\r\n  );\r\n\r\n  const renderQuestionInput = (question: Question) => {\r\n    const value =\r\n      answers[question.id] ?? (question.inputType === \"selectmany\" ? [] : \"\");\r\n\r\n    switch (question.inputType) {\r\n      case \"text\":\r\n        if (question.hint?.includes(\"multiline\")) {\r\n          return (\r\n            <Textarea\r\n              value={value}\r\n              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>\r\n                handleInputChange(question.id, e.target.value)\r\n              }\r\n              placeholder={question.hint || \"Your answer\"}\r\n              required={question.isRequired}\r\n            />\r\n          );\r\n        }\r\n        return (\r\n          <Input\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.hint || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"number\":\r\n        return (\r\n          <Input\r\n            type=\"number\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.hint || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"decimal\":\r\n        return (\r\n          <Input\r\n            type=\"decimal\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.hint || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"selectone\":\r\n        return (\r\n          <RadioGroup\r\n            value={value}\r\n            onValueChange={(val: string) => handleInputChange(question.id, val)}\r\n            required={question.isRequired}\r\n          >\r\n            <div className=\"space-y-2\">\r\n              {question.questionOptions?.map((option, index) => (\r\n                <div key={index} className=\"flex items-center space-x-2\">\r\n                  <RadioGroupItem\r\n                    value={option.label}\r\n                    id={`option-${option.id}`}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`option-${option.id}`}\r\n                    className=\"cursor-pointer\"\r\n                  >\r\n                    {option.label}\r\n                  </Label>\r\n                  {option.sublabel && (\r\n                    <p className=\"text-sm text-neutral-700 ml-4\">\r\n                      {`(${option.sublabel})`}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </RadioGroup>\r\n        );\r\n\r\n      case \"selectmany\":\r\n        return (\r\n          <div className=\"space-y-2\">\r\n            {question.questionOptions?.map((option) => (\r\n              <div key={option.id} className=\"flex items-center space-x-2\">\r\n                <Checkbox\r\n                  className=\"w-5 h-5 border border-neutral-500\"\r\n                  id={`option-${option.id}`}\r\n                  checked={(value || []).includes(option.label)}\r\n                  onCheckedChange={(checked) => {\r\n                    const currentValues = value || [];\r\n                    const newValues = checked\r\n                      ? [...currentValues, option.label]\r\n                      : currentValues.filter((v: string) => v !== option.label);\r\n                    handleInputChange(question.id, newValues);\r\n                  }}\r\n                />\r\n                <Label\r\n                  htmlFor={`option-${option.id}`}\r\n                  className=\"cursor-pointer\"\r\n                >\r\n                  {option.label} {option.sublabel}\r\n                </Label>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        );\r\n\r\n      case \"date\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <Input\r\n              type=\"date\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.hint || \"Select date\"}\r\n              required={question.isRequired}\r\n            />\r\n            <Calendar className=\"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\r\n          </div>\r\n        );\r\n\r\n      case \"dateandtime\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <Input\r\n              type=\"time\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.hint || \"Select time\"}\r\n              required={question.isRequired}\r\n            />\r\n            <Clock className=\"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\r\n          </div>\r\n        );\r\n\r\n      case \"table\":\r\n        return (\r\n          <TableInput\r\n            questionId={question.id}\r\n            value={value}\r\n            onChange={(cellValues) =>\r\n              handleInputChange(question.id, cellValues)\r\n            }\r\n            required={question.isRequired}\r\n            tableLabel={question.label}\r\n          />\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700\">\r\n      <div className=\"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700\">\r\n        <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\r\n          <ChevronLeft className=\"h-5 w-5\" />\r\n        </Button>\r\n        <h2 className=\"text-lg font-semibold\">Form Preview</h2>\r\n        <Button\r\n          className=\"cursor-pointer hover:bg-neutral-200\"\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          onClick={onClose}\r\n        >\r\n          <X className=\"h-5 w-5\" />\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"p-4 md:p-6\">\r\n        <div className=\"space-y-6\">\r\n          {questions.length === 0 ? (\r\n            <div className=\"text-center py-12\">\r\n              <p className=\"text-muted-foreground\">\r\n                This form has no questions yet.\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            // Render unified form items (groups and individual questions) in order\r\n            unifiedFormItems.map((item) => {\r\n              if (item.type === \"group\") {\r\n                const group = item.data as QuestionGroup;\r\n                const groupQuestions = groupedQuestions[group.id] || [];\r\n                const visibleGroupQuestions = groupQuestions.filter((q) =>\r\n                  visibleQuestions.some((vq) => vq.id === q.id)\r\n                );\r\n                const isExpanded = expandedGroups[group.id];\r\n\r\n                if (visibleGroupQuestions.length === 0) return null;\r\n\r\n                return (\r\n                  <div\r\n                    key={`group-${group.id}`}\r\n                    className=\"border border-neutral-500 dark:border-neutral-600 rounded-lg bg-neutral-100 dark:bg-neutral-800 overflow-hidden\"\r\n                  >\r\n                    {/* Group Header */}\r\n                    <div\r\n                      className=\"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700 cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700\"\r\n                      onClick={() => toggleGroupExpansion(group.id)}\r\n                    >\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        {isExpanded ? (\r\n                          <ChevronDown className=\"h-5 w-5 text-neutral-500\" />\r\n                        ) : (\r\n                          <ChevronRight className=\"h-5 w-5 text-neutral-500\" />\r\n                        )}\r\n                        <h3 className=\"text-lg font-semibold  dark:text-neutral-100\">\r\n                          {group.title}\r\n                        </h3>\r\n                        <span className=\"text-sm text-neutral-700 dark:text-neutral-400\">\r\n                          ({visibleGroupQuestions.length} visible question\r\n                          {visibleGroupQuestions.length !== 1 ? \"s\" : \"\"})\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Group Content with Nested Questions */}\r\n                    {isExpanded && (\r\n                      <div className=\"p-4 space-y-4\">\r\n                        {nestedQuestions\r\n                          .filter((nq) =>\r\n                            groupQuestions.some(\r\n                              (gq) => gq.id === nq.question.id\r\n                            )\r\n                          )\r\n                          .map((questionGroup) => (\r\n                            <NestedQuestionRenderer\r\n                              key={questionGroup.question.id}\r\n                              questionGroup={questionGroup}\r\n                              renderQuestionInput={renderQuestionInput}\r\n                              errors={errors}\r\n                              className=\"\"\r\n                            />\r\n                          ))}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              } else {\r\n                const question = item.data as Question;\r\n                return renderQuestion(question);\r\n              }\r\n            })\r\n          )}\r\n\r\n          {questions.length > 0 && visibleQuestions.length === 0 && (\r\n            <div className=\"text-center py-12\">\r\n              <p className=\"text-muted-foreground\">\r\n                No questions are currently visible. Please check your form\r\n                configuration.\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAMA;;;AAzBA;;;;;;;;;;;;AAoCO,SAAS,YAAY,EAC1B,SAAS,EACT,iBAAiB,EAAE,EACnB,cAAc,SAAS,EACvB,OAAO,EACP,QAAQ,EACS;;IACjB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAUnD,EAAE;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD,CAAC;IAGH,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,iBAAsC,CAAC;YAC7C,UAAU,OAAO;yCAAC,CAAC;oBACjB,cAAc,CAAC,SAAS,EAAE,CAAC,GACzB,SAAS,SAAS,KAAK,eAAe,EAAE,GAAG;gBAC/C;;YACA,WAAW;QACb;gCAAG;QAAC;KAAU;IAEd,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW;gBACb,MAAM,sBAAsB,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;gBAC3D,oBAAoB;gBAEpB,sCAAsC;gBACtC,MAAM,qBAAqB,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;gBACzD,mBAAmB;gBAEnB,4DAA4D;gBAC5D,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS;gBACrD,IAAI,OAAO,IAAI,CAAC,gBAAgB,MAAM,KAAK,OAAO,IAAI,CAAC,SAAS,MAAM,EAAE;oBACtE,WAAW;gBACb;YACF;QACF;gCAAG;QAAC;QAAW;KAAQ;IAEvB,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,uBAAgD,CAAC;YACvD,eAAe,OAAO;yCAAC,CAAC;oBACtB,oBAAoB,CAAC,MAAM,EAAE,CAAC,GAAG;gBACnC;;YACA,kBAAkB;QACpB;gCAAG;QAAC;KAAe;IAEnB,oCAAoC;IACpC,MAAM,mBAAmB,eAAe,MAAM,CAC5C,CAAC,KAAiC;QAChC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE;QACtE,OAAO;IACT,GACA,CAAC;IAGH,0BAA0B;IAC1B,MAAM,qBAAqB,UAAU,MAAM,CACzC,CAAC,IAAM,EAAE,eAAe,KAAK,QAAQ,EAAE,eAAe,KAAK;IAG7D,6FAA6F;IAC7F,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,OAAO;iDAAC;YACrC,MAAM,QAKD,EAAE;YAEP,2EAA2E;YAC3E,IAAI,gBAAgB,WAAW;gBAC7B,eAAe,OAAO;6DAAC,CAAC;wBACtB,kEAAkE;wBAClE,MAAM,iBAAiB,UAAU,MAAM;oFACrC,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE;;wBAEvC,MAAM,sBACJ,eAAe,MAAM,GAAG,IACpB,KAAK,GAAG,IAAI,eAAe,GAAG;qEAAC,CAAC,IAAM,EAAE,QAAQ;uEAChD,MAAM,KAAK;wBAEjB,MAAM,IAAI,CAAC;4BACT,MAAM;4BACN,MAAM;4BACN,OAAO;4BACP,kBAAkB;wBACpB;oBACF;;YACF;YAEA,uEAAuE;YACvE,MAAM,iBACJ,gBAAgB,YAAY,qBAAqB;YACnD,eAAe,OAAO;yDAAC,CAAC;oBACtB,MAAM,IAAI,CAAC;wBACT,MAAM;wBACN,MAAM;wBACN,OAAO,SAAS,QAAQ;wBACxB,kBAAkB,SAAS,QAAQ;oBACrC;gBACF;;YAEA,6DAA6D;YAC7D,OAAO,MAAM,IAAI;yDAAC,CAAC,GAAG;oBACpB,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;wBACvB,OACE,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK;oBAEpE;oBACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1B;;QACF;gDAAG;QAAC;QAAgB;QAAoB;QAAW;KAAY;IAE/D,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB,CAAC,OAAS,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC,YAAoB;QAC7C,WAAW,CAAC,OAAS,CAAC;gBACpB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;QACD,UAAU,CAAC,OAAS,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,0CAA0C;IAC1C,MAAM,iBAAiB,CAAC,yBACtB,6LAAC;YAEC,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6HAAA,CAAA,QAAK;4BAAC,WAAU;;gCACd,SAAS,KAAK;gCACd,SAAS,UAAU,kBAAI,6LAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;wBAE7D,SAAS,IAAI,kBACZ,6LAAC;4BAAE,WAAU;sCAAsC,SAAS,IAAI;;;;;;wBAEjE,MAAM,CAAC,SAAS,EAAE,CAAC,kBAClB,6LAAC;4BAAE,WAAU;sCAA6B,MAAM,CAAC,SAAS,EAAE,CAAC;;;;;;;;;;;;8BAGjE,6LAAC;oBAAI,WAAU;8BAAQ,oBAAoB;;;;;;;WAftC,SAAS,EAAE;;;;;IAmBpB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QACJ,OAAO,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,KAAK,eAAe,EAAE,GAAG,EAAE;QAExE,OAAQ,SAAS,SAAS;YACxB,KAAK;gBACH,IAAI,SAAS,IAAI,EAAE,SAAS,cAAc;oBACxC,qBACE,6LAAC,gIAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,UAAU,CAAC,IACT,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAE/C,aAAa,SAAS,IAAI,IAAI;wBAC9B,UAAU,SAAS,UAAU;;;;;;gBAGnC;gBACA,qBACE,6LAAC,6HAAA,CAAA,QAAK;oBACJ,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,IAAI,IAAI;oBAC9B,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC,6HAAA,CAAA,QAAK;oBACJ,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,IAAI,IAAI;oBAC9B,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC,6HAAA,CAAA,QAAK;oBACJ,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,IAAI,IAAI;oBAC9B,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC,sIAAA,CAAA,aAAU;oBACT,OAAO;oBACP,eAAe,CAAC,MAAgB,kBAAkB,SAAS,EAAE,EAAE;oBAC/D,UAAU,SAAS,UAAU;8BAE7B,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,eAAe,EAAE,IAAI,CAAC,QAAQ,sBACtC,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC,sIAAA,CAAA,iBAAc;wCACb,OAAO,OAAO,KAAK;wCACnB,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;;;;;;kDAE3B,6LAAC,6HAAA,CAAA,QAAK;wCACJ,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;wCAC9B,WAAU;kDAET,OAAO,KAAK;;;;;;oCAEd,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDACV,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;;;;;;;+BAbnB;;;;;;;;;;;;;;;YAsBpB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACZ,SAAS,eAAe,EAAE,IAAI,CAAC,uBAC9B,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC,gIAAA,CAAA,WAAQ;oCACP,WAAU;oCACV,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCACzB,SAAS,CAAC,SAAS,EAAE,EAAE,QAAQ,CAAC,OAAO,KAAK;oCAC5C,iBAAiB,CAAC;wCAChB,MAAM,gBAAgB,SAAS,EAAE;wCACjC,MAAM,YAAY,UACd;+CAAI;4CAAe,OAAO,KAAK;yCAAC,GAChC,cAAc,MAAM,CAAC,CAAC,IAAc,MAAM,OAAO,KAAK;wCAC1D,kBAAkB,SAAS,EAAE,EAAE;oCACjC;;;;;;8CAEF,6LAAC,6HAAA,CAAA,QAAK;oCACJ,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCAC9B,WAAU;;wCAET,OAAO,KAAK;wCAAC;wCAAE,OAAO,QAAQ;;;;;;;;2BAjBzB,OAAO,EAAE;;;;;;;;;;YAwB3B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6HAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4BAC9D,aAAa,SAAS,IAAI,IAAI;4BAC9B,UAAU,SAAS,UAAU;;;;;;sCAE/B,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;YAI1B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6HAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4BAC9D,aAAa,SAAS,IAAI,IAAI;4BAC9B,UAAU,SAAS,UAAU;;;;;;sCAE/B,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;YAIvB,KAAK;gBACH,qBACE,6LAAC,8IAAA,CAAA,aAAU;oBACT,YAAY,SAAS,EAAE;oBACvB,OAAO;oBACP,UAAU,CAAC,aACT,kBAAkB,SAAS,EAAE,EAAE;oBAEjC,UAAU,SAAS,UAAU;oBAC7B,YAAY,SAAS,KAAK;;;;;;YAIhC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,SAAS;kCAC3C,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,6LAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,6LAAC,8HAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAQ;wBACR,MAAK;wBACL,SAAS;kCAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,UAAU,MAAM,KAAK,kBACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;mCAKvC,uEAAuE;wBACvE,iBAAiB,GAAG,CAAC,CAAC;4BACpB,IAAI,KAAK,IAAI,KAAK,SAAS;gCACzB,MAAM,QAAQ,KAAK,IAAI;gCACvB,MAAM,iBAAiB,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;gCACvD,MAAM,wBAAwB,eAAe,MAAM,CAAC,CAAC,IACnD,iBAAiB,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,EAAE,EAAE;gCAE9C,MAAM,aAAa,cAAc,CAAC,MAAM,EAAE,CAAC;gCAE3C,IAAI,sBAAsB,MAAM,KAAK,GAAG,OAAO;gCAE/C,qBACE,6LAAC;oCAEC,WAAU;;sDAGV,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,qBAAqB,MAAM,EAAE;sDAE5C,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,2BACC,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEAE1B,6LAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAEd,6LAAC;wDAAK,WAAU;;4DAAiD;4DAC7D,sBAAsB,MAAM;4DAAC;4DAC9B,sBAAsB,MAAM,KAAK,IAAI,MAAM;4DAAG;;;;;;;;;;;;;;;;;;wCAMpD,4BACC,6LAAC;4CAAI,WAAU;sDACZ,gBACE,MAAM,CAAC,CAAC,KACP,eAAe,IAAI,CACjB,CAAC,KAAO,GAAG,EAAE,KAAK,GAAG,QAAQ,CAAC,EAAE,GAGnC,GAAG,CAAC,CAAC,8BACJ,6LAAC,0JAAA,CAAA,UAAsB;oDAErB,eAAe;oDACf,qBAAqB;oDACrB,QAAQ;oDACR,WAAU;mDAJL,cAAc,QAAQ,CAAC,EAAE;;;;;;;;;;;mCAnCnC,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;;;;;4BA8C9B,OAAO;gCACL,MAAM,WAAW,KAAK,IAAI;gCAC1B,OAAO,eAAe;4BACxB;wBACF;wBAGD,UAAU,MAAM,GAAG,KAAK,iBAAiB,MAAM,KAAK,mBACnD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;GA5agB;KAAA", "debugId": null}}, {"offset": {"line": 2182, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-preview/index.ts"], "sourcesContent": ["export { FormPreview } from './form-preview'; "], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2203, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/api/form-builder.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { ContextType } from \"@/types\";\r\n\r\nconst getQuestionsEndPoint = (contextType: ContextType) => {\r\n  if (contextType === \"project\") return \"/questions\";\r\n  else if (contextType === \"template\") return \"/template-questions\";\r\n  else if (contextType === \"questionBlock\") return \"/question-blocks\";\r\n  throw new Error(\"Unsupported context type\");\r\n};\r\n\r\nconst fetchQuestions = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/questions/${projectId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst fetchTemplateQuestions = async ({\r\n  templateId,\r\n}: {\r\n  templateId: number;\r\n}) => {\r\n  const { data } = await axios.get(`/template-questions/${templateId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst addQuestion = async ({\r\n  contextType,\r\n  contextId,\r\n  dataToSend,\r\n  position,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint?: string;\r\n    placeholder?: string;\r\n    inputType: string;\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n    }[];\r\n  };\r\n  position?: number;\r\n}) => {\r\n  // For question blocks, we don't need to include the contextId in the URL\r\n  // The userId is taken from the authenticated user in the backend\r\n  const url =\r\n    contextType === \"questionBlock\"\r\n      ? `${getQuestionsEndPoint(contextType)}`\r\n      : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n  const { data } = await axios.post(url, {\r\n    ...dataToSend,\r\n    position: position || 1,\r\n  });\r\n  return data;\r\n};\r\n\r\nconst deleteQuestion = async ({\r\n  contextType,\r\n  id,\r\n  projectId,\r\n}: {\r\n  contextType: ContextType;\r\n  id: number;\r\n  projectId: number;\r\n}) => {\r\n  const { data } = await axios.delete(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`\r\n  );\r\n  return data;\r\n};\r\n\r\nconst duplicateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n}) => {\r\n  // For question blocks, we don't need to send the contextId in the body\r\n  // The userId is taken from the authenticated user in the backend\r\n  const requestBody =\r\n    contextType === \"questionBlock\"\r\n      ? {}\r\n      : contextType === \"project\"\r\n        ? { projectId: contextId }\r\n        : { templateId: contextId };\r\n\r\n  const { data } = await axios.post(\r\n    `${getQuestionsEndPoint(\r\n      contextType\r\n    )}/duplicate/${id}?projectId=${contextId}`,\r\n    requestBody\r\n  );\r\n\r\n  return data;\r\n};\r\n\r\nconst updateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  dataToSend,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint: string;\r\n    placeholder: string;\r\n    position?: number; // Optional position field to preserve question order\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n    }[];\r\n  };\r\n  contextId: number;\r\n}) => {\r\n  const { data } = await axios.patch(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`,\r\n    dataToSend\r\n  );\r\n  return data;\r\n};\r\n\r\nconst fetchQuestionBlockQuestions = async () => {\r\n  try {\r\n    const response = await axios.get(`/question-blocks`);\r\n    return response.data.questions || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching question block questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst updateQuestionPositions = async ({\r\n  contextType,\r\n  contextId,\r\n  questionPositions,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  questionPositions: { id: number; position: number }[];\r\n}) => {\r\n  // Only support position updates for projects currently\r\n  if (contextType !== \"project\") {\r\n    throw new Error(\"Question position updates are only supported for projects\");\r\n  }\r\n\r\n  const url = `${getQuestionsEndPoint(contextType)}/positions?projectId=${contextId}`;\r\n  const payload = { questionPositions };\r\n\r\n  try {\r\n    const { data } = await axios.patch(url, payload);\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Update failed - Full error:\", error);\r\n    console.error(\"Update failed - Error details:\", {\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n      config: {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        data: error.config?.data,\r\n      },\r\n    });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport {\r\n  fetchQuestions,\r\n  fetchTemplateQuestions,\r\n  addQuestion,\r\n  deleteQuestion,\r\n  duplicateQuestion,\r\n  updateQuestion,\r\n  fetchQuestionBlockQuestions,\r\n  updateQuestionPositions,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,gBAAgB,WAAW,OAAO;SACjC,IAAI,gBAAgB,YAAY,OAAO;SACvC,IAAI,gBAAgB,iBAAiB,OAAO;IACjD,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,iBAAiB,OAAO,EAAE,SAAS,EAAyB;IAChE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;IAC1D,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,yBAAyB,OAAO,EACpC,UAAU,EAGX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY;IACpE,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,cAAc,OAAO,EACzB,WAAW,EACX,SAAS,EACT,UAAU,EACV,QAAQ,EAiBT;IACC,yEAAyE;IACzE,iEAAiE;IACjE,MAAM,MACJ,gBAAgB,kBACZ,GAAG,qBAAqB,cAAc,GACtC,GAAG,qBAAqB,aAAa,CAAC,EAAE,WAAW;IAEzD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK;QACrC,GAAG,UAAU;QACb,UAAU,YAAY;IACxB;IACA,OAAO;AACT;AAEA,MAAM,iBAAiB,OAAO,EAC5B,WAAW,EACX,EAAE,EACF,SAAS,EAKV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CACjC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW;IAErE,OAAO;AACT;AAEA,MAAM,oBAAoB,OAAO,EAC/B,EAAE,EACF,WAAW,EACX,SAAS,EAKV;IACC,uEAAuE;IACvE,iEAAiE;IACjE,MAAM,cACJ,gBAAgB,kBACZ,CAAC,IACD,gBAAgB,YACd;QAAE,WAAW;IAAU,IACvB;QAAE,YAAY;IAAU;IAEhC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,qBACD,aACA,WAAW,EAAE,GAAG,WAAW,EAAE,WAAW,EAC1C;IAGF,OAAO;AACT;AAEA,MAAM,iBAAiB,OAAO,EAC5B,EAAE,EACF,WAAW,EACX,UAAU,EACV,SAAS,EAiBV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW,EACnE;IAEF,OAAO;AACT;AAEA,MAAM,8BAA8B;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC;QACnD,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAEA,MAAM,0BAA0B,OAAO,EACrC,WAAW,EACX,SAAS,EACT,iBAAiB,EAKlB;IACC,uDAAuD;IACvD,IAAI,gBAAgB,WAAW;QAC7B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,MAAM,GAAG,qBAAqB,aAAa,qBAAqB,EAAE,WAAW;IACnF,MAAM,UAAU;QAAE;IAAkB;IAEpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,KAAK;QACxC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,QAAQ,KAAK,CAAC,kCAAkC;YAC9C,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,MAAM,MAAM,QAAQ,EAAE;YACtB,SAAS,MAAM,OAAO;YACtB,QAAQ;gBACN,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,MAAM,MAAM,MAAM,EAAE;YACtB;QACF;QACA,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 2305, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/api/question-groups.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\n/**\r\n * Fetch all question groups for a project\r\n */\r\nexport const fetchQuestionGroups = async ({ projectId }: { projectId: number }) => {\r\n  try {\r\n    // Use the project endpoint to fetch the project with its question groups\r\n    const { data } = await axios.get(`/projects/form/${projectId}`);\r\n    console.log(\"Fetched project with question groups:\", data);\r\n\r\n    // Extract question groups from the project data\r\n    const questionGroups = data.data?.project?.questionGroup || [];\r\n    console.log(\"Extracted question groups:\", questionGroups);\r\n\r\n    return questionGroups;\r\n  } catch (error) {\r\n    console.error(\"Error fetching question groups from project endpoint:\", error);\r\n\r\n    // Fallback to direct question groups endpoint\r\n    try {\r\n      console.log(\"Trying fallback to direct question groups endpoint\");\r\n      const { data } = await axios.post(`/question-groups`, { projectId });\r\n      console.log(\"Fallback response:\", data);\r\n      return data.data?.projectGroup || [];\r\n    } catch (fallbackError) {\r\n      console.error(\"Error in fallback fetch:\", fallbackError);\r\n\r\n      // Last resort: create a dummy group for debugging\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.log(\"Creating dummy group for debugging\");\r\n        return [];\r\n      }\r\n\r\n      return [];\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Create a new question group\r\n */\r\nexport const createQuestionGroup = async ({\r\n  title,\r\n  order,\r\n  projectId,\r\n  selectedQuestionIds,\r\n}: {\r\n  title: string;\r\n  order: number;\r\n  projectId: number;\r\n  selectedQuestionIds?: number[];\r\n}) => {\r\n  try {\r\n    console.log(\"Creating question group with data:\", {\r\n      title,\r\n      order,\r\n      projectId,\r\n      selectedQuestionIds\r\n    });\r\n\r\n    const { data } = await axios.post(`/question-groups`, {\r\n      title,\r\n      order,\r\n      projectId,\r\n      selectedQuestionIds: selectedQuestionIds || [],\r\n    });\r\n\r\n    console.log(\"Create question group response:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error creating question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Update an existing question group\r\n */\r\nexport const updateQuestionGroup = async ({\r\n  id,\r\n  title,\r\n  order,\r\n  selectedQuestionIds,\r\n}: {\r\n  id: number;\r\n  title: string;\r\n  order: number;\r\n  selectedQuestionIds?: number[];\r\n}) => {\r\n  try {\r\n    console.log(\"Updating question group with data:\", {\r\n      id,\r\n      title,\r\n      order,\r\n      selectedQuestionIds\r\n    });\r\n\r\n    const { data } = await axios.patch(`/question-groups`, {\r\n      id,\r\n      title,\r\n      order,\r\n      selectedQuestionIds,\r\n    });\r\n\r\n    console.log(\"Update question group response:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Delete a question group\r\n */\r\nexport const deleteQuestionGroup = async ({ id }: { id: number }) => {\r\n  try {\r\n    console.log(`Deleting question group with ID: ${id}`);\r\n    const { data } = await axios.delete(`/question-groups/${id}`);\r\n    console.log(\"Delete question group response:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Delete a question group and all its questions\r\n */\r\nexport const deleteQuestionAndGroup = async ({ id }: { id: number }) => {\r\n  try {\r\n    console.log(`Deleting question group and its questions with ID: ${id}`);\r\n    const { data } = await axios.delete(`/question-groups/group/question/${id}`);\r\n    console.log(\"Delete question group and questions response:\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting question group and questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Remove a question from a group\r\n */\r\nexport const removeQuestionFromGroup = async ({\r\n  groupId,\r\n  questionId,\r\n}: {\r\n  groupId: number;\r\n  questionId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/question/remove`, {\r\n    groupId,\r\n    questionId,\r\n  });\r\n  return data;\r\n};\r\n\r\n/**\r\n * Move a question from one group to another\r\n */\r\nexport const moveQuestionBetweenGroups = async ({\r\n  groupId,\r\n  newGroupId,\r\n  questionId,\r\n}: {\r\n  groupId: number;\r\n  newGroupId: number;\r\n  questionId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/question/move`, {\r\n    groupId,\r\n    newGroupId,\r\n    questionId,\r\n  });\r\n  return data;\r\n};\r\n\r\n// Parent group functionality removed for simplicity\r\n"], "names": [], "mappings": ";;;;;;;;;AA6BU;AA7BV;;AAKO,MAAM,sBAAsB,OAAO,EAAE,SAAS,EAAyB;IAC5E,IAAI;QACF,yEAAyE;QACzE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;QAC9D,QAAQ,GAAG,CAAC,yCAAyC;QAErD,gDAAgD;QAChD,MAAM,iBAAiB,KAAK,IAAI,EAAE,SAAS,iBAAiB,EAAE;QAC9D,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yDAAyD;QAEvE,8CAA8C;QAC9C,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;gBAAE;YAAU;YAClE,QAAQ,GAAG,CAAC,sBAAsB;YAClC,OAAO,KAAK,IAAI,EAAE,gBAAgB,EAAE;QACtC,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,kDAAkD;YAClD,wCAA4C;gBAC1C,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;;QAGF;IACF;AACF;AAKO,MAAM,sBAAsB,OAAO,EACxC,KAAK,EACL,KAAK,EACL,SAAS,EACT,mBAAmB,EAMpB;IACC,IAAI;QACF,QAAQ,GAAG,CAAC,sCAAsC;YAChD;YACA;YACA;YACA;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACpD;YACA;YACA;YACA,qBAAqB,uBAAuB,EAAE;QAChD;QAEA,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,sBAAsB,OAAO,EACxC,EAAE,EACF,KAAK,EACL,KAAK,EACL,mBAAmB,EAMpB;IACC,IAAI;QACF,QAAQ,GAAG,CAAC,sCAAsC;YAChD;YACA;YACA;YACA;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACrD;YACA;YACA;YACA;QACF;QAEA,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,sBAAsB,OAAO,EAAE,EAAE,EAAkB;IAC9D,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,IAAI;QACpD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,IAAI;QAC5D,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,yBAAyB,OAAO,EAAE,EAAE,EAAkB;IACjE,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,IAAI;QACtE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,gCAAgC,EAAE,IAAI;QAC3E,QAAQ,GAAG,CAAC,iDAAiD;QAC7D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;AACF;AAKO,MAAM,0BAA0B,OAAO,EAC5C,OAAO,EACP,UAAU,EAIX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;QACrE;QACA;IACF;IACA,OAAO;AACT;AAKO,MAAM,4BAA4B,OAAO,EAC9C,OAAO,EACP,UAAU,EACV,UAAU,EAKX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE;QACnE;QACA;QACA;IACF;IACA,OAAO;AACT,GAEA,oDAAoD", "debugId": null}}, {"offset": {"line": 2435, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-builder/QuestionItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { useSortable } from \"@dnd-kit/sortable\";\r\nimport { CSS } from \"@dnd-kit/utilities\";\r\nimport { Question } from \"@/types/formBuilder\";\r\n\r\nimport { GripVertical, Trash, Copy, Pencil, CopyPlus } from \"lucide-react\";\r\nimport { TemplateQuestion } from \"@/types\";\r\n\r\ninterface QuestionItemProps {\r\n  question: Question | TemplateQuestion;\r\n  onEdit: () => void;\r\n  onDelete: () => void;\r\n  onDuplicate: () => void;\r\n  isSelected?: boolean;\r\n  onToggleSelect?: () => void;\r\n  selectionMode?: boolean;\r\n}\r\n\r\nconst QuestionItem = ({\r\n  question,\r\n  onEdit,\r\n  onDelete,\r\n  onDuplicate,\r\n  isSelected = false,\r\n  onToggleSelect,\r\n  selectionMode = false,\r\n}: QuestionItemProps) => {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n    isDragging,\r\n  } = useSortable({ id: question.id });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n    opacity: isDragging ? 0.5 : 1,\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className={\"border border-neutral-400 rounded-md bg-card shadow-sm\"}\r\n    >\r\n      <div className=\"flex items-center p-4\">\r\n        {selectionMode && (\r\n          <div className=\"mr-2\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={isSelected}\r\n              onChange={() => onToggleSelect && onToggleSelect()}\r\n              className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        <div\r\n          {...attributes}\r\n          {...listeners}\r\n          className=\"cursor-grab mr-3 hover:text-primary\"\r\n        >\r\n          <GripVertical className=\"h-5 w-5\" />\r\n        </div>\r\n\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-xl font-semibold\">\r\n            {question.label || (\r\n              <span className=\"text-muted-foreground italic\">\r\n                Empty question\r\n              </span>\r\n            )}\r\n          </h3>\r\n          {question.hint && (\r\n            <p className=\"text-sm sub-text mt-1\">{question.hint}</p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-1\">\r\n          <button\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onDuplicate();\r\n            }}\r\n            title=\"Duplicate\"\r\n            className=\"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n          >\r\n            <CopyPlus size={16} />\r\n          </button>\r\n          <button\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onDelete();\r\n            }}\r\n            title=\"Delete\"\r\n            className=\"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors\"\r\n          >\r\n            <Trash size={16} />\r\n          </button>\r\n          <button\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onEdit();\r\n            }}\r\n            title=\"Edit\"\r\n            className=\"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n          >\r\n            <Pencil size={16} />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { QuestionItem };\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAGA;AAAA;AAAA;AAAA;;;AAPA;;;;AAoBA,MAAM,eAAe,CAAC,EACpB,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,WAAW,EACX,aAAa,KAAK,EAClB,cAAc,EACd,gBAAgB,KAAK,EACH;;IAClB,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,SAAS,EAAE;IAAC;IAElC,MAAM,QAAQ;QACZ,WAAW,wKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;QACA,SAAS,aAAa,MAAM;IAC9B;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,OAAO;QACP,WAAW;kBAEX,cAAA,6LAAC;YAAI,WAAU;;gBACZ,+BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU,IAAM,kBAAkB;wBAClC,WAAU;;;;;;;;;;;8BAKhB,6LAAC;oBACE,GAAG,UAAU;oBACb,GAAG,SAAS;oBACb,WAAU;8BAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;8BAG1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,SAAS,KAAK,kBACb,6LAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;wBAKlD,SAAS,IAAI,kBACZ,6LAAC;4BAAE,WAAU;sCAAyB,SAAS,IAAI;;;;;;;;;;;;8BAIvD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,OAAM;4BACN,WAAU;sCAEV,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;;;;;;sCAElB,6LAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,OAAM;4BACN,WAAU;sCAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,MAAM;;;;;;;;;;;sCAEf,6LAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,OAAM;4BACN,WAAU;sCAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;GAlGM;;QAgBA,sKAAA,CAAA,cAAW;;;KAhBX", "debugId": null}}, {"offset": {"line": 2629, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-builder/QuestionGroupItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { QuestionItem } from \"./QuestionItem\";\r\nimport { ChevronDown, ChevronRight, Edit, Trash, Plus } from \"lucide-react\";\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent,\r\n} from \"@dnd-kit/core\";\r\nimport {\r\n  SortableContext,\r\n  verticalListSortingStrategy,\r\n  arrayMove,\r\n} from \"@dnd-kit/sortable\";\r\n\r\ninterface QuestionGroupItemProps {\r\n  id: number;\r\n  title: string;\r\n  questions: Question[];\r\n  onEditGroup: (groupId: number) => void;\r\n  onDeleteGroup: (groupId: number) => void;\r\n  onAddQuestionToGroup: (groupId: number) => void;\r\n  onEditQuestion: (question: Question) => void;\r\n  onDeleteQuestion: (question: Question) => void;\r\n  onDuplicateQuestion: (question: Question) => void;\r\n  onReorderQuestions?: (questionPositions: { id: number; position: number }[]) => void;\r\n  isEditing?: boolean;\r\n  onStartEditing?: (groupId: number, currentName: string) => void;\r\n  onSaveGroupName?: (groupId: number) => void;\r\n  onCancelEditing?: () => void;\r\n  editingName?: string;\r\n  onEditingNameChange?: (name: string) => void;\r\n  selectionMode?: boolean;\r\n}\r\n\r\nconst QuestionGroupItem = ({\r\n  id,\r\n  title,\r\n  questions,\r\n  onEditGroup, // Kept for future use\r\n  onDeleteGroup,\r\n  onAddQuestionToGroup,\r\n  onEditQuestion,\r\n  onDeleteQuestion,\r\n  onDuplicateQuestion,\r\n  onReorderQuestions,\r\n  isEditing = false,\r\n  onStartEditing,\r\n  onSaveGroupName,\r\n  onCancelEditing,\r\n  editingName = \"\",\r\n  onEditingNameChange,\r\n  selectionMode = false,\r\n}: QuestionGroupItemProps) => {\r\n  const [isExpanded, setIsExpanded] = useState(true);\r\n\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor, {\r\n      activationConstraint: {\r\n        distance: 8,\r\n      },\r\n    }),\r\n    useSensor(KeyboardSensor)\r\n  );\r\n\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (!over || active.id === over.id || !onReorderQuestions) {\r\n      return;\r\n    }\r\n\r\n    // Sort questions by position to maintain order\r\n    const sortedQuestions = [...questions].sort((a, b) => a.position - b.position);\r\n\r\n    const oldIndex = sortedQuestions.findIndex((q) => q.id === active.id);\r\n    const newIndex = sortedQuestions.findIndex((q) => q.id === over.id);\r\n\r\n    if (oldIndex === -1 || newIndex === -1) {\r\n      return;\r\n    }\r\n\r\n    // Reorder the questions array\r\n    const reorderedQuestions = arrayMove(sortedQuestions, oldIndex, newIndex);\r\n\r\n    // Calculate new positions for all questions in this group\r\n    const questionPositions = reorderedQuestions.map((question, index) => ({\r\n      id: Number(question.id), // Ensure it's a number\r\n      position: index + 1, // Start positions from 1\r\n    }));\r\n\r\n    // Call the parent handler to update positions\r\n    onReorderQuestions(questionPositions);\r\n  };\r\n\r\n  return (\r\n    <div className=\"border border-neutral-400 rounded-md bg-card shadow-sm mb-4\">\r\n      {/* Group Header */}\r\n      <div className=\"flex items-center p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md\">\r\n        <button\r\n          onClick={() => setIsExpanded(!isExpanded)}\r\n          className=\"mr-2 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n          aria-label={isExpanded ? \"Collapse group\" : \"Expand group\"}\r\n        >\r\n          {isExpanded ? (\r\n            <ChevronDown className=\"h-5 w-5\" />\r\n          ) : (\r\n            <ChevronRight className=\"h-5 w-5\" />\r\n          )}\r\n        </button>\r\n\r\n        {isEditing ? (\r\n          <div className=\"flex-1 mr-4\">\r\n            <input\r\n              type=\"text\"\r\n              value={editingName}\r\n              onChange={(e) => onEditingNameChange && onEditingNameChange(e.target.value)}\r\n              className=\"w-full p-2 border border-gray-300 rounded\"\r\n              autoFocus\r\n              onKeyDown={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  onSaveGroupName && onSaveGroupName(id);\r\n                } else if (e.key === 'Escape') {\r\n                  onCancelEditing && onCancelEditing();\r\n                }\r\n              }}\r\n              placeholder=\"Enter group name\"\r\n            />\r\n          </div>\r\n        ) : (\r\n          <h3\r\n            className=\"flex-1 font-medium text-lg cursor-pointer hover:text-primary-500\"\r\n            onClick={() => onStartEditing && onStartEditing(id, title)}\r\n            title=\"Click to edit group name\"\r\n          >\r\n            {title}\r\n          </h3>\r\n        )}\r\n\r\n        <div className=\"flex items-center space-x-3\">\r\n          {isEditing ? (\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => onCancelEditing && onCancelEditing()}\r\n                title=\"Cancel Editing\"\r\n                className=\"cursor-pointer px-3 py-1 rounded btn-outline\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={() => onSaveGroupName && onSaveGroupName(id)}\r\n                title=\"Save Group Name\"\r\n                className=\"cursor-pointer px-3 py-1 rounded btn-primary\"\r\n              >\r\n                Save\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <button\r\n                onClick={() => onAddQuestionToGroup(id)}\r\n                title=\"Add Question to Group\"\r\n                className=\"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n              >\r\n                <Plus size={16} />\r\n              </button>\r\n              <button\r\n                onClick={() => onStartEditing && onStartEditing(id, title)}\r\n                title=\"Edit Group Name\"\r\n                className=\"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n              >\r\n                <Edit size={16} />\r\n              </button>\r\n              <button\r\n                onClick={() => onDeleteGroup(id)}\r\n                title=\"Delete Group\"\r\n                className=\"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors\"\r\n              >\r\n                <Trash size={16} />\r\n              </button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Group Content */}\r\n      {isExpanded && (\r\n        <div className=\"p-4 space-y-4\">\r\n          {questions.length > 0 ? (\r\n            <DndContext\r\n              sensors={sensors}\r\n              collisionDetection={closestCenter}\r\n              onDragEnd={handleDragEnd}\r\n            >\r\n              <SortableContext\r\n                items={questions.map((question) => question.id)}\r\n                strategy={verticalListSortingStrategy}\r\n              >\r\n                {questions\r\n                  .sort((a, b) => a.position - b.position)\r\n                  .map((question) => (\r\n                    <div key={question.id} className=\"mb-4\">\r\n                      <QuestionItem\r\n                        question={question}\r\n                        onEdit={() => onEditQuestion(question)}\r\n                        onDelete={() => onDeleteQuestion(question)}\r\n                        onDuplicate={() => onDuplicateQuestion(question)}\r\n                        selectionMode={selectionMode}\r\n                        isSelected={false}\r\n                        onToggleSelect={() => {}}\r\n                      />\r\n                    </div>\r\n                  ))}\r\n              </SortableContext>\r\n            </DndContext>\r\n          ) : (\r\n            <div className=\"text-center py-4 text-neutral-500\">\r\n              No questions in this group. Click the + button to add questions.\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { QuestionGroupItem };\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AASA;;;AAfA;;;;;;AAyCA,MAAM,oBAAoB,CAAC,EACzB,EAAE,EACF,KAAK,EACL,SAAS,EACT,WAAW,EACX,aAAa,EACb,oBAAoB,EACpB,cAAc,EACd,gBAAgB,EAChB,mBAAmB,EACnB,kBAAkB,EAClB,YAAY,KAAK,EACjB,cAAc,EACd,eAAe,EACf,eAAe,EACf,cAAc,EAAE,EAChB,mBAAmB,EACnB,gBAAgB,KAAK,EACE;;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa,EAAE;QACvB,sBAAsB;YACpB,UAAU;QACZ;IACF,IACA,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,iBAAc;IAG1B,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,CAAC,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,IAAI,CAAC,oBAAoB;YACzD;QACF;QAEA,+CAA+C;QAC/C,MAAM,kBAAkB;eAAI;SAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAE7E,MAAM,WAAW,gBAAgB,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE;QACpE,MAAM,WAAW,gBAAgB,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;QAElE,IAAI,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;YACtC;QACF;QAEA,8BAA8B;QAC9B,MAAM,qBAAqB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,UAAU;QAEhE,0DAA0D;QAC1D,MAAM,oBAAoB,mBAAmB,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;gBACrE,IAAI,OAAO,SAAS,EAAE;gBACtB,UAAU,QAAQ;YACpB,CAAC;QAED,8CAA8C;QAC9C,mBAAmB;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,cAAc,CAAC;wBAC9B,WAAU;wBACV,cAAY,aAAa,mBAAmB;kCAE3C,2BACC,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;iDAEvB,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;oBAI3B,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,uBAAuB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BAC1E,WAAU;4BACV,SAAS;4BACT,WAAW,CAAC;gCACV,IAAI,EAAE,GAAG,KAAK,SAAS;oCACrB,mBAAmB,gBAAgB;gCACrC,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;oCAC7B,mBAAmB;gCACrB;4BACF;4BACA,aAAY;;;;;;;;;;6CAIhB,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB,eAAe,IAAI;wBACpD,OAAM;kCAEL;;;;;;kCAIL,6LAAC;wBAAI,WAAU;kCACZ,0BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,OAAM;oCACN,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,mBAAmB,gBAAgB;oCAClD,OAAM;oCACN,WAAU;8CACX;;;;;;;;;;;iDAKH;;8CACE,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,OAAM;oCACN,WAAU;8CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAEd,6LAAC;oCACC,SAAS,IAAM,kBAAkB,eAAe,IAAI;oCACpD,OAAM;oCACN,WAAU;8CAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAEd,6LAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,OAAM;oCACN,WAAU;8CAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;YAQtB,4BACC,6LAAC;gBAAI,WAAU;0BACZ,UAAU,MAAM,GAAG,kBAClB,6LAAC,8JAAA,CAAA,aAAU;oBACT,SAAS;oBACT,oBAAoB,8JAAA,CAAA,gBAAa;oBACjC,WAAW;8BAEX,cAAA,6LAAC,sKAAA,CAAA,kBAAe;wBACd,OAAO,UAAU,GAAG,CAAC,CAAC,WAAa,SAAS,EAAE;wBAC9C,UAAU,sKAAA,CAAA,8BAA2B;kCAEpC,UACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,EACtC,GAAG,CAAC,CAAC,yBACJ,6LAAC;gCAAsB,WAAU;0CAC/B,cAAA,6LAAC,iJAAA,CAAA,eAAY;oCACX,UAAU;oCACV,QAAQ,IAAM,eAAe;oCAC7B,UAAU,IAAM,iBAAiB;oCACjC,aAAa,IAAM,oBAAoB;oCACvC,eAAe;oCACf,YAAY;oCACZ,gBAAgB,KAAO;;;;;;+BARjB,SAAS,EAAE;;;;;;;;;;;;;;yCAe7B,6LAAC;oBAAI,WAAU;8BAAoC;;;;;;;;;;;;;;;;;AAQ/D;GA7LM;;QAqBY,8JAAA,CAAA,aAAU;;;KArBtB", "debugId": null}}, {"offset": {"line": 2914, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/constants/inputType.ts"], "sourcesContent": ["const inputTypeMapLiteral = {\r\n  text: \"Text\",\r\n  number: \"Number\",\r\n  decimal: \"Decimal\",\r\n  selectone: \"Select one\",\r\n  selectmany: \"Select many\",\r\n  date: \"Date\",\r\n  dateandtime: \"Date and time\",\r\n  table: \"Table\",\r\n} as const;\r\n\r\nexport const InputTypeMap: Record<string, string> = inputTypeMapLiteral;\r\n\r\nexport const InputTypeKeys = Object.keys(inputTypeMapLiteral) as Array<\r\n  keyof typeof inputTypeMapLiteral\r\n>;\r\n\r\nexport type InputType = (typeof InputTypeKeys)[number];\r\n"], "names": [], "mappings": ";;;;AAAA,MAAM,sBAAsB;IAC1B,MAAM;IACN,QAAQ;IACR,SAAS;IACT,WAAW;IACX,YAAY;IACZ,MAAM;IACN,aAAa;IACb,OAAO;AACT;AAEO,MAAM,eAAuC;AAE7C,MAAM,gBAAgB,OAAO,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2939, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-builder/QuestionSelector.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport {\r\n  fetchQuestions,\r\n  fetchTemplateQuestions,\r\n  fetchQuestionBlockQuestions,\r\n} from \"@/lib/api/form-builder\";\r\nimport { ContextType } from \"@/types\";\r\nimport { Question } from \"@/types/formBuilder\";\r\n\r\ninterface QuestionSelectorProps {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  value?: number | null;\r\n  onChange: (questionId: number | null) => void;\r\n  currentQuestionId?: number; // To exclude current question from options\r\n  placeholder?: string;\r\n}\r\n\r\nconst QuestionSelector: React.FC<QuestionSelectorProps> = ({\r\n  contextType,\r\n  contextId,\r\n  value,\r\n  onChange,\r\n  currentQuestionId,\r\n  placeholder = \"Select next question (optional)\",\r\n}) => {\r\n  // Fetch questions based on context type\r\n  const {\r\n    data: questions = [],\r\n    isLoading,\r\n    error,\r\n  } = useQuery<Question[]>({\r\n    queryKey:\r\n      contextType === \"project\"\r\n        ? [\"questions\", contextId]\r\n        : contextType === \"template\"\r\n        ? [\"templateQuestions\", contextId]\r\n        : [\"questionBlockQuestions\", contextId],\r\n    queryFn: () => {\r\n      if (contextType === \"project\") {\r\n        return fetchQuestions({ projectId: contextId });\r\n      } else if (contextType === \"template\") {\r\n        return fetchTemplateQuestions({ templateId: contextId });\r\n      } else if (contextType === \"questionBlock\") {\r\n        return fetchQuestionBlockQuestions();\r\n      }\r\n      return [];\r\n    },\r\n    enabled: !!contextId,\r\n  });\r\n\r\n\r\n  // Filter out current question to prevent self-reference\r\n  const availableQuestions = questions.filter(\r\n    (q) => q.id !== currentQuestionId\r\n  );\r\n\r\n  const selectedQuestion = availableQuestions.find((q) => q.id === value);\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-1\">\r\n      <select\r\n        value={value || \"\"}\r\n        onChange={(e) => {\r\n          const selectedValue = e.target.value;\r\n          onChange(selectedValue ? parseInt(selectedValue) : null);\r\n        }}\r\n        className=\"input-field text-sm\"\r\n        disabled={isLoading}\r\n      >\r\n        <option value=\"\">\r\n          {isLoading ? \"Loading questions...\" : placeholder}\r\n        </option>\r\n        {availableQuestions.map((question) => (\r\n          <option key={question.id} value={question.id}>\r\n            {question.label || `Question ${question.id}`}\r\n          </option>\r\n        ))}\r\n      </select>\r\n      {selectedQuestion && (\r\n        <div className=\"text-xs text-gray-500 mt-1\">\r\n          Type: {selectedQuestion.inputType}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { QuestionSelector };\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;;AAiBA,MAAM,mBAAoD,CAAC,EACzD,WAAW,EACX,SAAS,EACT,KAAK,EACL,QAAQ,EACR,iBAAiB,EACjB,cAAc,iCAAiC,EAChD;;IACC,wCAAwC;IACxC,MAAM,EACJ,MAAM,YAAY,EAAE,EACpB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UACE,gBAAgB,YACZ;YAAC;YAAa;SAAU,GACxB,gBAAgB,aAChB;YAAC;YAAqB;SAAU,GAChC;YAAC;YAA0B;SAAU;QAC3C,OAAO;yCAAE;gBACP,IAAI,gBAAgB,WAAW;oBAC7B,OAAO,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;wBAAE,WAAW;oBAAU;gBAC/C,OAAO,IAAI,gBAAgB,YAAY;oBACrC,OAAO,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE;wBAAE,YAAY;oBAAU;gBACxD,OAAO,IAAI,gBAAgB,iBAAiB;oBAC1C,OAAO,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD;gBACnC;gBACA,OAAO,EAAE;YACX;;QACA,SAAS,CAAC,CAAC;IACb;IAGA,wDAAwD;IACxD,MAAM,qBAAqB,UAAU,MAAM,CACzC,CAAC,IAAM,EAAE,EAAE,KAAK;IAGlB,MAAM,mBAAmB,mBAAmB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IAEjE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,OAAO,SAAS;gBAChB,UAAU,CAAC;oBACT,MAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oBACpC,SAAS,gBAAgB,SAAS,iBAAiB;gBACrD;gBACA,WAAU;gBACV,UAAU;;kCAEV,6LAAC;wBAAO,OAAM;kCACX,YAAY,yBAAyB;;;;;;oBAEvC,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC;4BAAyB,OAAO,SAAS,EAAE;sCACzC,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;2BADjC,SAAS,EAAE;;;;;;;;;;;YAK3B,kCACC,6LAAC;gBAAI,WAAU;;oBAA6B;oBACnC,iBAAiB,SAAS;;;;;;;;;;;;;AAK3C;GApEM;;QAaA,8KAAA,CAAA,WAAQ;;;KAbR", "debugId": null}}, {"offset": {"line": 3054, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-builder/DynamicOptions.tsx"], "sourcesContent": ["import { Plus, Trash } from \"lucide-react\";\r\nimport { useEffect } from \"react\";\r\nimport { useFieldArray, useFormContext } from \"react-hook-form\";\r\nimport { QuestionSelector } from \"./QuestionSelector\";\r\nimport { ContextType } from \"@/types\";\r\n\r\ninterface DynamicOptionsProps {\r\n  contextType?: ContextType;\r\n  contextId?: number;\r\n  currentQuestionId?: number;\r\n  inputType?: string;\r\n}\r\n\r\nconst DynamicOptions = ({\r\n  contextType,\r\n  contextId,\r\n  currentQuestionId,\r\n  inputType,\r\n}: DynamicOptionsProps) => {\r\n  const {\r\n    control,\r\n    register,\r\n    formState: { errors },\r\n    setValue,\r\n    watch,\r\n  } = useFormContext();\r\n  const { fields, append, remove } = useFieldArray({\r\n    control,\r\n    name: \"questionOptions\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (fields.length === 0) {\r\n      append({ label: \"\", sublabel: \"\", code: \"\", nextQuestionId: null });\r\n    }\r\n  }, [fields, append]);\r\n\r\n  // Check if current input type supports conditional questions\r\n  const supportsConditionalQuestions =\r\n    inputType === \"selectone\" || inputType === \"selectmany\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-2\">\r\n      <label className=\"label-text\">Options</label>\r\n      <div className=\"flex flex-col gap-2\">\r\n        {fields.map((field, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"border  border-gray-400 rounded-lg p-3 space-y-2\"\r\n          >\r\n            <div className=\"flex items-center gap-2\">\r\n              <input\r\n                {...register(`questionOptions.${index}.label`)}\r\n                placeholder={`Option ${index + 1}`}\r\n                className=\"input-field flex-1 min-w-[150px]\"\r\n              />\r\n\r\n              <input\r\n                {...register(`questionOptions.${index}.sublabel`)}\r\n                placeholder={`Sub Options`}\r\n                className=\"input-field flex-1 min-w-[150px]\"\r\n              />\r\n              <input\r\n                {...register(`questionOptions.${index}.code`)}\r\n                placeholder=\"Code\"\r\n                className=\"w-28 input-field\"\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => remove(index)}\r\n                className=\"p-2 rounded-full hover:bg-red-500/10 text-neutral-700 hover:text-red-500 transition-colors cursor-pointer duration-300\"\r\n              >\r\n                <Trash size={16} />\r\n              </button>\r\n            </div>\r\n\r\n            {/* Conditional Question Selector */}\r\n            {supportsConditionalQuestions && contextType && contextId && (\r\n              <div className=\"ml-2\">\r\n                <label className=\"text-xs text-gray-600 mb-1 block\">\r\n                  Next Question (when this option is selected):\r\n                </label>\r\n                <QuestionSelector\r\n                  contextType={contextType}\r\n                  contextId={contextId}\r\n                  currentQuestionId={currentQuestionId}\r\n                  value={watch(`questionOptions.${index}.nextQuestionId`)}\r\n                  onChange={(questionId) => {\r\n                    setValue(\r\n                      `questionOptions.${index}.nextQuestionId`,\r\n                      questionId\r\n                    );\r\n                  }}\r\n                  placeholder=\"No follow-up question\"\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n\r\n        {errors.questionOptions && (\r\n          <p className=\"text-sm text-red-500\">{`${errors.questionOptions.root?.message}`}</p>\r\n        )}\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => append({ label: \"\", sublabel: \"\", code: \"\", nextQuestionId: null })}\r\n          className=\"btn-outline mt-2 flex items-center justify-center gap-2\"\r\n        >\r\n          <Plus size={16} />\r\n          Add Option\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { DynamicOptions };\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAUA,MAAM,iBAAiB,CAAC,EACtB,WAAW,EACX,SAAS,EACT,iBAAiB,EACjB,SAAS,EACW;;IACpB,MAAM,EACJ,OAAO,EACP,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C;QACA,MAAM;IACR;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB,OAAO;oBAAE,OAAO;oBAAI,UAAU;oBAAI,MAAM;oBAAI,gBAAgB;gBAAK;YACnE;QACF;mCAAG;QAAC;QAAQ;KAAO;IAEnB,6DAA6D;IAC7D,MAAM,+BACJ,cAAc,eAAe,cAAc;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAM,WAAU;0BAAa;;;;;;0BAC9B,6LAAC;gBAAI,WAAU;;oBACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACE,GAAG,SAAS,CAAC,gBAAgB,EAAE,MAAM,MAAM,CAAC,CAAC;4CAC9C,aAAa,CAAC,OAAO,EAAE,QAAQ,GAAG;4CAClC,WAAU;;;;;;sDAGZ,6LAAC;4CACE,GAAG,SAAS,CAAC,gBAAgB,EAAE,MAAM,SAAS,CAAC,CAAC;4CACjD,aAAa,CAAC,WAAW,CAAC;4CAC1B,WAAU;;;;;;sDAEZ,6LAAC;4CACE,GAAG,SAAS,CAAC,gBAAgB,EAAE,MAAM,KAAK,CAAC,CAAC;4CAC7C,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,OAAO;4CACtB,WAAU;sDAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;;;;;;;gCAKhB,gCAAgC,eAAe,2BAC9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAmC;;;;;;sDAGpD,6LAAC,qJAAA,CAAA,mBAAgB;4CACf,aAAa;4CACb,WAAW;4CACX,mBAAmB;4CACnB,OAAO,MAAM,CAAC,gBAAgB,EAAE,MAAM,eAAe,CAAC;4CACtD,UAAU,CAAC;gDACT,SACE,CAAC,gBAAgB,EAAE,MAAM,eAAe,CAAC,EACzC;4CAEJ;4CACA,aAAY;;;;;;;;;;;;;2BA9Cb;;;;;oBAqDR,OAAO,eAAe,kBACrB,6LAAC;wBAAE,WAAU;kCAAwB,GAAG,OAAO,eAAe,CAAC,IAAI,EAAE,SAAS;;;;;;kCAEhF,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO;gCAAE,OAAO;gCAAI,UAAU;gCAAI,MAAM;gCAAI,gBAAgB;4BAAK;wBAChF,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAAM;;;;;;;;;;;;;;;;;;;AAM5B;GArGM;;QAYA,iKAAA,CAAA,iBAAc;QACiB,iKAAA,CAAA,gBAAa;;;KAb5C", "debugId": null}}, {"offset": {"line": 3263, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/lib/needsOptions.ts"], "sourcesContent": ["export const needsOptions = (inputType: string) => {\r\n  const multipleChoiceInputTypes = [\"selectone\", \"selectmany\"];\r\n  return multipleChoiceInputTypes.includes(inputType);\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,eAAe,CAAC;IAC3B,MAAM,2BAA2B;QAAC;QAAa;KAAa;IAC5D,OAAO,yBAAyB,QAAQ,CAAC;AAC3C", "debugId": null}}, {"offset": {"line": 3282, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/types/formBuilder.ts"], "sourcesContent": ["import { InputTypeMap, InputType } from \"@/constants/inputType\";\r\nimport { needsOptions } from \"@/lib/needsOptions\";\r\nimport { z } from \"zod\";\r\n\r\nexport type QuestionOption = {\r\n  id: number;\r\n  label: string;\r\n  sublabel: string;\r\n  code: string;\r\n  nextQuestionId?: number | null;\r\n};\r\n\r\nexport type Question = {\r\n  id: number;\r\n  label: string;\r\n  inputType: InputType;\r\n  hint?: string;\r\n  placeholder?: string;\r\n  isRequired: boolean;\r\n  position: number;\r\n  questionOptions: QuestionOption[];\r\n  questionGroupId?: number;\r\n};\r\n\r\nexport type QuestionGroup = {\r\n  id: number;\r\n  title: string;\r\n  order: number;\r\n  projectId: number;\r\n  createdAt?: string;\r\n  updatedAt?: string;\r\n  question?: Question[];\r\n};\r\n\r\n// schema for validating question\r\nexport const InputTypeKeys = Object.keys(\r\n  InputTypeMap\r\n) as (keyof typeof InputTypeMap)[];\r\n\r\nexport const QuestionSchema = z\r\n  .object({\r\n    label: z.string().min(1, \"Question name is required\"),\r\n    inputType: z.enum(InputTypeKeys as [string, ...string[]]),\r\n    hint: z.string().optional(),\r\n    placeholder: z.string().optional(),\r\n    questionOptions: z\r\n      .array(\r\n        z.object({\r\n          label: z.string(),\r\n          sublabel: z.string().optional(),\r\n          code: z.string(),\r\n          nextQuestionId: z.number().optional().nullable(),\r\n        })\r\n      )\r\n      .optional(),\r\n  })\r\n  .superRefine((data, ctx) => {\r\n    const needsOpts = needsOptions(data.inputType);\r\n\r\n    if (needsOpts) {\r\n      if (!data.questionOptions || data.questionOptions.length === 0) {\r\n        ctx.addIssue({\r\n          path: [\"questionOptions\"],\r\n          code: z.ZodIssueCode.custom,\r\n          message: \"At least one option is required\",\r\n        });\r\n      } else {\r\n        data.questionOptions.forEach((option) => {\r\n          if (!option.label.trim() || !option.code.trim()) {\r\n            ctx.addIssue({\r\n              path: [\"questionOptions\"],\r\n              code: z.ZodIssueCode.custom,\r\n              message: \"Each option must have a label and code\",\r\n            });\r\n          }\r\n        });\r\n      }\r\n    }\r\n  });\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAiCO,MAAM,gBAAgB,OAAO,IAAI,CACtC,yHAAA,CAAA,eAAY;;AAGP,MAAM,iBAAiB,uIAAA,CAAA,IAAC,CAC5B,MAAM,CAAC;IACN,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,WAAW,uIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAClB,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzB,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,iBAAiB,uIAAA,CAAA,IAAC,CACf,KAAK,CACJ,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM;QACf,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM;QACd,gBAAgB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,IAED,QAAQ;AACb,GACC,WAAW,OAAC,CAAC,MAAM;IAClB,MAAM,YAAY,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,SAAS;IAE7C,IAAI,WAAW;QACb,IAAI,CAAC,KAAK,eAAe,IAAI,KAAK,eAAe,CAAC,MAAM,KAAK,GAAG;YAC9D,IAAI,QAAQ,CAAC;gBACX,MAAM;oBAAC;iBAAkB;gBACzB,MAAM,uIAAA,CAAA,IAAC,CAAC,YAAY,CAAC,MAAM;gBAC3B,SAAS;YACX;QACF,OAAO;YACL,KAAK,eAAe,CAAC,OAAO,CAAC,CAAC;gBAC5B,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI;oBAC/C,IAAI,QAAQ,CAAC;wBACX,MAAM;4BAAC;yBAAkB;wBACzB,MAAM,uIAAA,CAAA,IAAC,CAAC,YAAY,CAAC,MAAM;wBAC3B,SAAS;oBACX;gBACF;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 3345, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/general/LoadingOverlay.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Spinner from \"./Spinner\";\r\n\r\nconst LoadingOverlay = () => {\r\n  return (\r\n    <div\r\n      className=\"fixed top-0 left-0 h-screen w-screen bg-neutral-900/20 z-50 flex items-center justify-center\"\r\n      onClick={(e) => {\r\n        e.stopPropagation();\r\n      }}\r\n    >\r\n      <Spinner />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { LoadingOverlay };\r\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,iBAAiB;IACrB,qBACE,6LAAC;QACC,WAAU;QACV,SAAS,CAAC;YACR,EAAE,eAAe;QACnB;kBAEA,cAAA,6LAAC,oIAAA,CAAA,UAAO;;;;;;;;;;AAGd;KAXM", "debugId": null}}, {"offset": {"line": 3382, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/hooks/use-toast.ts"], "sourcesContent": ["import { useState } from \"react\";\r\n\r\ninterface Toast {\r\n  title: string;\r\n  description?: string;\r\n  variant?: \"default\" | \"destructive\";\r\n}\r\n\r\nexport function useToast() {\r\n  const [toasts, setToasts] = useState<Toast[]>([]);\r\n\r\n  const toast = (toast: Toast) => {\r\n    setToasts((prevToasts) => [...prevToasts, toast]);\r\n    // Remove the toast after 3 seconds\r\n    setTimeout(() => {\r\n      setToasts((prevToasts) => prevToasts.filter((t) => t !== toast));\r\n    }, 3000);\r\n  };\r\n\r\n  return { toast, toasts };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAQO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,QAAQ,CAAC;QACb,UAAU,CAAC,aAAe;mBAAI;gBAAY;aAAM;QAChD,mCAAmC;QACnC,WAAW;YACT,UAAU,CAAC,aAAe,WAAW,MAAM,CAAC,CAAC,IAAM,MAAM;QAC3D,GAAG;IACL;IAEA,OAAO;QAAE;QAAO;IAAO;AACzB;GAZgB", "debugId": null}}, {"offset": {"line": 3416, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-builder/TableQuestionBuilder.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Trash, Plus, Eye, EyeOff } from \"lucide-react\";\nimport { createTable, updateTable } from \"@/lib/api/table\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"@/components/ui/table\";\nimport { cn } from \"@/lib/utils\";\n\ninterface CellValue {\n  [columnId: string]: string; // Maps column ID to cell value\n}\n\ninterface Column {\n  id: string; // Unique identifier for the column in the UI\n  columnName: string;\n  parentId?: string; // Reference to parent column in the UI (string ID)\n  level: number; // Nesting level (0 for top level)\n  children?: Column[]; // Child columns\n  // Database ID of the parent column for API submission\n  // This should be the actual database ID of the parent column\n  parentColumnId?: number; // Database ID of the parent column\n}\n\ninterface DefaultCellValue {\n  columnId: number;\n  value: string;\n  code?: string;\n}\n\n// Helper function to extract numeric ID from column UI ID\nfunction extractColumnId(uiId: string): number | null {\n  // First try to match the format \"col-{id}\" for existing columns\n  const exactMatch = uiId.match(/^col-(\\d+)$/);\n  if (exactMatch) {\n    const id = parseInt(exactMatch[1], 10);\n    // Validate that the ID is within safe integer range for PostgreSQL INT4\n    if (id > 0 && id <= 2147483647) {\n      return id;\n    }\n  }\n\n  // If that fails, try to match \"col-{id}-{random}\" for new columns\n  const newColMatch = uiId.match(/^col-(\\d+)-\\d+$/);\n  if (newColMatch) {\n    const id = parseInt(newColMatch[1], 10);\n    // Validate that the ID is within safe integer range for PostgreSQL INT4\n    if (id > 0 && id <= 2147483647) {\n      return id;\n    }\n  }\n\n  // If all else fails, extract any number from the ID\n  const anyNumberMatch = uiId.match(/^col-(\\d+)/);\n  if (anyNumberMatch) {\n    const id = parseInt(anyNumberMatch[1], 10);\n    // Validate that the ID is within safe integer range for PostgreSQL INT4\n    if (id > 0 && id <= 2147483647) {\n      return id;\n    }\n  }\n\n  console.warn(`Invalid or unsafe column ID: ${uiId}`);\n  return null;\n}\n\ninterface TableQuestionBuilderProps {\n  projectId: number;\n  onTableCreated?: (tableId: number) => void;\n  isInModal?: boolean; // Flag to indicate if component is used inside a modal\n  isEditMode?: boolean; // Flag to indicate if component is used for editing\n  existingTableData?: {\n    id: number;\n    label: string;\n    tableColumns: {\n      id: number;\n      columnName: string;\n      parentColumnId?: number;\n      childColumns?: {\n        id: number;\n        columnName: string;\n        parentColumnId: number;\n      }[];\n    }[];\n    tableRows: {\n      id: number;\n      rowsName: string;\n      defaultValues?: DefaultCellValue[];\n    }[];\n    cellValues?: Record<string, any>; // Cell values from the API\n  };\n}\n\nexport function TableQuestionBuilder({\n  projectId,\n  onTableCreated,\n  isInModal = false,\n  isEditMode = false,\n  existingTableData,\n}: TableQuestionBuilderProps) {\n  const { toast } = useToast();\n  const [label, setLabel] = useState(existingTableData?.label || \"\");\n  const [columns, setColumns] = useState<Column[]>(() => {\n    if (existingTableData?.tableColumns) {\n      // Convert existing columns to the format needed for the UI\n      const formattedColumns: Column[] = [];\n\n      // Process parent columns first and add them to the formatted array\n      existingTableData.tableColumns.forEach((parentCol) => {\n        // Create the parent column\n        const parentColumn: Column = {\n          id: `col-${parentCol.id}`, // Use the actual database ID in the UI ID\n          columnName: parentCol.columnName,\n          level: 0,\n          parentColumnId: undefined,\n        };\n\n        // Add parent column to the formatted array\n        formattedColumns.push(parentColumn);\n\n        console.log(\n          `Added parent column: \"${parentCol.columnName}\" (ID: ${parentCol.id})`\n        );\n\n        // Process child columns if they exist and add them right after the parent\n        if (parentCol.childColumns && parentCol.childColumns.length > 0) {\n          console.log(\n            `Processing ${parentCol.childColumns.length} child columns for parent \"${parentCol.columnName}\"`\n          );\n\n          parentCol.childColumns.forEach((childCol) => {\n            const childColumn: Column = {\n              id: `col-${childCol.id}`,\n              columnName: childCol.columnName,\n              level: 1, // Child columns are always level 1\n              parentId: `col-${parentCol.id}`, // Reference to parent's UI ID\n              parentColumnId: parentCol.id, // Reference to parent's database ID\n            };\n\n            // Add child column to the formatted array\n            formattedColumns.push(childColumn);\n\n            console.log(\n              `Added child column: \"${childCol.columnName}\" (ID: ${childCol.id}) with parent \"${parentCol.columnName}\" (ID: ${parentCol.id})`\n            );\n          });\n        }\n      });\n\n      console.log(\"Final formatted columns for UI:\", formattedColumns);\n\n      return formattedColumns.length > 0\n        ? formattedColumns\n        : [{ id: \"col-1\", columnName: \"\", level: 0 }];\n    }\n\n    return [{ id: \"col-1\", columnName: \"\", level: 0 }];\n  });\n\n  const [rows, setRows] = useState<\n    {\n      id?: number;\n      rowsName: string;\n      defaultValues?: DefaultCellValue[];\n      cellValues?: CellValue;\n    }[]\n  >(() => {\n    if (\n      existingTableData?.tableRows &&\n      existingTableData.tableRows.length > 0\n    ) {\n      return existingTableData.tableRows.map((row) => {\n        // Initialize cell values from API data if available\n        const cellValues: CellValue = {};\n\n        if (existingTableData.cellValues) {\n          console.log(\n            \"Processing cell values for row:\",\n            row.id,\n            existingTableData.cellValues\n          );\n          // Convert API cell values to the format expected by the component\n          Object.entries(existingTableData.cellValues).forEach(\n            ([key, value]: [string, any]) => {\n              const [columnId, rowId] = key.split(\"_\");\n              if (parseInt(rowId) === row.id) {\n                // Convert database column ID to UI column ID format\n                const uiKey = `col-${columnId}`;\n                // Handle both object and string value formats\n                const cellValue =\n                  typeof value === \"object\" && value !== null\n                    ? value.value || \"\"\n                    : value || \"\";\n                cellValues[uiKey] = cellValue;\n                console.log(`Setting cell value: ${uiKey} = ${cellValue}`);\n              }\n            }\n          );\n        }\n\n        console.log(\"Final cell values for row:\", row.id, cellValues);\n\n        return {\n          id: row.id,\n          rowsName: row.rowsName,\n          defaultValues: row.defaultValues || [],\n          cellValues: cellValues,\n        };\n      });\n    }\n    // Start with one empty row\n    return [{ rowsName: \"1\", defaultValues: [], cellValues: {} }];\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [showPreview, setShowPreview] = useState(true);\n\n  // Generate a unique ID for new columns using a safer approach\n  // Use a counter-based approach to avoid large timestamp values\n  const generateId = (() => {\n    let counter = 1;\n    return () => {\n      // Use a smaller base number and increment counter\n      // This ensures IDs stay within safe integer range\n      const baseId = 100000 + counter;\n      counter++;\n      return `col-${baseId}-${Math.floor(Math.random() * 100)}`;\n    };\n  })();\n\n  // Function to handle cell value changes\n  const handleCellValueChange = (\n    rowIndex: number,\n    columnId: string,\n    value: string\n  ) => {\n    setRows((prevRows) => {\n      const newRows = [...prevRows];\n      if (!newRows[rowIndex]) return prevRows;\n      if (!newRows[rowIndex].cellValues) {\n        newRows[rowIndex].cellValues = {};\n      }\n      newRows[rowIndex].cellValues = {\n        ...newRows[rowIndex].cellValues,\n        [columnId]: value,\n      };\n      return newRows;\n    });\n  };\n\n  // Function to get a cell's value\n  const getCellValue = (rowIndex: number, columnId: string): string => {\n    return rows[rowIndex]?.cellValues?.[columnId] || \"\";\n  };\n\n  // Function to add a new row\n  const handleAddRow = () => {\n    setRows((prevRows) => [\n      ...prevRows,\n      {\n        rowsName: `${prevRows.length + 1}`, // Keep this for backend compatibility\n        defaultValues: [],\n        cellValues: {},\n      },\n    ]);\n  };\n\n  // Add a top-level column\n\n  // Table Preview JSX\n  // Place this in your component's return, replacing the old table preview if present\n  // (If you have a previous table preview, replace it with this block)\n\n  /* Table Preview Section Start */\n  <div className=\"mt-6 space-y-4\">\n    <div className=\"flex justify-between items-center\">\n      <h4 className=\"font-medium\">Table Preview</h4>\n      <Button type=\"button\" variant=\"outline\" size=\"sm\" onClick={handleAddRow}>\n        <Plus className=\"h-4 w-4 mr-1\" /> Add Row\n      </Button>\n    </div>\n    <div className=\"border rounded-md overflow-hidden\">\n      <Table>\n        <TableHeader>\n          <TableRow>\n            <TableHead className=\"w-[100px] text-center\">S.NO</TableHead>\n            {columns.map((column) => (\n              <TableHead key={column.id}>\n                {column.columnName || `Column ${column.id}`}\n              </TableHead>\n            ))}\n            <TableHead className=\"w-[100px]\">Actions</TableHead>\n          </TableRow>\n        </TableHeader>\n        <TableBody>\n          {rows.map((row, rowIndex) => (\n            <TableRow key={rowIndex}>\n              <TableCell className=\"font-medium text-center bg-blue-50/50\">\n                {rowIndex + 1}\n              </TableCell>\n              {columns.map((column) => (\n                <TableCell key={`${rowIndex}-${column.id}`}>\n                  <Input\n                    value={getCellValue(rowIndex, column.id)}\n                    onChange={(e) =>\n                      handleCellValueChange(rowIndex, column.id, e.target.value)\n                    }\n                    placeholder=\"Value\"\n                    className=\"w-full\"\n                  />\n                </TableCell>\n              ))}\n              <TableCell>\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => handleRemoveRow(rowIndex)}\n                  disabled={rows.length <= 1}\n                  title=\"Remove row\"\n                >\n                  <Trash className=\"h-4 w-4\" />\n                </Button>\n              </TableCell>\n            </TableRow>\n          ))}\n        </TableBody>\n      </Table>\n    </div>\n  </div>;\n  /* Table Preview Section End */\n\n  const handleAddColumn = () => {\n    setColumns([\n      ...columns,\n      {\n        id: generateId(),\n        columnName: \"\",\n        level: 0,\n      },\n    ]);\n  };\n\n  // Add a child column to a parent column\n  const handleAddChildColumn = (parentId: string) => {\n    // Find the parent column and its level\n    const parentColumn = findColumnById(parentId);\n    if (!parentColumn) return;\n\n    // Check if parent is already a child column (level > 0)\n    // If so, we can't add a child to it (would create a 3rd level)\n    if (parentColumn.level > 0) {\n      toast({\n        title: \"Cannot add child column\",\n        description:\n          \"Cannot create more than 2 levels of nested columns (parent → child → grandchild)\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    // Count existing children of this parent\n    const childCount = columns.filter(\n      (col) => col.parentId === parentId\n    ).length;\n\n    // Check if parent already has 2 children\n    if (childCount >= 2) {\n      toast({\n        title: \"Cannot add more child columns\",\n        description: `Parent column \"${\n          parentColumn.columnName || \"Unnamed\"\n        }\" cannot have more than 2 child columns`,\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    // Extract the parent's database ID from its UI ID\n    const parentDbIdMatch = parentId.match(/^col-(\\d+)/);\n    const parentDbId = parentDbIdMatch\n      ? parseInt(parentDbIdMatch[1], 10)\n      : undefined;\n\n    const newColumn: Column = {\n      id: generateId(),\n      columnName: \"\",\n      parentId: parentId, // This is the UI ID of the parent\n      level: parentColumn.level + 1,\n      // Store the parent's database ID for API submission\n      parentColumnId: parentDbId,\n    };\n\n    // Insert the new column after the parent and its existing children\n    const newColumns = [...columns];\n    const lastChildIndex = findLastChildIndex(parentId);\n\n    // If there are no existing children, insert right after the parent\n    if (\n      lastChildIndex === -1 ||\n      lastChildIndex === columns.findIndex((col) => col.id === parentId)\n    ) {\n      const parentIndex = columns.findIndex((col) => col.id === parentId);\n      newColumns.splice(parentIndex + 1, 0, newColumn);\n    } else {\n      // Otherwise, insert after the last child\n      newColumns.splice(lastChildIndex + 1, 0, newColumn);\n    }\n\n    setColumns(newColumns);\n  };\n\n  // Find a column by its ID\n  const findColumnById = (id: string): Column | undefined => {\n    return columns.find((col) => col.id === id);\n  };\n\n  // Find the index of the last child of a parent column\n  const findLastChildIndex = (parentId: string): number => {\n    const parentIndex = columns.findIndex((col) => col.id === parentId);\n    if (parentIndex === -1) return -1;\n\n    // Find the last consecutive child\n    let lastChildIndex = parentIndex;\n    let foundChild = false;\n\n    for (let i = parentIndex + 1; i < columns.length; i++) {\n      if (columns[i].parentId === parentId) {\n        // Direct child of this parent\n        lastChildIndex = i;\n        foundChild = true;\n      } else if (isDescendantOf(columns[i], parentId)) {\n        // Descendant (could be a grandchild)\n        lastChildIndex = i;\n        foundChild = true;\n      } else {\n        // Not a descendant, we've reached the end of this parent's children\n        break;\n      }\n    }\n\n    // If no children were found, return the parent index\n    return foundChild ? lastChildIndex : parentIndex;\n  };\n\n  // Check if a column is a descendant of a parent column\n  const isDescendantOf = (column: Column, ancestorId: string): boolean => {\n    if (column.parentId === ancestorId) return true;\n    if (!column.parentId) return false;\n\n    const parent = findColumnById(column.parentId);\n    if (!parent) return false;\n\n    return isDescendantOf(parent, ancestorId);\n  };\n\n  // Function to remove a column and its children\n  const handleRemoveColumn = (columnId: string) => {\n    // Don't allow removing the last column\n    if (columns.length <= 1) {\n      toast({\n        title: \"Cannot remove column\",\n        description: \"You must have at least one column in the table.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    // Find all child columns that need to be removed\n    const childrenToRemove = columns\n      .filter((col) => {\n        // Direct children\n        if (col.parentId === columnId) return true;\n        // Check for descendants (grandchildren, etc.)\n        return isDescendantOf(col, columnId);\n      })\n      .map((col) => col.id);\n\n    // Create a new array without the column and its children\n    const newColumns = columns.filter((col) => {\n      return col.id !== columnId && !childrenToRemove.includes(col.id);\n    });\n\n    // Update the columns state\n    setColumns(newColumns);\n  };\n\n  const handleRemoveRow = (index: number) => {\n    // Allow removing all rows since rows are now optional\n    const newRows = [...rows];\n    newRows.splice(index, 1);\n    setRows(newRows);\n  };\n\n  const handleColumnNameChange = (id: string, value: string) => {\n    const newColumns = columns.map((col) => {\n      if (col.id === id) {\n        return { ...col, columnName: value };\n      }\n      return col;\n    });\n    setColumns(newColumns);\n  };\n\n  const handleRowNameChange = (index: number, value: string) => {\n    const newRows = [...rows];\n    newRows[index].rowsName = value;\n    setRows(newRows);\n  };\n\n  // Prepare columns for API submission by converting the hierarchical structure\n  const prepareColumnsForSubmission = () => {\n    // Check if we're in edit mode with existing table data\n    const isEditing = isEditMode || existingTableData?.id;\n\n    // Create a map of UI column IDs to their database IDs\n    const columnIdMap = new Map<string, number>();\n\n    // Create a map to track parent-child relationships\n    const parentChildMap = new Map<string, string[]>();\n\n    // Build the parent-child relationship map from the current UI state\n    columns.forEach((col) => {\n      if (col.parentId) {\n        if (!parentChildMap.has(col.parentId)) {\n          parentChildMap.set(col.parentId, []);\n        }\n        parentChildMap.get(col.parentId)?.push(col.id);\n      }\n    });\n\n    // If editing, try to extract existing database IDs from the column IDs\n    if (isEditing && existingTableData?.tableColumns) {\n      // Map existing column IDs from the database\n      columns.forEach((col) => {\n        if (col.columnName.trim()) {\n          // Extract the database ID from the UI ID (format: \"col-{id}\")\n          const match = col.id.match(/^col-(\\d+)/);\n          if (match && match[1]) {\n            const dbId = parseInt(match[1], 10);\n            // Verify this ID exists in the original data\n            const existingColumn = existingTableData.tableColumns.find(\n              (ec) => ec.id === dbId\n            );\n            if (existingColumn) {\n              columnIdMap.set(col.id, dbId);\n            }\n          }\n        }\n      });\n    }\n\n    // For columns without mapped IDs (new columns), assign temporary IDs\n    let nextId =\n      Math.max(\n        ...Array.from(columnIdMap.values(), (id) => id || 0),\n        ...(existingTableData?.tableColumns?.map((col) => col.id) || [0]),\n        0\n      ) + 1;\n\n    // Assign IDs to columns that don't have them yet\n    columns.forEach((col) => {\n      if (col.columnName.trim() && !columnIdMap.has(col.id)) {\n        columnIdMap.set(col.id, nextId++);\n      }\n    });\n\n    // Create API columns array with proper ordering\n    const apiColumns: {\n      id?: number;\n      columnName: string;\n      parentColumnId?: number;\n    }[] = [];\n\n    // Create a map to track which UI column ID corresponds to which position in the API columns array\n    const apiColumnPositionMap = new Map<string, number>();\n\n    // Process columns in order of their level (0 = top level, 1 = first child level, etc.)\n    // This ensures that parent columns are processed before their children\n    // Use 0 as fallback if there are no columns with levels\n    const maxLevel = Math.max(...columns.map((col) => col.level || 0), 0);\n\n    // First pass: Add all top-level columns (level 0) to establish their positions\n    const topLevelColumns = columns.filter(\n      (col) => col.level === 0 && col.columnName.trim()\n    );\n\n    // Add all top-level columns first\n    topLevelColumns.forEach((col) => {\n      const columnId = columnIdMap.get(col.id);\n\n      const apiColumn: {\n        id?: number;\n        columnName: string;\n        parentColumnId?: number;\n      } = {\n        columnName: col.columnName.trim(),\n      };\n\n      // If editing and we have a column ID, include it\n      if (isEditing && columnId) {\n        apiColumn.id = columnId;\n      }\n\n      // Add the column to the API columns array\n      const apiColumnIndex = apiColumns.length;\n      apiColumns.push(apiColumn);\n\n      // Store the mapping between UI column ID and its position in the API columns array\n      apiColumnPositionMap.set(col.id, apiColumnIndex);\n    });\n\n    // Second pass: Add all child columns (level > 0)\n    for (let level = 1; level <= maxLevel; level++) {\n      // Get all columns at this level with valid names\n      const columnsAtLevel = columns.filter(\n        (col) => col.level === level && col.columnName.trim()\n      );\n\n      // Process each column at this level\n      columnsAtLevel.forEach((col) => {\n        const columnId = columnIdMap.get(col.id);\n\n        // Create the API column object\n        const apiColumn: {\n          id?: number;\n          columnName: string;\n          parentColumnId?: number;\n        } = {\n          columnName: col.columnName.trim(),\n        };\n\n        // If editing and we have a column ID, include it\n        if (isEditing && columnId) {\n          apiColumn.id = columnId;\n        }\n\n        // If this is a child column (has a parentId), set the parentColumnId\n        if (col.parentId) {\n          if (isEditing) {\n            // For editing, use database IDs for parentColumnId\n            const parentColumn = columns.find((c) => c.id === col.parentId);\n            const parentDbId = columnIdMap.get(col.parentId);\n\n            if (parentDbId) {\n              // Use the parent's database ID\n              apiColumn.parentColumnId = parentDbId;\n            } else {\n              console.warn(\n                `Could not find parent DB ID for column \"${col.columnName}\" (parentId: ${col.parentId})`\n              );\n              // Don't set a parent ID if we can't find the parent - make it a top-level column\n              toast({\n                title: \"Warning\",\n                description: `Column \"${col.columnName}\" had a missing parent reference and was converted to a top-level column.`,\n                variant: \"destructive\",\n              });\n            }\n          } else {\n            // For creation, use position-based indices (1-based)\n            const parentPosition = apiColumnPositionMap.get(col.parentId);\n\n            if (parentPosition !== undefined) {\n              // Use the parent's position (1-based index)\n              apiColumn.parentColumnId = parentPosition + 1; // Convert to 1-based index\n              console.log(\n                `Setting parentColumnId for \"${col.columnName}\" to ${\n                  parentPosition + 1\n                } (parent: \"${\n                  columns.find((c) => c.id === col.parentId)?.columnName\n                }\")`\n              );\n            } else {\n              console.warn(\n                `Could not find parent position for column \"${col.columnName}\" (parentId: ${col.parentId})`\n              );\n              // Don't set a parent ID if we can't find the parent - make it a top-level column\n              toast({\n                title: \"Warning\",\n                description: `Column \"${col.columnName}\" had a missing parent reference and was converted to a top-level column.`,\n                variant: \"destructive\",\n              });\n            }\n          }\n        }\n\n        // Add the column to the API columns array\n        const apiColumnIndex = apiColumns.length;\n        apiColumns.push(apiColumn);\n\n        // Store the mapping between UI column ID and its position in the API columns array\n        apiColumnPositionMap.set(col.id, apiColumnIndex);\n      });\n    }\n\n    // Validate all parent column references before returning\n    const invalidParentReferences = apiColumns.filter((col) => {\n      if (col.parentColumnId === undefined) {\n        return false; // No parent, so it's valid\n      }\n\n      if (isEditing) {\n        // For editing, parentColumnId should be a valid database ID\n        // It must be a positive number\n        if (col.parentColumnId <= 0) {\n          return true; // Invalid if not a positive number\n        }\n      } else {\n        // For creation, parentColumnId should be a valid position (1-based index)\n        // It must be a positive number and not greater than the number of columns\n        if (col.parentColumnId <= 0 || col.parentColumnId > apiColumns.length) {\n          return true; // Invalid position\n        }\n\n        // Check if the referenced parent is itself a child column\n        const parentColumn = apiColumns[col.parentColumnId - 1]; // Convert to 0-based index\n        if (parentColumn && parentColumn.parentColumnId !== undefined) {\n          return true; // Invalid: parent is itself a child (would create a 3rd level)\n        }\n      }\n\n      return false;\n    });\n\n    if (invalidParentReferences.length > 0) {\n      console.error(\n        \"Found invalid parent column references:\",\n        invalidParentReferences\n      );\n\n      // Fix the invalid references by removing them\n      invalidParentReferences.forEach((col) => {\n        col.parentColumnId = undefined;\n      });\n\n      toast({\n        title: \"Warning\",\n        description: `Fixed ${invalidParentReferences.length} invalid column relationships. Some child columns were converted to top-level columns.`,\n        variant: \"destructive\",\n      });\n    }\n\n    // Log the final columns for debugging\n    console.log(\"Final API columns:\", apiColumns);\n    return apiColumns;\n  };\n\n  const handleSubmit = async (e?: React.FormEvent) => {\n    if (e) e.preventDefault();\n    // Validate inputs\n    if (!label.trim()) {\n      toast({\n        title: \"Error\",\n        description: \"Please enter a table label\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      // Prepare columns for API submission\n      const apiColumns = prepareColumnsForSubmission();\n\n      // Ensure we have at least one row\n      let rowsToProcess = rows;\n      if (rowsToProcess.length === 0) {\n        // Add a default row if none exists\n        rowsToProcess = [\n          { rowsName: \"Row 1\", defaultValues: [], cellValues: {} },\n        ];\n        console.log(\"No valid rows found, adding a default row\");\n      }\n\n      // Prepare rows with trimmed values and proper IDs\n      // Sort rows to maintain consistent order\n      const sortedValidRows = [...rowsToProcess].sort((a, b) => {\n        // If both rows have IDs, sort by ID\n        if (a.id && b.id) {\n          return a.id - b.id;\n        }\n        // If only one has an ID, put the one with ID first\n        if (a.id) return -1;\n        if (b.id) return 1;\n        // Otherwise, maintain the order they appear in the UI\n        return rowsToProcess.indexOf(a) - rowsToProcess.indexOf(b);\n      });\n\n      console.log(\"Processing rows for submission:\", sortedValidRows);\n\n      const apiRows = sortedValidRows.map((row, rowIndex) => {\n        const rowData: {\n          id?: number;\n          rowsName: string;\n          defaultValues?: DefaultCellValue[];\n        } = {\n          rowsName: row.rowsName.trim() || `Row ${rowIndex + 1}`,\n        };\n\n        // Only include ID if it's a valid existing ID\n        if (isEditMode || existingTableData?.id) {\n          if (\n            row.id &&\n            existingTableData?.tableRows?.some((r) => r.id === row.id)\n          ) {\n            rowData.id = row.id;\n          }\n        }\n\n        // Convert cell values to defaultValues format for the API\n        const defaultValues: DefaultCellValue[] = [];\n\n        // Process cell values from the UI inputs\n        if (row.cellValues) {\n          Object.entries(row.cellValues).forEach(([columnId, value]) => {\n            // Skip empty values and the S.NO column (which is non-editable)\n            if (value && !columnId.includes(\"s-no\")) {\n              const column = columns.find((col) => col.id === columnId);\n              if (column) {\n                // For new columns, we need to use the position in the apiColumns array\n                let columnDbId: number | null = null;\n\n                if (isEditMode || existingTableData?.id) {\n                  // For edit mode, extract the database ID\n                  columnDbId = extractColumnId(column.id);\n                } else {\n                  // For creation mode, use the position in the apiColumns array\n                  const apiColumnIndex = apiColumns.findIndex(\n                    (apiCol) => apiCol.columnName === column.columnName.trim()\n                  );\n\n                  if (apiColumnIndex !== -1) {\n                    // Use 1-based index for the API\n                    columnDbId = apiColumnIndex + 1;\n                    console.log(\n                      `Using position-based index ${columnDbId} for column \"${column.columnName}\"`\n                    );\n                  }\n                }\n\n                if (columnDbId) {\n                  const defaultValue = {\n                    columnId: columnDbId,\n                    value: String(value).trim(),\n                    code: String(value).trim(), // Use the same value for code\n                  };\n                  console.log(\n                    `Adding default value for column ${columnDbId}:`,\n                    defaultValue\n                  );\n                  console.log(\n                    `Original UI column ID: ${columnId}, extracted DB ID: ${columnDbId}, column name: ${column.columnName}`\n                  );\n                  defaultValues.push(defaultValue);\n                } else {\n                  console.warn(\n                    `Could not extract column DB ID from UI ID: ${columnId} for column: ${column.columnName}`\n                  );\n                }\n              }\n            }\n          });\n        }\n\n        // Also include any existing default values that might be present\n        if (row.defaultValues && row.defaultValues.length > 0) {\n          row.defaultValues.forEach((dv) => {\n            // Only add if not already added from cellValues\n            if (\n              !defaultValues.some(\n                (existingDv) => existingDv.columnId === dv.columnId\n              )\n            ) {\n              defaultValues.push(dv);\n            }\n          });\n        }\n\n        console.log(\n          `Row ${rowData.rowsName} has ${defaultValues.length} default values before mapping`\n        );\n\n        // Process and validate default values\n        if (defaultValues.length > 0) {\n          // Filter out any invalid default values\n          const validDefaultValues = defaultValues.filter((dv) => {\n            const isValid =\n              dv.columnId &&\n              typeof dv.columnId === \"number\" &&\n              dv.columnId > 0 &&\n              dv.value &&\n              String(dv.value).trim() !== \"\";\n\n            if (!isValid) {\n              console.warn(`Filtering out invalid default value:`, dv);\n            }\n            return isValid;\n          });\n\n          console.log(\n            `Row ${rowData.rowsName} has ${validDefaultValues.length} valid default values after filtering`\n          );\n\n          // Set the default values on the row data\n          if (validDefaultValues.length > 0) {\n            rowData.defaultValues = validDefaultValues;\n            console.log(\n              `Final default values for row ${rowData.rowsName}:`,\n              JSON.stringify(validDefaultValues)\n            );\n          } else {\n            console.warn(\n              `No valid default values found for row ${rowData.rowsName}`\n            );\n          }\n        }\n\n        return rowData;\n      });\n\n      let table;\n\n      // Check if we're editing an existing table or creating a new one\n      if (existingTableData?.id) {\n        // Log the final payload being sent to the API\n        console.log(\"=== FINAL API PAYLOAD FOR UPDATE ===\");\n        console.log(\"Table ID:\", existingTableData.id);\n        console.log(\"Label:\", label.trim());\n        console.log(\"API Columns:\", JSON.stringify(apiColumns, null, 2));\n        console.log(\"API Rows:\", JSON.stringify(apiRows, null, 2));\n        console.log(\"=== END PAYLOAD ===\");\n\n        // Update existing table\n        table = await updateTable(\n          existingTableData.id,\n          label.trim(),\n          apiColumns,\n          apiRows\n        );\n\n        toast({\n          title: \"Success\",\n          description: \"Table question updated successfully\",\n        });\n      } else {\n        // Create new table\n        table = await createTable(label.trim(), projectId, apiColumns, apiRows);\n\n        toast({\n          title: \"Success\",\n          description: \"Table question created successfully\",\n        });\n\n        // Reset form for new table creation\n        setLabel(\"\");\n        setColumns([\n          {\n            id: generateId(),\n            columnName: \"\",\n            level: 0,\n          },\n        ]);\n        setRows([]); // Start with no rows since they're optional\n      }\n\n      // Notify parent component\n      if (onTableCreated && table?.id) {\n        onTableCreated(table.id);\n      }\n    } catch (error: any) {\n      console.error(\"Error with table operation:\", error);\n\n      // Extract more detailed error message if available\n      let errorMessage = existingTableData?.id\n        ? \"Failed to update table question\"\n        : \"Failed to create table question\";\n\n      if (error.response?.data?.message) {\n        errorMessage = error.response.data.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Create a ref for the table builder component\n  const tableBuilderRef = React.useRef<HTMLDivElement>(null);\n\n  // Add event listener for the custom submitTable event\n  useEffect(() => {\n    if (isInModal) {\n      const handleSubmitEvent = (event: Event) => {\n        // Prevent the event from bubbling further if needed\n        if (event.cancelable) {\n          event.stopPropagation();\n        }\n\n        // Call the submit handler\n        handleSubmit();\n      };\n\n      // Add event listener to multiple possible elements\n\n      // 1. Add to the component's root element\n      if (tableBuilderRef.current) {\n        const tableBuilder = tableBuilderRef.current;\n\n        tableBuilder.addEventListener(\"submitTable\", handleSubmitEvent);\n      }\n\n      // 2. Add to document for bubbled events\n      document.addEventListener(\"submitTable\", handleSubmitEvent);\n\n      // 3. Add to any existing table-question-builder elements\n      const allTableBuilders = document.querySelectorAll(\n        \".table-question-builder\"\n      );\n\n      allTableBuilders.forEach((element) => {\n        element.addEventListener(\"submitTable\", handleSubmitEvent);\n      });\n\n      return () => {\n        // Clean up when component unmounts\n\n        if (tableBuilderRef.current) {\n          tableBuilderRef.current.removeEventListener(\n            \"submitTable\",\n            handleSubmitEvent\n          );\n        }\n\n        document.removeEventListener(\"submitTable\", handleSubmitEvent);\n\n        allTableBuilders.forEach((element) => {\n          element.removeEventListener(\"submitTable\", handleSubmitEvent);\n        });\n      };\n    }\n  }, [isInModal, handleSubmit, columns]);\n\n  return (\n    <div\n      ref={tableBuilderRef}\n      className=\"space-y-6 p-4 border border-gray-200 rounded-md w-full table-question-builder\"\n    >\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium\">\n          {isEditMode || existingTableData?.id\n            ? \"Edit Table Question\"\n            : \"Create Table Question\"}\n        </h3>\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setShowPreview(!showPreview)}\n          className=\"flex items-center gap-1\"\n        >\n          {showPreview ? (\n            <>\n              <EyeOff className=\"h-4 w-4\" />\n              Hide Preview\n            </>\n          ) : (\n            <>\n              <Eye className=\"h-4 w-4\" />\n              Show Preview\n            </>\n          )}\n        </Button>\n      </div>\n\n      <div className=\"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200\">\n        <p className=\"font-medium mb-1\">Table Structure Guidelines:</p>\n        <ul className=\"list-disc pl-5 space-y-1\">\n          <li>\n            Create multiple <span className=\"font-medium\">parent columns</span>{\" \"}\n            using the \"Add Top-Level Column\" button\n          </li>\n          <li>\n            Add up to 2 <span className=\"font-medium\">child columns</span> under\n            each parent using the \"+\" button\n          </li>\n          <li>\n            Child columns cannot have their own children (maximum 2 levels)\n          </li>\n        </ul>\n      </div>\n\n      {isInModal ? (\n        <div className=\"space-y-4\">\n          <div>\n            <Label htmlFor=\"table-label\">Table Label</Label>\n            <Input\n              id=\"table-label\"\n              value={label}\n              onChange={(e) => setLabel(e.target.value)}\n              placeholder=\"Enter table question label\"\n              required\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 max-h-[60vh] overflow-y-auto\">\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label>Columns</Label>\n                {columns.map((column) => (\n                  <div\n                    key={column.id}\n                    className={cn(\n                      \"flex items-center gap-2 p-2 rounded-md\",\n                      column.level === 0\n                        ? \"bg-gray-50\"\n                        : \"bg-white border-l-2 border-gray-300\"\n                    )}\n                    style={{ marginLeft: `${column.level * 20}px` }}\n                  >\n                    <div className=\"flex-1 flex items-center gap-2\">\n                      {column.level > 0 && (\n                        <div className=\"w-4 h-4 flex items-center justify-center\">\n                          <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\n                        </div>\n                      )}\n                      <Input\n                        value={column.columnName}\n                        onChange={(e) =>\n                          handleColumnNameChange(column.id, e.target.value)\n                        }\n                        placeholder={`${\n                          column.level === 0 ? \"Parent\" : \"Child\"\n                        } Column`}\n                        className={cn(\n                          \"flex-1\",\n                          column.level === 0\n                            ? \"border-blue-200 bg-white\"\n                            : \"border-dashed\"\n                        )}\n                      />\n                      {column.level === 0 && (\n                        <div className=\"text-xs text-blue-500 font-medium\">\n                          Parent\n                        </div>\n                      )}\n                      {column.level > 0 && (\n                        <div className=\"text-xs text-gray-500 font-medium\">\n                          Child\n                        </div>\n                      )}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"icon\"\n                        onClick={() => handleAddChildColumn(column.id)}\n                        title={\n                          column.level > 0\n                            ? \"Child columns cannot have their own children\"\n                            : columns.filter(\n                                (col) => col.parentId === column.id\n                              ).length >= 2\n                            ? \"Maximum 2 child columns allowed\"\n                            : \"Add child column\"\n                        }\n                        disabled={\n                          column.level > 0 || // Disable if this is already a child column\n                          columns.filter((col) => col.parentId === column.id)\n                            .length >= 2 // Disable if already has 2 children\n                        }\n                      >\n                        <Plus\n                          className={cn(\n                            \"h-4 w-4\",\n                            (column.level > 0 ||\n                              columns.filter(\n                                (col) => col.parentId === column.id\n                              ).length >= 2) &&\n                              \"text-gray-300\"\n                          )}\n                        />\n                      </Button>\n\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"icon\"\n                        onClick={() => handleRemoveColumn(column.id)}\n                        disabled={columns.length <= 1}\n                        title=\"Remove column\"\n                      >\n                        <Trash className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleAddColumn}\n                  className=\"mt-2\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Top-Level Column\n                </Button>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label>Rows</Label>\n                {rows.map((row, index) => (\n                  <div key={index} className=\"flex items-center gap-2\">\n                    <Input\n                      value={row.rowsName}\n                      onChange={(e) =>\n                        handleRowNameChange(index, e.target.value)\n                      }\n                      placeholder={`Row ${index + 1}`}\n                    />\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"icon\"\n                      onClick={() => handleRemoveRow(index)}\n                      disabled={false}\n                    >\n                      <Trash className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                ))}\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleAddRow}\n                  className=\"mt-2\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Row\n                </Button>\n              </div>\n            </div>\n\n            {showPreview && (\n              <div className=\"space-y-2\">\n                <Label>Table Preview</Label>\n                <div className=\"border rounded-md p-4 overflow-x-auto max-h-[300px] overflow-y-auto\">\n                  <Table>\n                    <TableHeader>\n                      {/* Calculate the maximum nesting level */}\n                      {(() => {\n                        const maxLevel = Math.max(\n                          ...columns.map((col) => col.level),\n                          0\n                        );\n\n                        // Create header rows for each level\n                        const headerRows = [];\n\n                        // First row with parent columns starting from leftmost position\n                        headerRows.push(\n                          <TableRow key=\"header-row-0\">\n                            {/* Render top-level (parent) columns starting from position 1 */}\n                            {columns\n                              .filter((col) => col.level === 0)\n                              .map((parentCol) => {\n                                // Count all descendants at all levels\n                                const getAllDescendants = (\n                                  colId: string\n                                ): Column[] => {\n                                  const directChildren = columns.filter(\n                                    (c) => c.parentId === colId\n                                  );\n                                  let allDescendants = [...directChildren];\n\n                                  directChildren.forEach((child) => {\n                                    allDescendants = [\n                                      ...allDescendants,\n                                      ...getAllDescendants(child.id),\n                                    ];\n                                  });\n\n                                  return allDescendants;\n                                };\n\n                                const descendants = getAllDescendants(\n                                  parentCol.id\n                                );\n                                const leafNodes = descendants.filter(\n                                  (d) =>\n                                    !columns.some((c) => c.parentId === d.id)\n                                );\n\n                                // If no descendants, it's a leaf node itself\n                                const colSpanCount =\n                                  descendants.length > 0\n                                    ? leafNodes.length || 1\n                                    : 1;\n\n                                return (\n                                  <TableHead\n                                    key={parentCol.id}\n                                    colSpan={colSpanCount}\n                                    className=\"text-center border-b\"\n                                  >\n                                    {parentCol.columnName || \"Column\"}\n                                  </TableHead>\n                                );\n                              })}\n                          </TableRow>\n                        );\n\n                        // Create header rows for each additional level\n                        for (let level = 1; level <= maxLevel; level++) {\n                          headerRows.push(\n                            <TableRow key={`header-row-${level}`}>\n                              {/* For each level, we need to find columns at that level and their parents */}\n                              {(() => {\n                                const columnsAtLevel = columns.filter(\n                                  (col) => col.level === level - 1\n                                );\n\n                                return columnsAtLevel.map((col) => {\n                                  // Get direct children of this column\n                                  const children = columns.filter(\n                                    (c) => c.parentId === col.id\n                                  );\n\n                                  // If no children, render an empty cell to maintain alignment\n                                  if (children.length === 0) {\n                                    return (\n                                      <TableHead\n                                        key={`empty-${col.id}`}\n                                        className=\"text-center border-b\"\n                                      >\n                                        {/* Empty cell to maintain column alignment */}\n                                      </TableHead>\n                                    );\n                                  }\n\n                                  // Render each child with appropriate colspan\n                                  return children.map((child) => {\n                                    // Calculate how many leaf nodes are under this child\n                                    const getAllDescendants = (\n                                      colId: string\n                                    ): Column[] => {\n                                      const directChildren = columns.filter(\n                                        (c) => c.parentId === colId\n                                      );\n                                      let allDescendants = [...directChildren];\n\n                                      directChildren.forEach((child) => {\n                                        allDescendants = [\n                                          ...allDescendants,\n                                          ...getAllDescendants(child.id),\n                                        ];\n                                      });\n\n                                      return allDescendants;\n                                    };\n\n                                    const descendants = getAllDescendants(\n                                      child.id\n                                    );\n                                    const leafNodes = descendants.filter(\n                                      (d) =>\n                                        !columns.some(\n                                          (c) => c.parentId === d.id\n                                        )\n                                    );\n\n                                    // If no descendants, it's a leaf node itself\n                                    const colSpanCount =\n                                      descendants.length > 0\n                                        ? leafNodes.length || 1\n                                        : 1;\n\n                                    return (\n                                      <TableHead\n                                        key={child.id}\n                                        colSpan={colSpanCount}\n                                        className=\"text-center border-b\"\n                                      >\n                                        {child.columnName || \"Child Column\"}\n                                      </TableHead>\n                                    );\n                                  });\n                                });\n                              })()}\n                            </TableRow>\n                          );\n                        }\n\n                        return headerRows;\n                      })()}\n                    </TableHeader>\n                    <TableBody>\n                      {rows.length > 0 ? (\n                        rows.map((row, rowIndex) => (\n                          <TableRow key={rowIndex}>\n                            {/* Render cells for each column starting from leftmost position */}\n                            {columns\n                              .filter((col) => {\n                                // Show cells for top-level columns with no children\n                                // or for child columns (level > 0)\n                                const hasChildren = columns.some(\n                                  (c) => c.parentId === col.id\n                                );\n                                return (\n                                  (col.level === 0 && !hasChildren) ||\n                                  col.level > 0\n                                );\n                              })\n                              .map((col) => (\n                                <TableCell\n                                  key={col.id}\n                                  className=\"bg-gray-50 p-0\"\n                                >\n                                  {/* Extract the numeric ID from the column ID */}\n                                  {(() => {\n                                    const columnId = extractColumnId(col.id);\n                                    return columnId ? (\n                                      <Input\n                                        type=\"text\"\n                                        placeholder=\"Enter value\"\n                                        className=\"border-0 h-8 text-center bg-transparent\"\n                                        value={getCellValue(rowIndex, col.id)}\n                                        onChange={(e) =>\n                                          handleCellValueChange(\n                                            rowIndex,\n                                            col.id,\n                                            e.target.value\n                                          )\n                                        }\n                                      />\n                                    ) : (\n                                      <div className=\"h-8 flex items-center justify-center text-gray-400 text-xs\">\n                                        Input field\n                                      </div>\n                                    );\n                                  })()}\n                                </TableCell>\n                              ))}\n                          </TableRow>\n                        ))\n                      ) : (\n                        // When no rows exist, show a single row with input fields under columns\n                        <TableRow>\n                          {/* Render input cells for each column starting from leftmost position */}\n                          {columns\n                            .filter((col) => {\n                              // Show cells for top-level columns with no children\n                              // or for child columns (level > 0)\n                              const hasChildren = columns.some(\n                                (c) => c.parentId === col.id\n                              );\n                              return (\n                                (col.level === 0 && !hasChildren) ||\n                                col.level > 0\n                              );\n                            })\n                            .map((col) => (\n                              <TableCell key={col.id} className=\"bg-gray-50\">\n                                <div className=\"h-8 flex items-center justify-center text-gray-400 text-xs\">\n                                  Add rows to enter default values\n                                </div>\n                              </TableCell>\n                            ))}\n                        </TableRow>\n                      )}\n                    </TableBody>\n                  </Table>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-2\">\n                  This preview shows how the table will appear to users filling\n                  out the form.\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      ) : (\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200 mb-4\">\n            <p className=\"font-medium mb-1\">Table Structure Guidelines:</p>\n            <ul className=\"list-disc pl-5 space-y-1\">\n              <li>\n                Create multiple{\" \"}\n                <span className=\"font-medium\">parent columns</span> using the\n                \"Add Top-Level Column\" button\n              </li>\n              <li>\n                Add up to 2 <span className=\"font-medium\">child columns</span>{\" \"}\n                under each parent using the \"+\" button\n              </li>\n              <li>\n                Child columns cannot have their own children (maximum 2 levels)\n              </li>\n            </ul>\n          </div>\n\n          <div>\n            <Label htmlFor=\"table-label\">Table Label</Label>\n            <Input\n              id=\"table-label\"\n              value={label}\n              onChange={(e) => setLabel(e.target.value)}\n              placeholder=\"Enter table question label\"\n              required\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label>Columns</Label>\n                {columns.map((column) => (\n                  <div\n                    key={column.id}\n                    className={cn(\n                      \"flex items-center gap-2 p-2 rounded-md\",\n                      column.level === 0\n                        ? \"bg-gray-50\"\n                        : \"bg-white border-l-2 border-gray-300\"\n                    )}\n                    style={{ marginLeft: `${column.level * 20}px` }}\n                  >\n                    <div className=\"flex-1 flex items-center gap-2\">\n                      {column.level > 0 && (\n                        <div className=\"w-4 h-4 flex items-center justify-center\">\n                          <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\n                        </div>\n                      )}\n                      <Input\n                        value={column.columnName}\n                        onChange={(e) =>\n                          handleColumnNameChange(column.id, e.target.value)\n                        }\n                        placeholder={`${\n                          column.level === 0 ? \"Parent\" : \"Child\"\n                        } Column`}\n                        className={cn(\n                          \"flex-1\",\n                          column.level === 0\n                            ? \"border-blue-200 bg-white\"\n                            : \"border-dashed\"\n                        )}\n                      />\n                      {column.level === 0 && (\n                        <div className=\"text-xs text-blue-500 font-medium\">\n                          Parent\n                        </div>\n                      )}\n                      {column.level > 0 && (\n                        <div className=\"text-xs text-gray-500 font-medium\">\n                          Child\n                        </div>\n                      )}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"icon\"\n                        onClick={() => handleAddChildColumn(column.id)}\n                        title={\n                          column.level > 0\n                            ? \"Child columns cannot have their own children\"\n                            : columns.filter(\n                                (col) => col.parentId === column.id\n                              ).length >= 2\n                            ? \"Maximum 2 child columns allowed\"\n                            : \"Add child column\"\n                        }\n                        disabled={\n                          column.level > 0 || // Disable if this is already a child column\n                          columns.filter((col) => col.parentId === column.id)\n                            .length >= 2 // Disable if already has 2 children\n                        }\n                      >\n                        <Plus\n                          className={cn(\n                            \"h-4 w-4\",\n                            (column.level > 0 ||\n                              columns.filter(\n                                (col) => col.parentId === column.id\n                              ).length >= 2) &&\n                              \"text-gray-300\"\n                          )}\n                        />\n                      </Button>\n\n                      <Button\n                        type=\"button\"\n                        variant=\"ghost\"\n                        size=\"icon\"\n                        onClick={() => handleRemoveColumn(column.id)}\n                        disabled={columns.length <= 1}\n                        title=\"Remove column\"\n                      >\n                        <Trash className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleAddColumn}\n                  className=\"mt-2\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Top-Level Column\n                </Button>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label>Rows</Label>\n                {rows.map((row, index) => (\n                  <div key={index} className=\"flex items-center gap-2\">\n                    <Input\n                      value={row.rowsName}\n                      onChange={(e) =>\n                        handleRowNameChange(index, e.target.value)\n                      }\n                      placeholder={`Row ${index + 1}`}\n                    />\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"icon\"\n                      onClick={() => handleRemoveRow(index)}\n                      disabled={false}\n                    >\n                      <Trash className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                ))}\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleAddRow}\n                  className=\"mt-2\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Row\n                </Button>\n              </div>\n            </div>\n\n            {showPreview && (\n              <div className=\"space-y-2\">\n                <Label>Table Preview</Label>\n                <div className=\"border rounded-md p-4 overflow-x-auto\">\n                  <Table>\n                    <TableHeader>\n                      {/* Calculate the maximum nesting level */}\n                      {(() => {\n                        const maxLevel = Math.max(\n                          ...columns.map((col) => col.level),\n                          0\n                        );\n\n                        // Create header rows for each level\n                        const headerRows = [];\n\n                        // First row with parent columns starting from leftmost position\n                        headerRows.push(\n                          <TableRow key=\"header-row-0\">\n                            {/* Render top-level (parent) columns starting from position 1 */}\n                            {columns\n                              .filter((col) => col.level === 0)\n                              .map((parentCol) => {\n                                // Count all descendants at all levels\n                                const getAllDescendants = (\n                                  colId: string\n                                ): Column[] => {\n                                  const directChildren = columns.filter(\n                                    (c) => c.parentId === colId\n                                  );\n                                  let allDescendants = [...directChildren];\n\n                                  directChildren.forEach((child) => {\n                                    allDescendants = [\n                                      ...allDescendants,\n                                      ...getAllDescendants(child.id),\n                                    ];\n                                  });\n\n                                  return allDescendants;\n                                };\n\n                                const descendants = getAllDescendants(\n                                  parentCol.id\n                                );\n                                const leafNodes = descendants.filter(\n                                  (d) =>\n                                    !columns.some((c) => c.parentId === d.id)\n                                );\n\n                                // If no descendants, it's a leaf node itself\n                                const colSpanCount =\n                                  descendants.length > 0\n                                    ? leafNodes.length || 1\n                                    : 1;\n\n                                return (\n                                  <TableHead\n                                    key={parentCol.id}\n                                    colSpan={colSpanCount}\n                                    className=\"text-center border-b\"\n                                  >\n                                    {parentCol.columnName || \"Column\"}\n                                  </TableHead>\n                                );\n                              })}\n                          </TableRow>\n                        );\n\n                        // Create header rows for each additional level\n                        for (let level = 1; level <= maxLevel; level++) {\n                          headerRows.push(\n                            <TableRow key={`header-row-${level}`}>\n                              {/* For each level, we need to find columns at that level and their parents */}\n                              {(() => {\n                                const columnsAtLevel = columns.filter(\n                                  (col) => col.level === level - 1\n                                );\n\n                                return columnsAtLevel.map((col) => {\n                                  // Get direct children of this column\n                                  const children = columns.filter(\n                                    (c) => c.parentId === col.id\n                                  );\n\n                                  // If no children, render an empty cell to maintain alignment\n                                  if (children.length === 0) {\n                                    return (\n                                      <TableHead\n                                        key={`empty-${col.id}`}\n                                        className=\"text-center border-b\"\n                                      >\n                                        {/* Empty cell to maintain column alignment */}\n                                      </TableHead>\n                                    );\n                                  }\n\n                                  // Render each child with appropriate colspan\n                                  return children.map((child) => {\n                                    // Calculate how many leaf nodes are under this child\n                                    const getAllDescendants = (\n                                      colId: string\n                                    ): Column[] => {\n                                      const directChildren = columns.filter(\n                                        (c) => c.parentId === colId\n                                      );\n                                      let allDescendants = [...directChildren];\n\n                                      directChildren.forEach((child) => {\n                                        allDescendants = [\n                                          ...allDescendants,\n                                          ...getAllDescendants(child.id),\n                                        ];\n                                      });\n\n                                      return allDescendants;\n                                    };\n\n                                    const descendants = getAllDescendants(\n                                      child.id\n                                    );\n                                    const leafNodes = descendants.filter(\n                                      (d) =>\n                                        !columns.some(\n                                          (c) => c.parentId === d.id\n                                        )\n                                    );\n\n                                    // If no descendants, it's a leaf node itself\n                                    const colSpanCount =\n                                      descendants.length > 0\n                                        ? leafNodes.length || 1\n                                        : 1;\n\n                                    return (\n                                      <TableHead\n                                        key={child.id}\n                                        colSpan={colSpanCount}\n                                        className=\"text-center border-b\"\n                                      >\n                                        {child.columnName || \"Child Column\"}\n                                      </TableHead>\n                                    );\n                                  });\n                                });\n                              })()}\n                            </TableRow>\n                          );\n                        }\n\n                        return headerRows;\n                      })()}\n                    </TableHeader>\n                    <TableBody>\n                      {rows.length > 0 ? (\n                        rows.map((row, rowIndex) => (\n                          <TableRow key={rowIndex}>\n                            {/* Render cells for each column starting from leftmost position */}\n                            {columns\n                              .filter((col) => {\n                                // Show cells for top-level columns with no children\n                                // or for child columns (level > 0)\n                                const hasChildren = columns.some(\n                                  (c) => c.parentId === col.id\n                                );\n                                return (\n                                  (col.level === 0 && !hasChildren) ||\n                                  col.level > 0\n                                );\n                              })\n                              .map((col) => (\n                                <TableCell\n                                  key={col.id}\n                                  className=\"bg-gray-50 p-0\"\n                                >\n                                  {/* Extract the numeric ID from the column ID */}\n                                  {(() => {\n                                    const columnId = extractColumnId(col.id);\n                                    return columnId ? (\n                                      <Input\n                                        type=\"text\"\n                                        placeholder=\"Enter value\"\n                                        className=\"border-0 h-8 text-center bg-transparent\"\n                                        value={getCellValue(rowIndex, col.id)}\n                                        onChange={(e) =>\n                                          handleCellValueChange(\n                                            rowIndex,\n                                            col.id,\n                                            e.target.value\n                                          )\n                                        }\n                                      />\n                                    ) : (\n                                      <div className=\"h-8 flex items-center justify-center text-gray-400 text-xs\">\n                                        Input field\n                                      </div>\n                                    );\n                                  })()}\n                                </TableCell>\n                              ))}\n                          </TableRow>\n                        ))\n                      ) : (\n                        // When no rows exist, show a single row with input fields under columns\n                        <TableRow>\n                          {/* Render input cells for each column starting from leftmost position */}\n                          {columns\n                            .filter((col) => {\n                              // Show cells for top-level columns with no children\n                              // or for child columns (level > 0)\n                              const hasChildren = columns.some(\n                                (c) => c.parentId === col.id\n                              );\n                              return (\n                                (col.level === 0 && !hasChildren) ||\n                                col.level > 0\n                              );\n                            })\n                            .map((col) => (\n                              <TableCell key={col.id} className=\"bg-gray-50\">\n                                <div className=\"h-8 flex items-center justify-center text-gray-400 text-xs\">\n                                  Add rows to enter default values\n                                </div>\n                              </TableCell>\n                            ))}\n                        </TableRow>\n                      )}\n                    </TableBody>\n                  </Table>\n                </div>\n                <p className=\"text-xs text-gray-500 mt-2\">\n                  This preview shows how the table will appear to users filling\n                  out the form.\n                </p>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex items-center justify-end space-x-4 mt-6\">\n            <Button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"bg-primary-500 text-white hover:bg-primary-600\"\n            >\n              {isSubmitting\n                ? \"Saving...\"\n                : isEditMode || existingTableData?.id\n                ? \"Update\"\n                : \"Save\"}\n            </Button>\n          </div>\n        </form>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAQA;;;AAjBA;;;;;;;;;;AAwCA,0DAA0D;AAC1D,SAAS,gBAAgB,IAAY;IACnC,gEAAgE;IAChE,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,IAAI,YAAY;QACd,MAAM,KAAK,SAAS,UAAU,CAAC,EAAE,EAAE;QACnC,wEAAwE;QACxE,IAAI,KAAK,KAAK,MAAM,YAAY;YAC9B,OAAO;QACT;IACF;IAEA,kEAAkE;IAClE,MAAM,cAAc,KAAK,KAAK,CAAC;IAC/B,IAAI,aAAa;QACf,MAAM,KAAK,SAAS,WAAW,CAAC,EAAE,EAAE;QACpC,wEAAwE;QACxE,IAAI,KAAK,KAAK,MAAM,YAAY;YAC9B,OAAO;QACT;IACF;IAEA,oDAAoD;IACpD,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,IAAI,gBAAgB;QAClB,MAAM,KAAK,SAAS,cAAc,CAAC,EAAE,EAAE;QACvC,wEAAwE;QACxE,IAAI,KAAK,KAAK,MAAM,YAAY;YAC9B,OAAO;QACT;IACF;IAEA,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM;IACnD,OAAO;AACT;AA6BO,SAAS,qBAAqB,EACnC,SAAS,EACT,cAAc,EACd,YAAY,KAAK,EACjB,aAAa,KAAK,EAClB,iBAAiB,EACS;;IAC1B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,SAAS;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;yCAAY;YAC/C,IAAI,mBAAmB,cAAc;gBACnC,2DAA2D;gBAC3D,MAAM,mBAA6B,EAAE;gBAErC,mEAAmE;gBACnE,kBAAkB,YAAY,CAAC,OAAO;qDAAC,CAAC;wBACtC,2BAA2B;wBAC3B,MAAM,eAAuB;4BAC3B,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;4BACzB,YAAY,UAAU,UAAU;4BAChC,OAAO;4BACP,gBAAgB;wBAClB;wBAEA,2CAA2C;wBAC3C,iBAAiB,IAAI,CAAC;wBAEtB,QAAQ,GAAG,CACT,CAAC,sBAAsB,EAAE,UAAU,UAAU,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;wBAGxE,0EAA0E;wBAC1E,IAAI,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,MAAM,GAAG,GAAG;4BAC/D,QAAQ,GAAG,CACT,CAAC,WAAW,EAAE,UAAU,YAAY,CAAC,MAAM,CAAC,2BAA2B,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC;4BAGlG,UAAU,YAAY,CAAC,OAAO;iEAAC,CAAC;oCAC9B,MAAM,cAAsB;wCAC1B,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE;wCACxB,YAAY,SAAS,UAAU;wCAC/B,OAAO;wCACP,UAAU,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;wCAC/B,gBAAgB,UAAU,EAAE;oCAC9B;oCAEA,0CAA0C;oCAC1C,iBAAiB,IAAI,CAAC;oCAEtB,QAAQ,GAAG,CACT,CAAC,qBAAqB,EAAE,SAAS,UAAU,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,eAAe,EAAE,UAAU,UAAU,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;gCAEnI;;wBACF;oBACF;;gBAEA,QAAQ,GAAG,CAAC,mCAAmC;gBAE/C,OAAO,iBAAiB,MAAM,GAAG,IAC7B,mBACA;oBAAC;wBAAE,IAAI;wBAAS,YAAY;wBAAI,OAAO;oBAAE;iBAAE;YACjD;YAEA,OAAO;gBAAC;oBAAE,IAAI;oBAAS,YAAY;oBAAI,OAAO;gBAAE;aAAE;QACpD;;IAEA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;yCAO7B;YACA,IACE,mBAAmB,aACnB,kBAAkB,SAAS,CAAC,MAAM,GAAG,GACrC;gBACA,OAAO,kBAAkB,SAAS,CAAC,GAAG;qDAAC,CAAC;wBACtC,oDAAoD;wBACpD,MAAM,aAAwB,CAAC;wBAE/B,IAAI,kBAAkB,UAAU,EAAE;4BAChC,QAAQ,GAAG,CACT,mCACA,IAAI,EAAE,EACN,kBAAkB,UAAU;4BAE9B,kEAAkE;4BAClE,OAAO,OAAO,CAAC,kBAAkB,UAAU,EAAE,OAAO;iEAClD,CAAC,CAAC,KAAK,MAAqB;oCAC1B,MAAM,CAAC,UAAU,MAAM,GAAG,IAAI,KAAK,CAAC;oCACpC,IAAI,SAAS,WAAW,IAAI,EAAE,EAAE;wCAC9B,oDAAoD;wCACpD,MAAM,QAAQ,CAAC,IAAI,EAAE,UAAU;wCAC/B,8CAA8C;wCAC9C,MAAM,YACJ,OAAO,UAAU,YAAY,UAAU,OACnC,MAAM,KAAK,IAAI,KACf,SAAS;wCACf,UAAU,CAAC,MAAM,GAAG;wCACpB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,MAAM,GAAG,EAAE,WAAW;oCAC3D;gCACF;;wBAEJ;wBAEA,QAAQ,GAAG,CAAC,8BAA8B,IAAI,EAAE,EAAE;wBAElD,OAAO;4BACL,IAAI,IAAI,EAAE;4BACV,UAAU,IAAI,QAAQ;4BACtB,eAAe,IAAI,aAAa,IAAI,EAAE;4BACtC,YAAY;wBACd;oBACF;;YACF;YACA,2BAA2B;YAC3B,OAAO;gBAAC;oBAAE,UAAU;oBAAK,eAAe,EAAE;oBAAE,YAAY,CAAC;gBAAE;aAAE;QAC/D;;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,8DAA8D;IAC9D,+DAA+D;IAC/D,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU;QACd,OAAO;YACL,kDAAkD;YAClD,kDAAkD;YAClD,MAAM,SAAS,SAAS;YACxB;YACA,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;QAC3D;IACF,CAAC;IAED,wCAAwC;IACxC,MAAM,wBAAwB,CAC5B,UACA,UACA;QAEA,QAAQ,CAAC;YACP,MAAM,UAAU;mBAAI;aAAS;YAC7B,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO;YAC/B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,EAAE;gBACjC,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC;YAClC;YACA,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG;gBAC7B,GAAG,OAAO,CAAC,SAAS,CAAC,UAAU;gBAC/B,CAAC,SAAS,EAAE;YACd;YACA,OAAO;QACT;IACF;IAEA,iCAAiC;IACjC,MAAM,eAAe,CAAC,UAAkB;QACtC,OAAO,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,IAAI;IACnD;IAEA,4BAA4B;IAC5B,MAAM,eAAe;QACnB,QAAQ,CAAC,WAAa;mBACjB;gBACH;oBACE,UAAU,GAAG,SAAS,MAAM,GAAG,GAAG;oBAClC,eAAe,EAAE;oBACjB,YAAY,CAAC;gBACf;aACD;IACH;IAEA,yBAAyB;IAEzB,oBAAoB;IACpB,oFAAoF;IACpF,qEAAqE;IAErE,+BAA+B,iBAC/B,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAc;;;;;;kCAC5B,6LAAC,8HAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,SAAQ;wBAAU,MAAK;wBAAK,SAAS;;0CACzD,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAGrC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;sCACJ,6LAAC,6HAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;;kDACP,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAAwB;;;;;;oCAC5C,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,6HAAA,CAAA,YAAS;sDACP,OAAO,UAAU,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;2CAD7B,OAAO,EAAE;;;;;kDAI3B,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAAY;;;;;;;;;;;;;;;;;sCAGrC,6LAAC,6HAAA,CAAA,YAAS;sCACP,KAAK,GAAG,CAAC,CAAC,KAAK,yBACd,6LAAC,6HAAA,CAAA,WAAQ;;sDACP,6LAAC,6HAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,WAAW;;;;;;wCAEb,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,6HAAA,CAAA,YAAS;0DACR,cAAA,6LAAC,6HAAA,CAAA,QAAK;oDACJ,OAAO,aAAa,UAAU,OAAO,EAAE;oDACvC,UAAU,CAAC,IACT,sBAAsB,UAAU,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oDAE3D,aAAY;oDACZ,WAAU;;;;;;+CAPE,GAAG,SAAS,CAAC,EAAE,OAAO,EAAE,EAAE;;;;;sDAW5C,6LAAC,6HAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,gBAAgB;gDAC/B,UAAU,KAAK,MAAM,IAAI;gDACzB,OAAM;0DAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;mCAzBR;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkCzB,6BAA6B,GAE7B,MAAM,kBAAkB;QACtB,WAAW;eACN;YACH;gBACE,IAAI;gBACJ,YAAY;gBACZ,OAAO;YACT;SACD;IACH;IAEA,wCAAwC;IACxC,MAAM,uBAAuB,CAAC;QAC5B,uCAAuC;QACvC,MAAM,eAAe,eAAe;QACpC,IAAI,CAAC,cAAc;QAEnB,wDAAwD;QACxD,+DAA+D;QAC/D,IAAI,aAAa,KAAK,GAAG,GAAG;YAC1B,MAAM;gBACJ,OAAO;gBACP,aACE;gBACF,SAAS;YACX;YACA;QACF;QAEA,yCAAyC;QACzC,MAAM,aAAa,QAAQ,MAAM,CAC/B,CAAC,MAAQ,IAAI,QAAQ,KAAK,UAC1B,MAAM;QAER,yCAAyC;QACzC,IAAI,cAAc,GAAG;YACnB,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,eAAe,EAC3B,aAAa,UAAU,IAAI,UAC5B,uCAAuC,CAAC;gBACzC,SAAS;YACX;YACA;QACF;QAEA,kDAAkD;QAClD,MAAM,kBAAkB,SAAS,KAAK,CAAC;QACvC,MAAM,aAAa,kBACf,SAAS,eAAe,CAAC,EAAE,EAAE,MAC7B;QAEJ,MAAM,YAAoB;YACxB,IAAI;YACJ,YAAY;YACZ,UAAU;YACV,OAAO,aAAa,KAAK,GAAG;YAC5B,oDAAoD;YACpD,gBAAgB;QAClB;QAEA,mEAAmE;QACnE,MAAM,aAAa;eAAI;SAAQ;QAC/B,MAAM,iBAAiB,mBAAmB;QAE1C,mEAAmE;QACnE,IACE,mBAAmB,CAAC,KACpB,mBAAmB,QAAQ,SAAS,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,WACzD;YACA,MAAM,cAAc,QAAQ,SAAS,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;YAC1D,WAAW,MAAM,CAAC,cAAc,GAAG,GAAG;QACxC,OAAO;YACL,yCAAyC;YACzC,WAAW,MAAM,CAAC,iBAAiB,GAAG,GAAG;QAC3C;QAEA,WAAW;IACb;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC;QACtB,OAAO,QAAQ,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;IAC1C;IAEA,sDAAsD;IACtD,MAAM,qBAAqB,CAAC;QAC1B,MAAM,cAAc,QAAQ,SAAS,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;QAC1D,IAAI,gBAAgB,CAAC,GAAG,OAAO,CAAC;QAEhC,kCAAkC;QAClC,IAAI,iBAAiB;QACrB,IAAI,aAAa;QAEjB,IAAK,IAAI,IAAI,cAAc,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrD,IAAI,OAAO,CAAC,EAAE,CAAC,QAAQ,KAAK,UAAU;gBACpC,8BAA8B;gBAC9B,iBAAiB;gBACjB,aAAa;YACf,OAAO,IAAI,eAAe,OAAO,CAAC,EAAE,EAAE,WAAW;gBAC/C,qCAAqC;gBACrC,iBAAiB;gBACjB,aAAa;YACf,OAAO;gBAEL;YACF;QACF;QAEA,qDAAqD;QACrD,OAAO,aAAa,iBAAiB;IACvC;IAEA,uDAAuD;IACvD,MAAM,iBAAiB,CAAC,QAAgB;QACtC,IAAI,OAAO,QAAQ,KAAK,YAAY,OAAO;QAC3C,IAAI,CAAC,OAAO,QAAQ,EAAE,OAAO;QAE7B,MAAM,SAAS,eAAe,OAAO,QAAQ;QAC7C,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAO,eAAe,QAAQ;IAChC;IAEA,+CAA+C;IAC/C,MAAM,qBAAqB,CAAC;QAC1B,uCAAuC;QACvC,IAAI,QAAQ,MAAM,IAAI,GAAG;YACvB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,iDAAiD;QACjD,MAAM,mBAAmB,QACtB,MAAM,CAAC,CAAC;YACP,kBAAkB;YAClB,IAAI,IAAI,QAAQ,KAAK,UAAU,OAAO;YACtC,8CAA8C;YAC9C,OAAO,eAAe,KAAK;QAC7B,GACC,GAAG,CAAC,CAAC,MAAQ,IAAI,EAAE;QAEtB,yDAAyD;QACzD,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAC;YACjC,OAAO,IAAI,EAAE,KAAK,YAAY,CAAC,iBAAiB,QAAQ,CAAC,IAAI,EAAE;QACjE;QAEA,2BAA2B;QAC3B,WAAW;IACb;IAEA,MAAM,kBAAkB,CAAC;QACvB,sDAAsD;QACtD,MAAM,UAAU;eAAI;SAAK;QACzB,QAAQ,MAAM,CAAC,OAAO;QACtB,QAAQ;IACV;IAEA,MAAM,yBAAyB,CAAC,IAAY;QAC1C,MAAM,aAAa,QAAQ,GAAG,CAAC,CAAC;YAC9B,IAAI,IAAI,EAAE,KAAK,IAAI;gBACjB,OAAO;oBAAE,GAAG,GAAG;oBAAE,YAAY;gBAAM;YACrC;YACA,OAAO;QACT;QACA,WAAW;IACb;IAEA,MAAM,sBAAsB,CAAC,OAAe;QAC1C,MAAM,UAAU;eAAI;SAAK;QACzB,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG;QAC1B,QAAQ;IACV;IAEA,8EAA8E;IAC9E,MAAM,8BAA8B;QAClC,uDAAuD;QACvD,MAAM,YAAY,cAAc,mBAAmB;QAEnD,sDAAsD;QACtD,MAAM,cAAc,IAAI;QAExB,mDAAmD;QACnD,MAAM,iBAAiB,IAAI;QAE3B,oEAAoE;QACpE,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,IAAI,QAAQ,EAAE;gBAChB,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,QAAQ,GAAG;oBACrC,eAAe,GAAG,CAAC,IAAI,QAAQ,EAAE,EAAE;gBACrC;gBACA,eAAe,GAAG,CAAC,IAAI,QAAQ,GAAG,KAAK,IAAI,EAAE;YAC/C;QACF;QAEA,uEAAuE;QACvE,IAAI,aAAa,mBAAmB,cAAc;YAChD,4CAA4C;YAC5C,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI;oBACzB,8DAA8D;oBAC9D,MAAM,QAAQ,IAAI,EAAE,CAAC,KAAK,CAAC;oBAC3B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;wBACrB,MAAM,OAAO,SAAS,KAAK,CAAC,EAAE,EAAE;wBAChC,6CAA6C;wBAC7C,MAAM,iBAAiB,kBAAkB,YAAY,CAAC,IAAI,CACxD,CAAC,KAAO,GAAG,EAAE,KAAK;wBAEpB,IAAI,gBAAgB;4BAClB,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;wBAC1B;oBACF;gBACF;YACF;QACF;QAEA,qEAAqE;QACrE,IAAI,SACF,KAAK,GAAG,IACH,MAAM,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,KAAO,MAAM,OAC9C,mBAAmB,cAAc,IAAI,CAAC,MAAQ,IAAI,EAAE,KAAK;YAAC;SAAE,EAChE,KACE;QAEN,iDAAiD;QACjD,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,IAAI,UAAU,CAAC,IAAI,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,EAAE,GAAG;gBACrD,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;YAC1B;QACF;QAEA,gDAAgD;QAChD,MAAM,aAIA,EAAE;QAER,kGAAkG;QAClG,MAAM,uBAAuB,IAAI;QAEjC,uFAAuF;QACvF,uEAAuE;QACvE,wDAAwD;QACxD,MAAM,WAAW,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC,MAAQ,IAAI,KAAK,IAAI,IAAI;QAEnE,+EAA+E;QAC/E,MAAM,kBAAkB,QAAQ,MAAM,CACpC,CAAC,MAAQ,IAAI,KAAK,KAAK,KAAK,IAAI,UAAU,CAAC,IAAI;QAGjD,kCAAkC;QAClC,gBAAgB,OAAO,CAAC,CAAC;YACvB,MAAM,WAAW,YAAY,GAAG,CAAC,IAAI,EAAE;YAEvC,MAAM,YAIF;gBACF,YAAY,IAAI,UAAU,CAAC,IAAI;YACjC;YAEA,iDAAiD;YACjD,IAAI,aAAa,UAAU;gBACzB,UAAU,EAAE,GAAG;YACjB;YAEA,0CAA0C;YAC1C,MAAM,iBAAiB,WAAW,MAAM;YACxC,WAAW,IAAI,CAAC;YAEhB,mFAAmF;YACnF,qBAAqB,GAAG,CAAC,IAAI,EAAE,EAAE;QACnC;QAEA,iDAAiD;QACjD,IAAK,IAAI,QAAQ,GAAG,SAAS,UAAU,QAAS;YAC9C,iDAAiD;YACjD,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,IAAI,KAAK,KAAK,SAAS,IAAI,UAAU,CAAC,IAAI;YAGrD,oCAAoC;YACpC,eAAe,OAAO,CAAC,CAAC;gBACtB,MAAM,WAAW,YAAY,GAAG,CAAC,IAAI,EAAE;gBAEvC,+BAA+B;gBAC/B,MAAM,YAIF;oBACF,YAAY,IAAI,UAAU,CAAC,IAAI;gBACjC;gBAEA,iDAAiD;gBACjD,IAAI,aAAa,UAAU;oBACzB,UAAU,EAAE,GAAG;gBACjB;gBAEA,qEAAqE;gBACrE,IAAI,IAAI,QAAQ,EAAE;oBAChB,IAAI,WAAW;wBACb,mDAAmD;wBACnD,MAAM,eAAe,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,QAAQ;wBAC9D,MAAM,aAAa,YAAY,GAAG,CAAC,IAAI,QAAQ;wBAE/C,IAAI,YAAY;4BACd,+BAA+B;4BAC/B,UAAU,cAAc,GAAG;wBAC7B,OAAO;4BACL,QAAQ,IAAI,CACV,CAAC,wCAAwC,EAAE,IAAI,UAAU,CAAC,aAAa,EAAE,IAAI,QAAQ,CAAC,CAAC,CAAC;4BAE1F,iFAAiF;4BACjF,MAAM;gCACJ,OAAO;gCACP,aAAa,CAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,yEAAyE,CAAC;gCACjH,SAAS;4BACX;wBACF;oBACF,OAAO;wBACL,qDAAqD;wBACrD,MAAM,iBAAiB,qBAAqB,GAAG,CAAC,IAAI,QAAQ;wBAE5D,IAAI,mBAAmB,WAAW;4BAChC,4CAA4C;4BAC5C,UAAU,cAAc,GAAG,iBAAiB,GAAG,2BAA2B;4BAC1E,QAAQ,GAAG,CACT,CAAC,4BAA4B,EAAE,IAAI,UAAU,CAAC,KAAK,EACjD,iBAAiB,EAClB,WAAW,EACV,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,QAAQ,GAAG,WAC7C,EAAE,CAAC;wBAER,OAAO;4BACL,QAAQ,IAAI,CACV,CAAC,2CAA2C,EAAE,IAAI,UAAU,CAAC,aAAa,EAAE,IAAI,QAAQ,CAAC,CAAC,CAAC;4BAE7F,iFAAiF;4BACjF,MAAM;gCACJ,OAAO;gCACP,aAAa,CAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,yEAAyE,CAAC;gCACjH,SAAS;4BACX;wBACF;oBACF;gBACF;gBAEA,0CAA0C;gBAC1C,MAAM,iBAAiB,WAAW,MAAM;gBACxC,WAAW,IAAI,CAAC;gBAEhB,mFAAmF;gBACnF,qBAAqB,GAAG,CAAC,IAAI,EAAE,EAAE;YACnC;QACF;QAEA,yDAAyD;QACzD,MAAM,0BAA0B,WAAW,MAAM,CAAC,CAAC;YACjD,IAAI,IAAI,cAAc,KAAK,WAAW;gBACpC,OAAO,OAAO,2BAA2B;YAC3C;YAEA,IAAI,WAAW;gBACb,4DAA4D;gBAC5D,+BAA+B;gBAC/B,IAAI,IAAI,cAAc,IAAI,GAAG;oBAC3B,OAAO,MAAM,mCAAmC;gBAClD;YACF,OAAO;gBACL,0EAA0E;gBAC1E,0EAA0E;gBAC1E,IAAI,IAAI,cAAc,IAAI,KAAK,IAAI,cAAc,GAAG,WAAW,MAAM,EAAE;oBACrE,OAAO,MAAM,mBAAmB;gBAClC;gBAEA,0DAA0D;gBAC1D,MAAM,eAAe,UAAU,CAAC,IAAI,cAAc,GAAG,EAAE,EAAE,2BAA2B;gBACpF,IAAI,gBAAgB,aAAa,cAAc,KAAK,WAAW;oBAC7D,OAAO,MAAM,+DAA+D;gBAC9E;YACF;YAEA,OAAO;QACT;QAEA,IAAI,wBAAwB,MAAM,GAAG,GAAG;YACtC,QAAQ,KAAK,CACX,2CACA;YAGF,8CAA8C;YAC9C,wBAAwB,OAAO,CAAC,CAAC;gBAC/B,IAAI,cAAc,GAAG;YACvB;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,MAAM,EAAE,wBAAwB,MAAM,CAAC,sFAAsF,CAAC;gBAC5I,SAAS;YACX;QACF;QAEA,sCAAsC;QACtC,QAAQ,GAAG,CAAC,sBAAsB;QAClC,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,GAAG,EAAE,cAAc;QACvB,kBAAkB;QAClB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,qCAAqC;YACrC,MAAM,aAAa;YAEnB,kCAAkC;YAClC,IAAI,gBAAgB;YACpB,IAAI,cAAc,MAAM,KAAK,GAAG;gBAC9B,mCAAmC;gBACnC,gBAAgB;oBACd;wBAAE,UAAU;wBAAS,eAAe,EAAE;wBAAE,YAAY,CAAC;oBAAE;iBACxD;gBACD,QAAQ,GAAG,CAAC;YACd;YAEA,kDAAkD;YAClD,yCAAyC;YACzC,MAAM,kBAAkB;mBAAI;aAAc,CAAC,IAAI,CAAC,CAAC,GAAG;gBAClD,oCAAoC;gBACpC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;oBAChB,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;gBACpB;gBACA,mDAAmD;gBACnD,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC;gBAClB,IAAI,EAAE,EAAE,EAAE,OAAO;gBACjB,sDAAsD;gBACtD,OAAO,cAAc,OAAO,CAAC,KAAK,cAAc,OAAO,CAAC;YAC1D;YAEA,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,MAAM,UAAU,gBAAgB,GAAG,CAAC,CAAC,KAAK;gBACxC,MAAM,UAIF;oBACF,UAAU,IAAI,QAAQ,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,WAAW,GAAG;gBACxD;gBAEA,8CAA8C;gBAC9C,IAAI,cAAc,mBAAmB,IAAI;oBACvC,IACE,IAAI,EAAE,IACN,mBAAmB,WAAW,KAAK,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,EAAE,GACzD;wBACA,QAAQ,EAAE,GAAG,IAAI,EAAE;oBACrB;gBACF;gBAEA,0DAA0D;gBAC1D,MAAM,gBAAoC,EAAE;gBAE5C,yCAAyC;gBACzC,IAAI,IAAI,UAAU,EAAE;oBAClB,OAAO,OAAO,CAAC,IAAI,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;wBACvD,gEAAgE;wBAChE,IAAI,SAAS,CAAC,SAAS,QAAQ,CAAC,SAAS;4BACvC,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;4BAChD,IAAI,QAAQ;gCACV,uEAAuE;gCACvE,IAAI,aAA4B;gCAEhC,IAAI,cAAc,mBAAmB,IAAI;oCACvC,yCAAyC;oCACzC,aAAa,gBAAgB,OAAO,EAAE;gCACxC,OAAO;oCACL,8DAA8D;oCAC9D,MAAM,iBAAiB,WAAW,SAAS,CACzC,CAAC,SAAW,OAAO,UAAU,KAAK,OAAO,UAAU,CAAC,IAAI;oCAG1D,IAAI,mBAAmB,CAAC,GAAG;wCACzB,gCAAgC;wCAChC,aAAa,iBAAiB;wCAC9B,QAAQ,GAAG,CACT,CAAC,2BAA2B,EAAE,WAAW,aAAa,EAAE,OAAO,UAAU,CAAC,CAAC,CAAC;oCAEhF;gCACF;gCAEA,IAAI,YAAY;oCACd,MAAM,eAAe;wCACnB,UAAU;wCACV,OAAO,OAAO,OAAO,IAAI;wCACzB,MAAM,OAAO,OAAO,IAAI;oCAC1B;oCACA,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC,EAChD;oCAEF,QAAQ,GAAG,CACT,CAAC,uBAAuB,EAAE,SAAS,mBAAmB,EAAE,WAAW,eAAe,EAAE,OAAO,UAAU,EAAE;oCAEzG,cAAc,IAAI,CAAC;gCACrB,OAAO;oCACL,QAAQ,IAAI,CACV,CAAC,2CAA2C,EAAE,SAAS,aAAa,EAAE,OAAO,UAAU,EAAE;gCAE7F;4BACF;wBACF;oBACF;gBACF;gBAEA,iEAAiE;gBACjE,IAAI,IAAI,aAAa,IAAI,IAAI,aAAa,CAAC,MAAM,GAAG,GAAG;oBACrD,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;wBACzB,gDAAgD;wBAChD,IACE,CAAC,cAAc,IAAI,CACjB,CAAC,aAAe,WAAW,QAAQ,KAAK,GAAG,QAAQ,GAErD;4BACA,cAAc,IAAI,CAAC;wBACrB;oBACF;gBACF;gBAEA,QAAQ,GAAG,CACT,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,KAAK,EAAE,cAAc,MAAM,CAAC,8BAA8B,CAAC;gBAGrF,sCAAsC;gBACtC,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,wCAAwC;oBACxC,MAAM,qBAAqB,cAAc,MAAM,CAAC,CAAC;wBAC/C,MAAM,UACJ,GAAG,QAAQ,IACX,OAAO,GAAG,QAAQ,KAAK,YACvB,GAAG,QAAQ,GAAG,KACd,GAAG,KAAK,IACR,OAAO,GAAG,KAAK,EAAE,IAAI,OAAO;wBAE9B,IAAI,CAAC,SAAS;4BACZ,QAAQ,IAAI,CAAC,CAAC,oCAAoC,CAAC,EAAE;wBACvD;wBACA,OAAO;oBACT;oBAEA,QAAQ,GAAG,CACT,CAAC,IAAI,EAAE,QAAQ,QAAQ,CAAC,KAAK,EAAE,mBAAmB,MAAM,CAAC,qCAAqC,CAAC;oBAGjG,yCAAyC;oBACzC,IAAI,mBAAmB,MAAM,GAAG,GAAG;wBACjC,QAAQ,aAAa,GAAG;wBACxB,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,QAAQ,QAAQ,CAAC,CAAC,CAAC,EACnD,KAAK,SAAS,CAAC;oBAEnB,OAAO;wBACL,QAAQ,IAAI,CACV,CAAC,sCAAsC,EAAE,QAAQ,QAAQ,EAAE;oBAE/D;gBACF;gBAEA,OAAO;YACT;YAEA,IAAI;YAEJ,iEAAiE;YACjE,IAAI,mBAAmB,IAAI;gBACzB,8CAA8C;gBAC9C,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,aAAa,kBAAkB,EAAE;gBAC7C,QAAQ,GAAG,CAAC,UAAU,MAAM,IAAI;gBAChC,QAAQ,GAAG,CAAC,gBAAgB,KAAK,SAAS,CAAC,YAAY,MAAM;gBAC7D,QAAQ,GAAG,CAAC,aAAa,KAAK,SAAS,CAAC,SAAS,MAAM;gBACvD,QAAQ,GAAG,CAAC;gBAEZ,wBAAwB;gBACxB,QAAQ,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EACtB,kBAAkB,EAAE,EACpB,MAAM,IAAI,IACV,YACA;gBAGF,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,mBAAmB;gBACnB,QAAQ,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,IAAI,IAAI,WAAW,YAAY;gBAE/D,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBAEA,oCAAoC;gBACpC,SAAS;gBACT,WAAW;oBACT;wBACE,IAAI;wBACJ,YAAY;wBACZ,OAAO;oBACT;iBACD;gBACD,QAAQ,EAAE,GAAG,4CAA4C;YAC3D;YAEA,0BAA0B;YAC1B,IAAI,kBAAkB,OAAO,IAAI;gBAC/B,eAAe,MAAM,EAAE;YACzB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,mDAAmD;YACnD,IAAI,eAAe,mBAAmB,KAClC,oCACA;YAEJ,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC5C,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,eAAe,MAAM,OAAO;YAC9B;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,+CAA+C;IAC/C,MAAM,kBAAkB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAErD,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,WAAW;gBACb,MAAM;wEAAoB,CAAC;wBACzB,oDAAoD;wBACpD,IAAI,MAAM,UAAU,EAAE;4BACpB,MAAM,eAAe;wBACvB;wBAEA,0BAA0B;wBAC1B;oBACF;;gBAEA,mDAAmD;gBAEnD,yCAAyC;gBACzC,IAAI,gBAAgB,OAAO,EAAE;oBAC3B,MAAM,eAAe,gBAAgB,OAAO;oBAE5C,aAAa,gBAAgB,CAAC,eAAe;gBAC/C;gBAEA,wCAAwC;gBACxC,SAAS,gBAAgB,CAAC,eAAe;gBAEzC,yDAAyD;gBACzD,MAAM,mBAAmB,SAAS,gBAAgB,CAChD;gBAGF,iBAAiB,OAAO;sDAAC,CAAC;wBACxB,QAAQ,gBAAgB,CAAC,eAAe;oBAC1C;;gBAEA;sDAAO;wBACL,mCAAmC;wBAEnC,IAAI,gBAAgB,OAAO,EAAE;4BAC3B,gBAAgB,OAAO,CAAC,mBAAmB,CACzC,eACA;wBAEJ;wBAEA,SAAS,mBAAmB,CAAC,eAAe;wBAE5C,iBAAiB,OAAO;8DAAC,CAAC;gCACxB,QAAQ,mBAAmB,CAAC,eAAe;4BAC7C;;oBACF;;YACF;QACF;yCAAG;QAAC;QAAW;QAAc;KAAQ;IAErC,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,cAAc,mBAAmB,KAC9B,wBACA;;;;;;kCAEN,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;kCAET,4BACC;;8CACE,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;yDAIhC;;8CACE,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;0BAOnC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;;oCAAG;kDACc,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAsB;oCAAI;;;;;;;0CAG1E,6LAAC;;oCAAG;kDACU,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAoB;;;;;;;0CAGhE,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;YAMP,0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAc;;;;;;0CAC7B,6LAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,QAAQ;;;;;;;;;;;;kCAIZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;4CACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oDAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0CACA,OAAO,KAAK,KAAK,IACb,eACA;oDAEN,OAAO;wDAAE,YAAY,GAAG,OAAO,KAAK,GAAG,GAAG,EAAE,CAAC;oDAAC;;sEAE9C,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,KAAK,GAAG,mBACd,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;;;;;;;;;;8EAGnB,6LAAC,6HAAA,CAAA,QAAK;oEACJ,OAAO,OAAO,UAAU;oEACxB,UAAU,CAAC,IACT,uBAAuB,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oEAElD,aAAa,GACX,OAAO,KAAK,KAAK,IAAI,WAAW,QACjC,OAAO,CAAC;oEACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,UACA,OAAO,KAAK,KAAK,IACb,6BACA;;;;;;gEAGP,OAAO,KAAK,KAAK,mBAChB,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;gEAIpD,OAAO,KAAK,GAAG,mBACd,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAKvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,qBAAqB,OAAO,EAAE;oEAC7C,OACE,OAAO,KAAK,GAAG,IACX,iDACA,QAAQ,MAAM,CACZ,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EACnC,MAAM,IAAI,IACZ,oCACA;oEAEN,UACE,OAAO,KAAK,GAAG,KAAK,4CAA4C;oEAChE,QAAQ,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EAC/C,MAAM,IAAI,EAAE,oCAAoC;;8EAGrD,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEACH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,WACA,CAAC,OAAO,KAAK,GAAG,KACd,QAAQ,MAAM,CACZ,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EACnC,MAAM,IAAI,CAAC,KACb;;;;;;;;;;;8EAKR,6LAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,mBAAmB,OAAO,EAAE;oEAC3C,UAAU,QAAQ,MAAM,IAAI;oEAC5B,OAAM;8EAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;;mDAlFhB,OAAO,EAAE;;;;;0DAuFlB,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;4CACN,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC,6HAAA,CAAA,QAAK;4DACJ,OAAO,IAAI,QAAQ;4DACnB,UAAU,CAAC,IACT,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK;4DAE3C,aAAa,CAAC,IAAI,EAAE,QAAQ,GAAG;;;;;;sEAEjC,6LAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,gBAAgB;4DAC/B,UAAU;sEAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;mDAfX;;;;;0DAmBZ,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAMtC,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;8DACJ,6LAAC,6HAAA,CAAA,cAAW;8DAET,CAAC;wDACA,MAAM,WAAW,KAAK,GAAG,IACpB,QAAQ,GAAG,CAAC,CAAC,MAAQ,IAAI,KAAK,GACjC;wDAGF,oCAAoC;wDACpC,MAAM,aAAa,EAAE;wDAErB,gEAAgE;wDAChE,WAAW,IAAI,eACb,6LAAC,6HAAA,CAAA,WAAQ;sEAEN,QACE,MAAM,CAAC,CAAC,MAAQ,IAAI,KAAK,KAAK,GAC9B,GAAG,CAAC,CAAC;gEACJ,sCAAsC;gEACtC,MAAM,oBAAoB,CACxB;oEAEA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,IAAM,EAAE,QAAQ,KAAK;oEAExB,IAAI,iBAAiB;2EAAI;qEAAe;oEAExC,eAAe,OAAO,CAAC,CAAC;wEACtB,iBAAiB;+EACZ;+EACA,kBAAkB,MAAM,EAAE;yEAC9B;oEACH;oEAEA,OAAO;gEACT;gEAEA,MAAM,cAAc,kBAClB,UAAU,EAAE;gEAEd,MAAM,YAAY,YAAY,MAAM,CAClC,CAAC,IACC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,EAAE,EAAE;gEAG5C,6CAA6C;gEAC7C,MAAM,eACJ,YAAY,MAAM,GAAG,IACjB,UAAU,MAAM,IAAI,IACpB;gEAEN,qBACE,6LAAC,6HAAA,CAAA,YAAS;oEAER,SAAS;oEACT,WAAU;8EAET,UAAU,UAAU,IAAI;mEAJpB,UAAU,EAAE;;;;;4DAOvB;2DA/CU;;;;;wDAmDhB,+CAA+C;wDAC/C,IAAK,IAAI,QAAQ,GAAG,SAAS,UAAU,QAAS;4DAC9C,WAAW,IAAI,eACb,6LAAC,6HAAA,CAAA,WAAQ;0EAEN,CAAC;oEACA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,IAAI,KAAK,KAAK,QAAQ;oEAGjC,OAAO,eAAe,GAAG,CAAC,CAAC;wEACzB,qCAAqC;wEACrC,MAAM,WAAW,QAAQ,MAAM,CAC7B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;wEAG9B,6DAA6D;wEAC7D,IAAI,SAAS,MAAM,KAAK,GAAG;4EACzB,qBACE,6LAAC,6HAAA,CAAA,YAAS;gFAER,WAAU;+EADL,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;;;;;wEAM5B;wEAEA,6CAA6C;wEAC7C,OAAO,SAAS,GAAG,CAAC,CAAC;4EACnB,qDAAqD;4EACrD,MAAM,oBAAoB,CACxB;gFAEA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,IAAM,EAAE,QAAQ,KAAK;gFAExB,IAAI,iBAAiB;uFAAI;iFAAe;gFAExC,eAAe,OAAO,CAAC,CAAC;oFACtB,iBAAiB;2FACZ;2FACA,kBAAkB,MAAM,EAAE;qFAC9B;gFACH;gFAEA,OAAO;4EACT;4EAEA,MAAM,cAAc,kBAClB,MAAM,EAAE;4EAEV,MAAM,YAAY,YAAY,MAAM,CAClC,CAAC,IACC,CAAC,QAAQ,IAAI,CACX,CAAC,IAAM,EAAE,QAAQ,KAAK,EAAE,EAAE;4EAIhC,6CAA6C;4EAC7C,MAAM,eACJ,YAAY,MAAM,GAAG,IACjB,UAAU,MAAM,IAAI,IACpB;4EAEN,qBACE,6LAAC,6HAAA,CAAA,YAAS;gFAER,SAAS;gFACT,WAAU;0FAET,MAAM,UAAU,IAAI;+EAJhB,MAAM,EAAE;;;;;wEAOnB;oEACF;gEACF,CAAC;+DAzEY,CAAC,WAAW,EAAE,OAAO;;;;;wDA4ExC;wDAEA,OAAO;oDACT,CAAC;;;;;;8DAEH,6LAAC,6HAAA,CAAA,YAAS;8DACP,KAAK,MAAM,GAAG,IACb,KAAK,GAAG,CAAC,CAAC,KAAK,yBACb,6LAAC,6HAAA,CAAA,WAAQ;sEAEN,QACE,MAAM,CAAC,CAAC;gEACP,oDAAoD;gEACpD,mCAAmC;gEACnC,MAAM,cAAc,QAAQ,IAAI,CAC9B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;gEAE9B,OACE,AAAC,IAAI,KAAK,KAAK,KAAK,CAAC,eACrB,IAAI,KAAK,GAAG;4DAEhB,GACC,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,YAAS;oEAER,WAAU;8EAGT,CAAC;wEACA,MAAM,WAAW,gBAAgB,IAAI,EAAE;wEACvC,OAAO,yBACL,6LAAC,6HAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,aAAY;4EACZ,WAAU;4EACV,OAAO,aAAa,UAAU,IAAI,EAAE;4EACpC,UAAU,CAAC,IACT,sBACE,UACA,IAAI,EAAE,EACN,EAAE,MAAM,CAAC,KAAK;;;;;iGAKpB,6LAAC;4EAAI,WAAU;sFAA6D;;;;;;oEAIhF,CAAC;mEAzBI,IAAI,EAAE;;;;;2DAhBJ;;;;oEA+CjB,wEAAwE;kEACxE,6LAAC,6HAAA,CAAA,WAAQ;kEAEN,QACE,MAAM,CAAC,CAAC;4DACP,oDAAoD;4DACpD,mCAAmC;4DACnC,MAAM,cAAc,QAAQ,IAAI,CAC9B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;4DAE9B,OACE,AAAC,IAAI,KAAK,KAAK,KAAK,CAAC,eACrB,IAAI,KAAK,GAAG;wDAEhB,GACC,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,YAAS;gEAAc,WAAU;0EAChC,cAAA,6LAAC;oEAAI,WAAU;8EAA6D;;;;;;+DAD9D,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWpC,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;qCASlD,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAmB;;;;;;0CAChC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;;4CAAG;4CACc;0DAChB,6LAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAqB;;;;;;;kDAGrD,6LAAC;;4CAAG;0DACU,6LAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAqB;4CAAI;;;;;;;kDAGrE,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAMR,6LAAC;;0CACC,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAc;;;;;;0CAC7B,6LAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,QAAQ;;;;;;;;;;;;kCAIZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;4CACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oDAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0CACA,OAAO,KAAK,KAAK,IACb,eACA;oDAEN,OAAO;wDAAE,YAAY,GAAG,OAAO,KAAK,GAAG,GAAG,EAAE,CAAC;oDAAC;;sEAE9C,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,KAAK,GAAG,mBACd,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;;;;;;;;;;8EAGnB,6LAAC,6HAAA,CAAA,QAAK;oEACJ,OAAO,OAAO,UAAU;oEACxB,UAAU,CAAC,IACT,uBAAuB,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oEAElD,aAAa,GACX,OAAO,KAAK,KAAK,IAAI,WAAW,QACjC,OAAO,CAAC;oEACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,UACA,OAAO,KAAK,KAAK,IACb,6BACA;;;;;;gEAGP,OAAO,KAAK,KAAK,mBAChB,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;gEAIpD,OAAO,KAAK,GAAG,mBACd,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAKvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,qBAAqB,OAAO,EAAE;oEAC7C,OACE,OAAO,KAAK,GAAG,IACX,iDACA,QAAQ,MAAM,CACZ,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EACnC,MAAM,IAAI,IACZ,oCACA;oEAEN,UACE,OAAO,KAAK,GAAG,KAAK,4CAA4C;oEAChE,QAAQ,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EAC/C,MAAM,IAAI,EAAE,oCAAoC;;8EAGrD,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEACH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,WACA,CAAC,OAAO,KAAK,GAAG,KACd,QAAQ,MAAM,CACZ,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EACnC,MAAM,IAAI,CAAC,KACb;;;;;;;;;;;8EAKR,6LAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,mBAAmB,OAAO,EAAE;oEAC3C,UAAU,QAAQ,MAAM,IAAI;oEAC5B,OAAM;8EAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;;mDAlFhB,OAAO,EAAE;;;;;0DAuFlB,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;4CACN,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC,6HAAA,CAAA,QAAK;4DACJ,OAAO,IAAI,QAAQ;4DACnB,UAAU,CAAC,IACT,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK;4DAE3C,aAAa,CAAC,IAAI,EAAE,QAAQ,GAAG;;;;;;sEAEjC,6LAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,gBAAgB;4DAC/B,UAAU;sEAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;mDAfX;;;;;0DAmBZ,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAMtC,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;8DACJ,6LAAC,6HAAA,CAAA,cAAW;8DAET,CAAC;wDACA,MAAM,WAAW,KAAK,GAAG,IACpB,QAAQ,GAAG,CAAC,CAAC,MAAQ,IAAI,KAAK,GACjC;wDAGF,oCAAoC;wDACpC,MAAM,aAAa,EAAE;wDAErB,gEAAgE;wDAChE,WAAW,IAAI,eACb,6LAAC,6HAAA,CAAA,WAAQ;sEAEN,QACE,MAAM,CAAC,CAAC,MAAQ,IAAI,KAAK,KAAK,GAC9B,GAAG,CAAC,CAAC;gEACJ,sCAAsC;gEACtC,MAAM,oBAAoB,CACxB;oEAEA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,IAAM,EAAE,QAAQ,KAAK;oEAExB,IAAI,iBAAiB;2EAAI;qEAAe;oEAExC,eAAe,OAAO,CAAC,CAAC;wEACtB,iBAAiB;+EACZ;+EACA,kBAAkB,MAAM,EAAE;yEAC9B;oEACH;oEAEA,OAAO;gEACT;gEAEA,MAAM,cAAc,kBAClB,UAAU,EAAE;gEAEd,MAAM,YAAY,YAAY,MAAM,CAClC,CAAC,IACC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,EAAE,EAAE;gEAG5C,6CAA6C;gEAC7C,MAAM,eACJ,YAAY,MAAM,GAAG,IACjB,UAAU,MAAM,IAAI,IACpB;gEAEN,qBACE,6LAAC,6HAAA,CAAA,YAAS;oEAER,SAAS;oEACT,WAAU;8EAET,UAAU,UAAU,IAAI;mEAJpB,UAAU,EAAE;;;;;4DAOvB;2DA/CU;;;;;wDAmDhB,+CAA+C;wDAC/C,IAAK,IAAI,QAAQ,GAAG,SAAS,UAAU,QAAS;4DAC9C,WAAW,IAAI,eACb,6LAAC,6HAAA,CAAA,WAAQ;0EAEN,CAAC;oEACA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,IAAI,KAAK,KAAK,QAAQ;oEAGjC,OAAO,eAAe,GAAG,CAAC,CAAC;wEACzB,qCAAqC;wEACrC,MAAM,WAAW,QAAQ,MAAM,CAC7B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;wEAG9B,6DAA6D;wEAC7D,IAAI,SAAS,MAAM,KAAK,GAAG;4EACzB,qBACE,6LAAC,6HAAA,CAAA,YAAS;gFAER,WAAU;+EADL,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;;;;;wEAM5B;wEAEA,6CAA6C;wEAC7C,OAAO,SAAS,GAAG,CAAC,CAAC;4EACnB,qDAAqD;4EACrD,MAAM,oBAAoB,CACxB;gFAEA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,IAAM,EAAE,QAAQ,KAAK;gFAExB,IAAI,iBAAiB;uFAAI;iFAAe;gFAExC,eAAe,OAAO,CAAC,CAAC;oFACtB,iBAAiB;2FACZ;2FACA,kBAAkB,MAAM,EAAE;qFAC9B;gFACH;gFAEA,OAAO;4EACT;4EAEA,MAAM,cAAc,kBAClB,MAAM,EAAE;4EAEV,MAAM,YAAY,YAAY,MAAM,CAClC,CAAC,IACC,CAAC,QAAQ,IAAI,CACX,CAAC,IAAM,EAAE,QAAQ,KAAK,EAAE,EAAE;4EAIhC,6CAA6C;4EAC7C,MAAM,eACJ,YAAY,MAAM,GAAG,IACjB,UAAU,MAAM,IAAI,IACpB;4EAEN,qBACE,6LAAC,6HAAA,CAAA,YAAS;gFAER,SAAS;gFACT,WAAU;0FAET,MAAM,UAAU,IAAI;+EAJhB,MAAM,EAAE;;;;;wEAOnB;oEACF;gEACF,CAAC;+DAzEY,CAAC,WAAW,EAAE,OAAO;;;;;wDA4ExC;wDAEA,OAAO;oDACT,CAAC;;;;;;8DAEH,6LAAC,6HAAA,CAAA,YAAS;8DACP,KAAK,MAAM,GAAG,IACb,KAAK,GAAG,CAAC,CAAC,KAAK,yBACb,6LAAC,6HAAA,CAAA,WAAQ;sEAEN,QACE,MAAM,CAAC,CAAC;gEACP,oDAAoD;gEACpD,mCAAmC;gEACnC,MAAM,cAAc,QAAQ,IAAI,CAC9B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;gEAE9B,OACE,AAAC,IAAI,KAAK,KAAK,KAAK,CAAC,eACrB,IAAI,KAAK,GAAG;4DAEhB,GACC,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,YAAS;oEAER,WAAU;8EAGT,CAAC;wEACA,MAAM,WAAW,gBAAgB,IAAI,EAAE;wEACvC,OAAO,yBACL,6LAAC,6HAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,aAAY;4EACZ,WAAU;4EACV,OAAO,aAAa,UAAU,IAAI,EAAE;4EACpC,UAAU,CAAC,IACT,sBACE,UACA,IAAI,EAAE,EACN,EAAE,MAAM,CAAC,KAAK;;;;;iGAKpB,6LAAC;4EAAI,WAAU;sFAA6D;;;;;;oEAIhF,CAAC;mEAzBI,IAAI,EAAE;;;;;2DAhBJ;;;;oEA+CjB,wEAAwE;kEACxE,6LAAC,6HAAA,CAAA,WAAQ;kEAEN,QACE,MAAM,CAAC,CAAC;4DACP,oDAAoD;4DACpD,mCAAmC;4DACnC,MAAM,cAAc,QAAQ,IAAI,CAC9B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;4DAE9B,OACE,AAAC,IAAI,KAAK,KAAK,KAAK,CAAC,eACrB,IAAI,KAAK,GAAG;wDAEhB,GACC,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,YAAS;gEAAc,WAAU;0EAChC,cAAA,6LAAC;oEAAI,WAAU;8EAA6D;;;;;;+DAD9D,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWpC,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,eACG,cACA,cAAc,mBAAmB,KACjC,WACA;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA/xDgB;;QAOI,wHAAA,CAAA,WAAQ;;;KAPZ", "debugId": null}}, {"offset": {"line": 5555, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/modals/AddQuestionModal.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { FieldVal<PERSON>, FormProvider, useForm } from \"react-hook-form\";\r\nimport { Select } from \"../general/Select\";\r\nimport { labelToKey } from \"@/lib/labelToKey\";\r\nimport { InputTypeMap } from \"@/constants/inputType\";\r\nimport { Switch } from \"../ui\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { addQuestion } from \"@/lib/api/form-builder\";\r\nimport { DynamicOptions } from \"../form-builder/DynamicOptions\";\r\nimport { z } from \"zod\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { needsOptions } from \"@/lib/needsOptions\";\r\nimport { QuestionSchema } from \"@/types/formBuilder\";\r\nimport { ContextType } from \"@/types\";\r\nimport { LoadingOverlay } from \"../general/LoadingOverlay\";\r\nimport { TableQuestionBuilder } from \"../form-builder/TableQuestionBuilder\";\r\n\r\n// Confirmation dialog component\r\nconst ConfirmationDialog = ({\r\n  isOpen,\r\n  onConfirm,\r\n  onCancel,\r\n}: {\r\n  isOpen: boolean;\r\n  onConfirm: () => void;\r\n  onCancel: () => void;\r\n}) => {\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white rounded-lg p-6 max-w-md w-full\">\r\n        <h2 className=\"text-lg text-neutral-700 font-semibold mb-4\">Unsaved Changes</h2>\r\n        <p className=\"mb-6 text-neutral-700\">\r\n          You have unsaved changes. Are you sure you want to close this form?\r\n        </p>\r\n        <div className=\"flex justify-end space-x-3\">\r\n          <button\r\n            onClick={onCancel}\r\n            className=\"btn-outline\"\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button\r\n            onClick={onConfirm}\r\n            className=\"btn-danger\"\r\n          >\r\n            Discard Changes\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst AddQuestionModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  contextType,\r\n  contextId,\r\n  position,\r\n}: {\r\n  showModal: boolean;\r\n  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  position?: number;\r\n}) => {\r\n  const methods = useForm<z.infer<typeof QuestionSchema>>({\r\n    resolver: zodResolver(QuestionSchema),\r\n    defaultValues: {\r\n      label: \"\",\r\n      inputType: \"\",\r\n      hint: \"\",\r\n      placeholder: \"\",\r\n      questionOptions: [],\r\n    },\r\n  });\r\n\r\n  const {\r\n    register,\r\n    formState: { errors, isSubmitted, isDirty },\r\n    setValue,\r\n    handleSubmit,\r\n    reset,\r\n    watch,\r\n  } = methods;\r\n\r\n  // State for confirmation dialog\r\n  const [showConfirmation, setShowConfirmation] = useState(false);\r\n\r\n  const queryClient = useQueryClient();\r\n  const queryKey =\r\n    contextType === \"project\"\r\n      ? [\"questions\", contextId]\r\n      : contextType === \"template\"\r\n      ? [\"templateQuestions\", contextId]\r\n      : [\"questionBlockQuestions\", contextId];\r\n  const questionMutation = useMutation({\r\n    mutationFn: addQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey });\r\n      reset();\r\n      setSelectedInputType(\"\");\r\n      setShowModal(false);\r\n    },\r\n    onError: () => {},\r\n  });\r\n\r\n  const [isRequired, setIsRequired] = useState(false);\r\n  const [selectedInputType, setSelectedInputType] = useState<string>(\"\");\r\n\r\n  useEffect(() => {\r\n    register(\"inputType\", { required: \"Please select an input type\" });\r\n  }, [register]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"inputType\", selectedInputType, { shouldValidate: isSubmitted });\r\n  }, [selectedInputType]);\r\n\r\n  useEffect(() => {\r\n    if (showModal) {\r\n      setIsLoading(false);\r\n    }\r\n  }, [showModal]);\r\n\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Function to check if there are unsaved changes\r\n  const hasUnsavedChanges = () => {\r\n    // Check if the form is dirty (has been modified)\r\n    const questionOptions = watch(\"questionOptions\");\r\n\r\n    return (\r\n      isDirty ||\r\n      // Check if any input has been filled\r\n      !!watch(\"label\") ||\r\n      !!watch(\"hint\") ||\r\n      !!watch(\"placeholder\") ||\r\n      !!selectedInputType ||\r\n      (questionOptions &&\r\n        Array.isArray(questionOptions) &&\r\n        questionOptions.length > 0)\r\n    );\r\n  };\r\n\r\n  // Function to handle modal close with confirmation if needed\r\n  const handleCloseWithConfirmation = () => {\r\n    if (hasUnsavedChanges()) {\r\n      setShowConfirmation(true);\r\n    } else {\r\n      // No changes, close directly\r\n      handleClose();\r\n    }\r\n  };\r\n\r\n  // Function to handle actual closing\r\n  const handleClose = () => {\r\n    reset();\r\n    setSelectedInputType(\"\");\r\n    setIsLoading(false);\r\n    setShowConfirmation(false);\r\n    setShowModal(false);\r\n  };\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    if (isLoading) return; // Prevent double submission\r\n\r\n    // If it's a table question, let the TableQuestionBuilder handle it\r\n    if (selectedInputType === \"table\") {\r\n      // The TableQuestionBuilder component will handle its own submission\r\n      // through its handleSubmit function\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    const dataToSend = {\r\n      label: data.label,\r\n      isRequired,\r\n      hint: data.hint,\r\n      placeholder: data.placeholder,\r\n      inputType: selectedInputType,\r\n      questionOptions: data.questionOptions,\r\n    };\r\n\r\n    questionMutation.mutate({ contextType, contextId, dataToSend, position });\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Modal\r\n        isOpen={showModal}\r\n        onClose={handleCloseWithConfirmation}\r\n        className=\"w-11/12 tablet:w-4/5 desktop:w-4/5 bg-neutral-100 rounded-lg p-6\"\r\n      >\r\n        <h1 className=\"heading-text capitalize mb-4\">Add question</h1>\r\n\r\n        {questionMutation.isPending && <LoadingOverlay />}\r\n        <FormProvider {...methods}>\r\n          <form\r\n            className=\"space-y-4 max-h-[500px] w-full  overflow-y-auto p-4 \"\r\n            onSubmit={handleSubmit(onSubmit)}\r\n          >\r\n            <div className=\"label-input-group group \">\r\n              <input\r\n                {...register(\"label\", {\r\n                  required: \"Question name is required\",\r\n                })}\r\n                className=\"input-field\"\r\n                placeholder=\"Enter the question\"\r\n              />\r\n              {errors.label && (\r\n                <p className=\"text-sm text-red-500\">{`${errors.label.message}`}</p>\r\n              )}\r\n            </div>\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              <div className=\"label-input-group group\">\r\n                <label htmlFor=\"question-type\" className=\"label-text\">\r\n                  Input Type\r\n                </label>\r\n                <div className=\"mt-1\">\r\n                  <Select\r\n                    id=\"question-type\"\r\n                    options={Object.values(InputTypeMap)}\r\n                    value={\r\n                      selectedInputType && InputTypeMap[selectedInputType]\r\n                        ? InputTypeMap[selectedInputType]\r\n                        : \"Select an option\"\r\n                    }\r\n                    onChange={(label) => {\r\n                      const selectedKey = labelToKey(label, InputTypeMap);\r\n                      setSelectedInputType(selectedKey ?? \"\");\r\n                    }}\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-end\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <Switch\r\n                    id=\"required\"\r\n                    checked={isRequired}\r\n                    onCheckedChange={() => setIsRequired((prev) => !prev)}\r\n                    className=\"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500\"\r\n                  />\r\n                  <label htmlFor=\"required\" className=\"label-text\">\r\n                    Required\r\n                  </label>\r\n                </div>\r\n              </div>\r\n              {errors.inputType && (\r\n                <p className=\"text-sm text-red-500\">{`${errors.inputType.message}`}</p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"hint\" className=\"label-text\">\r\n                Help text\r\n              </label>\r\n              <textarea\r\n                {...register(\"hint\")}\r\n                id=\"hint\"\r\n                placeholder=\"Add a hint or instructions for this question\"\r\n                className=\"input-field resize-none\"\r\n              />\r\n            </div>\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"placeholder\" className=\"label-text\">\r\n                Placeholder text\r\n              </label>\r\n              <input\r\n                {...register(\"placeholder\")}\r\n                id=\"placeholder\"\r\n                placeholder=\"Add a placeholder for this question\"\r\n                className=\"input-field\"\r\n              />\r\n            </div>\r\n\r\n            {needsOptions(selectedInputType) && <DynamicOptions />}\r\n\r\n            {selectedInputType === \"table\" && (\r\n              <div className=\"mt-4\">\r\n                <TableQuestionBuilder\r\n                  projectId={contextId}\r\n                  isInModal={true}\r\n                  onTableCreated={(tableId) => {\r\n                    // Close the modal after table creation\r\n                    reset();\r\n                    setSelectedInputType(\"\");\r\n                    setShowModal(false);\r\n\r\n                    // Refresh the questions list if a table was created\r\n                    if (tableId !== -1) {\r\n                      queryClient.invalidateQueries({ queryKey });\r\n                    }\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"flex items-center justify-end space-x-4\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleCloseWithConfirmation}\r\n                className=\"btn-outline\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn-primary flex items-center justify-center gap-2\"\r\n                onClick={(e) => {\r\n                  if (selectedInputType === \"table\") {\r\n                    e.preventDefault();\r\n                    // Find the TableQuestionBuilder component and call its handleSubmit function\r\n                    const tableBuilder = document.querySelector(\r\n                      \".table-question-builder\"\r\n                    );\r\n                    if (tableBuilder) {\r\n                      // Trigger a custom event that the TableQuestionBuilder will listen for\r\n                      tableBuilder.dispatchEvent(\r\n                        new CustomEvent(\"submitTable\")\r\n                      );\r\n                    }\r\n                  }\r\n                }}\r\n                disabled={isLoading}\r\n              >\r\n                {isLoading ? (\r\n                  <>\r\n                    <span className=\"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full\" />\r\n                    Saving...\r\n                  </>\r\n                ) : (\r\n                  \"Save\"\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </FormProvider>\r\n      </Modal>\r\n\r\n      {/* Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        isOpen={showConfirmation}\r\n        onConfirm={handleClose}\r\n        onCancel={() => {\r\n          setShowConfirmation(false);\r\n        }}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport { AddQuestionModal };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;;AAEA,gCAAgC;AAChC,MAAM,qBAAqB,CAAC,EAC1B,MAAM,EACN,SAAS,EACT,QAAQ,EAKT;IACC,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA8C;;;;;;8BAC5D,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;8BAGrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAnCM;AAqCN,MAAM,mBAAmB,CAAC,EACxB,SAAS,EACT,YAAY,EACZ,WAAW,EACX,SAAS,EACT,QAAQ,EAOT;;IACC,MAAM,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkC;QACtD,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,uHAAA,CAAA,iBAAc;QACpC,eAAe;YACb,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;YACb,iBAAiB,EAAE;QACrB;IACF;IAEA,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,EAC3C,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,KAAK,EACN,GAAG;IAEJ,gCAAgC;IAChC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,WACJ,gBAAgB,YACZ;QAAC;QAAa;KAAU,GACxB,gBAAgB,aAChB;QAAC;QAAqB;KAAU,GAChC;QAAC;QAA0B;KAAU;IAC3C,MAAM,mBAAmB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACnC,YAAY,gIAAA,CAAA,cAAW;QACvB,SAAS;8DAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE;gBAAS;gBACzC;gBACA,qBAAqB;gBACrB,aAAa;YACf;;QACA,OAAO;8DAAE,KAAO;;IAClB;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,SAAS,aAAa;gBAAE,UAAU;YAA8B;QAClE;qCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,SAAS,aAAa,mBAAmB;gBAAE,gBAAgB;YAAY;QACzE;qCAAG;QAAC;KAAkB;IAEtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW;gBACb,aAAa;YACf;QACF;qCAAG;QAAC;KAAU;IAEd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,iDAAiD;IACjD,MAAM,oBAAoB;QACxB,iDAAiD;QACjD,MAAM,kBAAkB,MAAM;QAE9B,OACE,WACA,qCAAqC;QACrC,CAAC,CAAC,MAAM,YACR,CAAC,CAAC,MAAM,WACR,CAAC,CAAC,MAAM,kBACR,CAAC,CAAC,qBACD,mBACC,MAAM,OAAO,CAAC,oBACd,gBAAgB,MAAM,GAAG;IAE/B;IAEA,6DAA6D;IAC7D,MAAM,8BAA8B;QAClC,IAAI,qBAAqB;YACvB,oBAAoB;QACtB,OAAO;YACL,6BAA6B;YAC7B;QACF;IACF;IAEA,oCAAoC;IACpC,MAAM,cAAc;QAClB;QACA,qBAAqB;QACrB,aAAa;QACb,oBAAoB;QACpB,aAAa;IACf;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,WAAW,QAAQ,4BAA4B;QAEnD,mEAAmE;QACnE,IAAI,sBAAsB,SAAS;YACjC,oEAAoE;YACpE,oCAAoC;YACpC;QACF;QAEA,aAAa;QAEb,MAAM,aAAa;YACjB,OAAO,KAAK,KAAK;YACjB;YACA,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,WAAW;YACX,iBAAiB,KAAK,eAAe;QACvC;QAEA,iBAAiB,MAAM,CAAC;YAAE;YAAa;YAAW;YAAY;QAAS;IACzE;IAEA,qBACE;;0BACE,6LAAC,iIAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS;gBACT,WAAU;;kCAEV,6LAAC;wBAAG,WAAU;kCAA+B;;;;;;oBAE5C,iBAAiB,SAAS,kBAAI,6LAAC,2IAAA,CAAA,iBAAc;;;;;kCAC9C,6LAAC,iKAAA,CAAA,eAAY;wBAAE,GAAG,OAAO;kCACvB,cAAA,6LAAC;4BACC,WAAU;4BACV,UAAU,aAAa;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACE,GAAG,SAAS,SAAS;gDACpB,UAAU;4CACZ,EAAE;4CACF,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAAwB,GAAG,OAAO,KAAK,CAAC,OAAO,EAAE;;;;;;;;;;;;8CAGlE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAgB,WAAU;8DAAa;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mIAAA,CAAA,SAAM;wDACL,IAAG;wDACH,SAAS,OAAO,MAAM,CAAC,yHAAA,CAAA,eAAY;wDACnC,OACE,qBAAqB,yHAAA,CAAA,eAAY,CAAC,kBAAkB,GAChD,yHAAA,CAAA,eAAY,CAAC,kBAAkB,GAC/B;wDAEN,UAAU,CAAC;4DACT,MAAM,cAAc,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,yHAAA,CAAA,eAAY;4DAClD,qBAAqB,eAAe;wDACtC;;;;;;;;;;;;;;;;;sDAIN,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8HAAA,CAAA,SAAM;wDACL,IAAG;wDACH,SAAS;wDACT,iBAAiB,IAAM,cAAc,CAAC,OAAS,CAAC;wDAChD,WAAU;;;;;;kEAEZ,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAa;;;;;;;;;;;;;;;;;wCAKpD,OAAO,SAAS,kBACf,6LAAC;4CAAE,WAAU;sDAAwB,GAAG,OAAO,SAAS,CAAC,OAAO,EAAE;;;;;;;;;;;;8CAItE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAAa;;;;;;sDAG7C,6LAAC;4CACE,GAAG,SAAS,OAAO;4CACpB,IAAG;4CACH,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAa;;;;;;sDAGpD,6LAAC;4CACE,GAAG,SAAS,cAAc;4CAC3B,IAAG;4CACH,aAAY;4CACZ,WAAU;;;;;;;;;;;;gCAIb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,oCAAsB,6LAAC,mJAAA,CAAA,iBAAc;;;;;gCAElD,sBAAsB,yBACrB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yJAAA,CAAA,uBAAoB;wCACnB,WAAW;wCACX,WAAW;wCACX,gBAAgB,CAAC;4CACf,uCAAuC;4CACvC;4CACA,qBAAqB;4CACrB,aAAa;4CAEb,oDAAoD;4CACpD,IAAI,YAAY,CAAC,GAAG;gDAClB,YAAY,iBAAiB,CAAC;oDAAE;gDAAS;4CAC3C;wCACF;;;;;;;;;;;8CAKN,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,CAAC;gDACR,IAAI,sBAAsB,SAAS;oDACjC,EAAE,cAAc;oDAChB,6EAA6E;oDAC7E,MAAM,eAAe,SAAS,aAAa,CACzC;oDAEF,IAAI,cAAc;wDAChB,uEAAuE;wDACvE,aAAa,aAAa,CACxB,IAAI,YAAY;oDAEpB;gDACF;4CACF;4CACA,UAAU;sDAET,0BACC;;kEACE,6LAAC;wDAAK,WAAU;;;;;;oDAAiF;;+DAInG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASZ,6LAAC;gBACC,QAAQ;gBACR,WAAW;gBACX,UAAU;oBACR,oBAAoB;gBACtB;;;;;;;;AAIR;GAzSM;;QAaY,iKAAA,CAAA,UAAO;QAuBH,yLAAA,CAAA,iBAAc;QAOT,iLAAA,CAAA,cAAW;;;MA3ChC", "debugId": null}}, {"offset": {"line": 6112, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/modals/EditQuestionModal.tsx"], "sourcesContent": ["import { Question, QuestionSchema } from \"@/types/formBuilder\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { FieldValues, FormProvider, useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { InputTypeMap } from \"@/constants/inputType\";\r\nimport { needsOptions } from \"@/lib/needsOptions\";\r\nimport { DynamicOptions } from \"../form-builder/DynamicOptions\";\r\nimport { Switch } from \"../ui\";\r\nimport { ContextType, TemplateQuestion } from \"@/types\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { updateQuestion } from \"@/lib/api/form-builder\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { LoadingOverlay } from \"../general/LoadingOverlay\";\r\n\r\nconst EditQuestionModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  contextType,\r\n  question,\r\n  contextId,\r\n}: {\r\n  showModal: boolean;\r\n  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;\r\n  contextType: ContextType;\r\n  question: Question | TemplateQuestion;\r\n  contextId: number;\r\n}) => {\r\n  const methods = useForm<z.infer<typeof QuestionSchema>>({});\r\n\r\n  const {\r\n    register,\r\n    formState: { errors, isSubmitted },\r\n    setValue,\r\n    handleSubmit,\r\n    reset,\r\n  } = methods;\r\n\r\n  useEffect(() => {\r\n    const result = QuestionSchema.safeParse(question);\r\n    if (result.success) {\r\n      reset(result.data);\r\n      setSelectedInputType(result.data.inputType || \"\");\r\n    }\r\n  }, [question]);\r\n\r\n  const [isRequired, setIsRequired] = useState(false);\r\n  const [selectedInputType, setSelectedInputType] = useState<string>(\"\");\r\n\r\n  useEffect(() => {\r\n    register(\"inputType\", { required: \"Please select an input type\" });\r\n  }, [register]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"inputType\", selectedInputType, { shouldValidate: isSubmitted });\r\n  }, [selectedInputType]);\r\n\r\n  const queryClient = useQueryClient();\r\n  const dispatch = useDispatch();\r\n\r\n  const queryKey =\r\n    contextType === \"project\"\r\n      ? [\"questions\"]\r\n      : contextType === \"template\"\r\n      ? [\"templateQuestions\"]\r\n      : [\"questionBlockQuestions\"];\r\n\r\n  const updateQuestionMutation = useMutation({\r\n    mutationFn: updateQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey,\r\n        exact: false,\r\n      });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Successfully updated question\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setShowModal(false);\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to update question\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    const dataToSend = {\r\n      label: data.label,\r\n      isRequired,\r\n      hint: data.hint,\r\n      placeholder: data.placeholder,\r\n      questionOptions: data.questionOptions,\r\n      // Preserve the original position if it exists (for Question type)\r\n      ...(('position' in question) && { position: question.position }),\r\n    };\r\n    updateQuestionMutation.mutate({\r\n      id: question.id,\r\n      contextType,\r\n      dataToSend,\r\n      contextId,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={() => {\r\n        setShowModal(false);\r\n      }}\r\n      className=\"w-11/12 tablet:w-4/5 desktop:w-3/5\"\r\n    >\r\n      <h1 className=\"heading-text capitalize mb-4\">Edit question</h1>\r\n\r\n      {updateQuestionMutation.isPending && <LoadingOverlay />}\r\n      <FormProvider {...methods}>\r\n        <form\r\n          className=\"space-y-4 max-h-[500px] overflow-y-auto p-4\"\r\n          onSubmit={handleSubmit(onSubmit)}\r\n        >\r\n          <div className=\"label-input-group group\">\r\n            <input\r\n              {...register(\"label\", { required: \"Question name is required\" })}\r\n              className=\"input-field\"\r\n              placeholder=\"Enter the question\"\r\n            />\r\n            {errors.label && (\r\n              <p className=\"text-sm text-red-500\">{`${errors.label.message}`}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"question-type\" className=\"label-text\">\r\n                Input Type\r\n              </label>\r\n              <input\r\n                id=\"question-type\"\r\n                className=\"input-field bg-gray-100 \"\r\n                value={\r\n                  selectedInputType && InputTypeMap[selectedInputType]\r\n                    ? InputTypeMap[selectedInputType]\r\n                    : \"N/A\"\r\n                }\r\n                disabled\r\n              />\r\n            </div>\r\n            <div className=\"flex items-end\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Switch\r\n                  id=\"required\"\r\n                  checked={isRequired}\r\n                  onCheckedChange={() => setIsRequired((prev) => !prev)}\r\n                  className=\"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500\"\r\n                />\r\n                <label htmlFor=\"required\" className=\"label-text\">\r\n                  Required\r\n                </label>\r\n              </div>\r\n            </div>\r\n            {errors.inputType && (\r\n              <p className=\"text-sm text-red-500\">{`${errors.inputType.message}`}</p>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"hint\" className=\"label-text\">\r\n              Help text\r\n            </label>\r\n            <textarea\r\n              {...register(\"hint\")}\r\n              id=\"hint\"\r\n              placeholder=\"Add a hint or instructions for this question\"\r\n              className=\"input-field resize-none\"\r\n            />\r\n          </div>\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"placeholder\" className=\"label-text\">\r\n              Placeholder text\r\n            </label>\r\n            <input\r\n              {...register(\"placeholder\")}\r\n              id=\"placeholder\"\r\n              placeholder=\"Add a placeholder for this question\"\r\n              className=\"input-field\"\r\n            />\r\n          </div>\r\n\r\n          {needsOptions(selectedInputType) && (\r\n            <DynamicOptions\r\n              contextType={contextType}\r\n              contextId={contextId}\r\n              currentQuestionId={question.id}\r\n              inputType={selectedInputType}\r\n            />\r\n          )}\r\n          <div className=\"flex items-center justify-end space-x-4\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                setShowModal(false);\r\n              }}\r\n              className=\"btn-outline\"\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button onClick={handleSubmit(onSubmit)} className=\"btn-primary\">\r\n              Save Edit\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </FormProvider>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { EditQuestionModal };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAEA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,SAAS,EAOV;;IACC,MAAM,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkC,CAAC;IAEzD,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,EAClC,QAAQ,EACR,YAAY,EACZ,KAAK,EACN,GAAG;IAEJ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,SAAS,uHAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,IAAI;gBACjB,qBAAqB,OAAO,IAAI,CAAC,SAAS,IAAI;YAChD;QACF;sCAAG;QAAC;KAAS;IAEb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,SAAS,aAAa;gBAAE,UAAU;YAA8B;QAClE;sCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,SAAS,aAAa,mBAAmB;gBAAE,gBAAgB;YAAY;QACzE;sCAAG;QAAC;KAAkB;IAEtB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WACJ,gBAAgB,YACZ;QAAC;KAAY,GACb,gBAAgB,aAChB;QAAC;KAAoB,GACrB;QAAC;KAAyB;IAEhC,MAAM,yBAAyB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACzC,YAAY,gIAAA,CAAA,iBAAc;QAC1B,SAAS;qEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAC5B;oBACA,OAAO;gBACT;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,aAAa;YACf;;QACA,OAAO;qEAAE;gBACP,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,aAAa;YACjB,OAAO,KAAK,KAAK;YACjB;YACA,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,iBAAiB,KAAK,eAAe;YACrC,kEAAkE;YAClE,GAAI,AAAC,cAAc,YAAa;gBAAE,UAAU,SAAS,QAAQ;YAAC,CAAC;QACjE;QACA,uBAAuB,MAAM,CAAC;YAC5B,IAAI,SAAS,EAAE;YACf;YACA;YACA;QACF;IACF;IAEA,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;YACP,aAAa;QACf;QACA,WAAU;;0BAEV,6LAAC;gBAAG,WAAU;0BAA+B;;;;;;YAE5C,uBAAuB,SAAS,kBAAI,6LAAC,2IAAA,CAAA,iBAAc;;;;;0BACpD,6LAAC,iKAAA,CAAA,eAAY;gBAAE,GAAG,OAAO;0BACvB,cAAA,6LAAC;oBACC,WAAU;oBACV,UAAU,aAAa;;sCAEvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACE,GAAG,SAAS,SAAS;wCAAE,UAAU;oCAA4B,EAAE;oCAChE,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,KAAK,kBACX,6LAAC;oCAAE,WAAU;8CAAwB,GAAG,OAAO,KAAK,CAAC,OAAO,EAAE;;;;;;;;;;;;sCAGlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAAa;;;;;;sDAGtD,6LAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OACE,qBAAqB,yHAAA,CAAA,eAAY,CAAC,kBAAkB,GAChD,yHAAA,CAAA,eAAY,CAAC,kBAAkB,GAC/B;4CAEN,QAAQ;;;;;;;;;;;;8CAGZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS;gDACT,iBAAiB,IAAM,cAAc,CAAC,OAAS,CAAC;gDAChD,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAAa;;;;;;;;;;;;;;;;;gCAKpD,OAAO,SAAS,kBACf,6LAAC;oCAAE,WAAU;8CAAwB,GAAG,OAAO,SAAS,CAAC,OAAO,EAAE;;;;;;;;;;;;sCAItE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAAa;;;;;;8CAG7C,6LAAC;oCACE,GAAG,SAAS,OAAO;oCACpB,IAAG;oCACH,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAGd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAAa;;;;;;8CAGpD,6LAAC;oCACE,GAAG,SAAS,cAAc;oCAC3B,IAAG;oCACH,aAAY;oCACZ,WAAU;;;;;;;;;;;;wBAIb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,oCACZ,6LAAC,mJAAA,CAAA,iBAAc;4BACb,aAAa;4BACb,WAAW;4BACX,mBAAmB,SAAS,EAAE;4BAC9B,WAAW;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;wCACP,aAAa;oCACf;oCACA,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCAAO,SAAS,aAAa;oCAAW,WAAU;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7E;GA5MM;;QAaY,iKAAA,CAAA,UAAO;QA6BH,yLAAA,CAAA,iBAAc;QACjB,4JAAA,CAAA,cAAW;QASG,iLAAA,CAAA,cAAW;;;KApDtC", "debugId": null}}, {"offset": {"line": 6502, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/modals/EditTableQuestionModal.tsx"], "sourcesContent": ["import { Question } from \"@/types/formBuilder\";\r\nimport React, { useEffect } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { ContextType } from \"@/types\";\r\nimport { useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { LoadingOverlay } from \"../general/LoadingOverlay\";\r\nimport { TableQuestionBuilder } from \"../form-builder/TableQuestionBuilder\";\r\nimport { fetchTableStructure, TableQuestion } from \"@/lib/api/table\";\r\nimport axios from \"@/lib/axios\";\r\n\r\nconst EditTableQuestionModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  contextType,\r\n  question,\r\n  contextId,\r\n}: {\r\n  showModal: boolean;\r\n  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;\r\n  contextType: ContextType;\r\n  question: Question;\r\n  contextId: number;\r\n}) => {\r\n  const queryClient = useQueryClient();\r\n  const dispatch = useDispatch();\r\n\r\n  // Fetch the table structure (columns and rows) and cell values\r\n  const {\r\n    data: rawTableResponse,\r\n    isLoading,\r\n    error,\r\n  } = useQuery<{ question: TableQuestion; cellValues?: Record<string, any> }>({\r\n    queryKey: [\"tableQuestion\", question.id],\r\n    queryFn: async () => {\r\n      try {\r\n        console.log(\"Fetching table structure for question ID:\", question.id);\r\n\r\n        // Use the table-questions endpoint directly to get both structure and cell values\r\n        const response = await axios.get(`/table-questions/${question.id}`);\r\n        console.log(\"Full API response:\", response.data);\r\n\r\n        if (response.data && response.data.success && response.data.data) {\r\n          const { question: tableQuestion, cellValues } = response.data.data;\r\n          console.log(\"Extracted table question:\", tableQuestion);\r\n          console.log(\"Extracted cell values:\", cellValues);\r\n\r\n          return {\r\n            question: tableQuestion,\r\n            cellValues: cellValues || {},\r\n          };\r\n        } else {\r\n          throw new Error(\"Invalid response structure\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching table data:\", err);\r\n        throw err;\r\n      }\r\n    },\r\n    enabled: showModal && question.id > 0 && question.inputType === \"table\",\r\n  });\r\n\r\n  // Process the table data to match the expected format for TableQuestionBuilder\r\n  const tableData = React.useMemo(() => {\r\n    if (!rawTableResponse?.question) return null;\r\n\r\n    const rawTableData = rawTableResponse.question;\r\n    const cellValues = rawTableResponse.cellValues || {};\r\n\r\n    console.log(\"Processing raw table data:\", rawTableData);\r\n    console.log(\"Processing cell values:\", cellValues);\r\n    console.log(\"Cell values keys:\", Object.keys(cellValues));\r\n    console.log(\"Table rows:\", rawTableData.tableRows);\r\n\r\n    // Create a copy of the raw table data and ensure it matches the expected interface\r\n    const processedData = {\r\n      id: rawTableData.id,\r\n      label: rawTableData.label,\r\n      tableColumns: rawTableData.tableColumns.map((col) => ({\r\n        id: col.id,\r\n        columnName: col.columnName,\r\n        parentColumnId: col.parentColumnId,\r\n        childColumns:\r\n          col.childColumns?.map((child) => ({\r\n            id: child.id,\r\n            columnName: child.columnName,\r\n            parentColumnId: child.parentColumnId || col.id, // Ensure parentColumnId is set for child columns\r\n          })) || [],\r\n      })),\r\n      tableRows: rawTableData.tableRows.map((row) => ({\r\n        id: row.id,\r\n        rowsName: row.rowsName,\r\n        // Add default values from cell values if they exist\r\n        defaultValues: Object.entries(cellValues)\r\n          .filter(([key, value]) => {\r\n            const [columnId, rowId] = key.split(\"_\");\r\n            return (\r\n              parseInt(rowId) === row.id &&\r\n              value.value &&\r\n              value.value.trim() !== \"\"\r\n            );\r\n          })\r\n          .map(([key, value]) => {\r\n            const [columnId] = key.split(\"_\");\r\n            return {\r\n              columnId: parseInt(columnId),\r\n              value: value.value || \"\",\r\n              code: value.code || value.value || \"\",\r\n            };\r\n          }),\r\n      })),\r\n      // Also include cell values for the TableQuestionBuilder\r\n      cellValues: cellValues,\r\n    };\r\n\r\n    console.log(\"Processed table data:\", processedData);\r\n    return processedData;\r\n  }, [rawTableResponse]);\r\n\r\n  // Log when the modal is shown or hidden\r\n  useEffect(() => {\r\n    console.log(\"EditTableQuestionModal showModal changed:\", showModal);\r\n    if (showModal) {\r\n      console.log(\"Modal opened for question:\", question);\r\n    }\r\n  }, [showModal, question]);\r\n\r\n  const queryKey =\r\n    contextType === \"project\"\r\n      ? [\"questions\"]\r\n      : contextType === \"template\"\r\n      ? [\"templateQuestions\"]\r\n      : [\"questionBlockQuestions\"];\r\n\r\n  const handleTableUpdated = async (tableId: number) => {\r\n    console.log(\"Table updated with ID:\", tableId);\r\n\r\n    try {\r\n      // Invalidate all relevant queries to ensure fresh data is loaded everywhere\r\n      await queryClient.invalidateQueries({\r\n        queryKey,\r\n        exact: false,\r\n      });\r\n\r\n      await queryClient.invalidateQueries({\r\n        queryKey: [\"tableQuestion\", question.id],\r\n        exact: true,\r\n      });\r\n\r\n      // Also invalidate any cached table structure queries that TableInput might use\r\n      await queryClient.invalidateQueries({\r\n        queryKey: [\"tableStructure\"],\r\n        exact: false,\r\n      });\r\n\r\n      // Invalidate any queries that might be caching table data\r\n      await queryClient.invalidateQueries({\r\n        predicate: (query) => {\r\n          const queryKey = query.queryKey;\r\n          return (\r\n            Array.isArray(queryKey) &&\r\n            (queryKey.includes(\"tableQuestion\") ||\r\n              queryKey.includes(\"tableStructure\") ||\r\n              queryKey.includes(\"formQuestions\") ||\r\n              (queryKey.includes(question.id) &&\r\n                typeof question.id === \"number\"))\r\n          );\r\n        },\r\n      });\r\n\r\n      // Force refetch of the current table data\r\n      await queryClient.refetchQueries({\r\n        queryKey: [\"tableQuestion\", question.id],\r\n      });\r\n\r\n      console.log(\r\n        \"Successfully invalidated and refetched all table-related queries\"\r\n      );\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Successfully updated table question\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      // Add a small delay before closing the modal to ensure state is properly updated\r\n      setTimeout(() => {\r\n        console.log(\"Closing modal after table update\");\r\n        setShowModal(false);\r\n      }, 200); // Increased delay to allow for async operations\r\n    } catch (error) {\r\n      console.error(\"Error updating queries after table update:\", error);\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message:\r\n            \"Table updated but there was an issue refreshing the data. Please refresh the page.\",\r\n          type: \"warning\",\r\n        })\r\n      );\r\n\r\n      // Still close the modal after a delay\r\n      setTimeout(() => {\r\n        setShowModal(false);\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n  if (error) {\r\n    console.error(\"Error fetching table data:\", error);\r\n    dispatch(\r\n      showNotification({\r\n        message: \"Failed to load table data\",\r\n        type: \"error\",\r\n      })\r\n    );\r\n  }\r\n\r\n  // Reference to the TableQuestionBuilder component\r\n  const tableBuilderRef = React.useRef<HTMLDivElement>(null);\r\n\r\n  // Function to trigger the submit event on the TableQuestionBuilder\r\n  const handleSaveClick = () => {\r\n    console.log(\"Save button clicked\");\r\n\r\n    // Try multiple approaches to find the table builder element\r\n    let tableBuilder = null;\r\n\r\n    // Approach 1: Direct reference through ref\r\n    if (tableBuilderRef.current) {\r\n      console.log(\"Using tableBuilderRef.current:\", tableBuilderRef.current);\r\n      tableBuilder = tableBuilderRef.current;\r\n    }\r\n\r\n    // Approach 2: Find by class name in the document\r\n    if (!tableBuilder) {\r\n      const allTableBuilders = document.querySelectorAll(\r\n        \".table-question-builder\"\r\n      );\r\n      console.log(\"Found table builders by class:\", allTableBuilders.length);\r\n\r\n      if (allTableBuilders.length > 0) {\r\n        tableBuilder = allTableBuilders[0];\r\n        console.log(\"Using first table builder found by class:\", tableBuilder);\r\n      }\r\n    }\r\n\r\n    // Approach 3: Find by class name in the container\r\n    if (!tableBuilder && tableBuilderRef.current) {\r\n      const containerTableBuilder = tableBuilderRef.current.querySelector(\r\n        \".table-question-builder\"\r\n      );\r\n      if (containerTableBuilder) {\r\n        tableBuilder = containerTableBuilder;\r\n        console.log(\"Using table builder found in container:\", tableBuilder);\r\n      }\r\n    }\r\n\r\n    if (tableBuilder) {\r\n      console.log(\"Dispatching submitTable event to:\", tableBuilder);\r\n\r\n      // Create and dispatch the event\r\n      const submitEvent = new CustomEvent(\"submitTable\", {\r\n        bubbles: true, // Allow event to bubble up the DOM tree\r\n        cancelable: true, // Allow event to be canceled\r\n        detail: { timestamp: Date.now() }, // Add some detail to help with debugging\r\n      });\r\n\r\n      tableBuilder.dispatchEvent(submitEvent);\r\n      console.log(\"Event dispatched successfully\");\r\n    } else {\r\n      console.error(\r\n        \"Could not find any table builder element to dispatch event to\"\r\n      );\r\n\r\n      // As a last resort, try to find any element with a similar class\r\n      const anyElement = document.querySelector(\"[class*='table']\");\r\n      if (anyElement) {\r\n        console.log(\"Found element with 'table' in class:\", anyElement);\r\n        console.log(\"Attempting to dispatch event to this element\");\r\n\r\n        const submitEvent = new CustomEvent(\"submitTable\", {\r\n          bubbles: true,\r\n          cancelable: true,\r\n          detail: { timestamp: Date.now(), isLastResort: true },\r\n        });\r\n\r\n        anyElement.dispatchEvent(submitEvent);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Function to handle confirmation before closing\r\n  const handleClose = () => {\r\n    // Show a confirmation dialog if there are unsaved changes\r\n    if (\r\n      window.confirm(\r\n        \"Are you sure you want to close? Any unsaved changes will be lost.\"\r\n      )\r\n    ) {\r\n      setShowModal(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={handleClose}\r\n      className=\"w-11/12 tablet:w-4/5 desktop:w-3/5\"\r\n      preventOutsideClick={true}\r\n    >\r\n      {isLoading && <LoadingOverlay />}\r\n\r\n      <div className=\"space-y-4\">\r\n        <h1 className=\"heading-text capitalize mb-4\">Edit Table Question</h1>\r\n\r\n        {tableData ? (\r\n          <div\r\n            ref={tableBuilderRef}\r\n            className=\"table-question-builder-container\"\r\n          >\r\n            <TableQuestionBuilder\r\n              projectId={contextId}\r\n              isInModal={true}\r\n              isEditMode={true}\r\n              existingTableData={tableData as any}\r\n              onTableCreated={handleTableUpdated}\r\n            />\r\n\r\n            {/* Add Save button outside the TableQuestionBuilder */}\r\n            <div className=\"flex items-center justify-end space-x-4 mt-6\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setShowModal(false)}\r\n                className=\"btn-outline\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleSaveClick}\r\n                className=\"btn-primary\"\r\n              >\r\n                Save Changes\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ) : !isLoading ? (\r\n          <div className=\"p-4 text-center\">\r\n            <p className=\"text-red-500\">\r\n              Could not load table data. Please try again.\r\n            </p>\r\n          </div>\r\n        ) : null}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { EditTableQuestionModal };\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;AAEA,MAAM,yBAAyB,CAAC,EAC9B,SAAS,EACT,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,SAAS,EAOV;;IACC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE3B,+DAA+D;IAC/D,MAAM,EACJ,MAAM,gBAAgB,EACtB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAiE;QAC1E,UAAU;YAAC;YAAiB,SAAS,EAAE;SAAC;QACxC,OAAO;+CAAE;gBACP,IAAI;oBACF,QAAQ,GAAG,CAAC,6CAA6C,SAAS,EAAE;oBAEpE,kFAAkF;oBAClF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE,EAAE;oBAClE,QAAQ,GAAG,CAAC,sBAAsB,SAAS,IAAI;oBAE/C,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;wBAChE,MAAM,EAAE,UAAU,aAAa,EAAE,UAAU,EAAE,GAAG,SAAS,IAAI,CAAC,IAAI;wBAClE,QAAQ,GAAG,CAAC,6BAA6B;wBACzC,QAAQ,GAAG,CAAC,0BAA0B;wBAEtC,OAAO;4BACL,UAAU;4BACV,YAAY,cAAc,CAAC;wBAC7B;oBACF,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,MAAM;gBACR;YACF;;QACA,SAAS,aAAa,SAAS,EAAE,GAAG,KAAK,SAAS,SAAS,KAAK;IAClE;IAEA,+EAA+E;IAC/E,MAAM,YAAY,6JAAA,CAAA,UAAK,CAAC,OAAO;qDAAC;YAC9B,IAAI,CAAC,kBAAkB,UAAU,OAAO;YAExC,MAAM,eAAe,iBAAiB,QAAQ;YAC9C,MAAM,aAAa,iBAAiB,UAAU,IAAI,CAAC;YAEnD,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,QAAQ,GAAG,CAAC,2BAA2B;YACvC,QAAQ,GAAG,CAAC,qBAAqB,OAAO,IAAI,CAAC;YAC7C,QAAQ,GAAG,CAAC,eAAe,aAAa,SAAS;YAEjD,mFAAmF;YACnF,MAAM,gBAAgB;gBACpB,IAAI,aAAa,EAAE;gBACnB,OAAO,aAAa,KAAK;gBACzB,cAAc,aAAa,YAAY,CAAC,GAAG;iEAAC,CAAC,MAAQ,CAAC;4BACpD,IAAI,IAAI,EAAE;4BACV,YAAY,IAAI,UAAU;4BAC1B,gBAAgB,IAAI,cAAc;4BAClC,cACE,IAAI,YAAY,EAAE;6EAAI,CAAC,QAAU,CAAC;wCAChC,IAAI,MAAM,EAAE;wCACZ,YAAY,MAAM,UAAU;wCAC5B,gBAAgB,MAAM,cAAc,IAAI,IAAI,EAAE;oCAChD,CAAC;+EAAM,EAAE;wBACb,CAAC;;gBACD,WAAW,aAAa,SAAS,CAAC,GAAG;iEAAC,CAAC,MAAQ,CAAC;4BAC9C,IAAI,IAAI,EAAE;4BACV,UAAU,IAAI,QAAQ;4BACtB,oDAAoD;4BACpD,eAAe,OAAO,OAAO,CAAC,YAC3B,MAAM;6EAAC,CAAC,CAAC,KAAK,MAAM;oCACnB,MAAM,CAAC,UAAU,MAAM,GAAG,IAAI,KAAK,CAAC;oCACpC,OACE,SAAS,WAAW,IAAI,EAAE,IAC1B,MAAM,KAAK,IACX,MAAM,KAAK,CAAC,IAAI,OAAO;gCAE3B;4EACC,GAAG;6EAAC,CAAC,CAAC,KAAK,MAAM;oCAChB,MAAM,CAAC,SAAS,GAAG,IAAI,KAAK,CAAC;oCAC7B,OAAO;wCACL,UAAU,SAAS;wCACnB,OAAO,MAAM,KAAK,IAAI;wCACtB,MAAM,MAAM,IAAI,IAAI,MAAM,KAAK,IAAI;oCACrC;gCACF;;wBACJ,CAAC;;gBACD,wDAAwD;gBACxD,YAAY;YACd;YAEA,QAAQ,GAAG,CAAC,yBAAyB;YACrC,OAAO;QACT;oDAAG;QAAC;KAAiB;IAErB,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,QAAQ,GAAG,CAAC,6CAA6C;YACzD,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC,8BAA8B;YAC5C;QACF;2CAAG;QAAC;QAAW;KAAS;IAExB,MAAM,WACJ,gBAAgB,YACZ;QAAC;KAAY,GACb,gBAAgB,aAChB;QAAC;KAAoB,GACrB;QAAC;KAAyB;IAEhC,MAAM,qBAAqB,OAAO;QAChC,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,IAAI;YACF,4EAA4E;YAC5E,MAAM,YAAY,iBAAiB,CAAC;gBAClC;gBACA,OAAO;YACT;YAEA,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAiB,SAAS,EAAE;iBAAC;gBACxC,OAAO;YACT;YAEA,+EAA+E;YAC/E,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;iBAAiB;gBAC5B,OAAO;YACT;YAEA,0DAA0D;YAC1D,MAAM,YAAY,iBAAiB,CAAC;gBAClC,WAAW,CAAC;oBACV,MAAM,WAAW,MAAM,QAAQ;oBAC/B,OACE,MAAM,OAAO,CAAC,aACd,CAAC,SAAS,QAAQ,CAAC,oBACjB,SAAS,QAAQ,CAAC,qBAClB,SAAS,QAAQ,CAAC,oBACjB,SAAS,QAAQ,CAAC,SAAS,EAAE,KAC5B,OAAO,SAAS,EAAE,KAAK,QAAS;gBAExC;YACF;YAEA,0CAA0C;YAC1C,MAAM,YAAY,cAAc,CAAC;gBAC/B,UAAU;oBAAC;oBAAiB,SAAS,EAAE;iBAAC;YAC1C;YAEA,QAAQ,GAAG,CACT;YAGF,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAGF,iFAAiF;YACjF,WAAW;gBACT,QAAQ,GAAG,CAAC;gBACZ,aAAa;YACf,GAAG,MAAM,gDAAgD;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAE5D,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SACE;gBACF,MAAM;YACR;YAGF,sCAAsC;YACtC,WAAW;gBACT,aAAa;YACf,GAAG;QACL;IACF;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;YACf,SAAS;YACT,MAAM;QACR;IAEJ;IAEA,kDAAkD;IAClD,MAAM,kBAAkB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAErD,mEAAmE;IACnE,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC;QAEZ,4DAA4D;QAC5D,IAAI,eAAe;QAEnB,2CAA2C;QAC3C,IAAI,gBAAgB,OAAO,EAAE;YAC3B,QAAQ,GAAG,CAAC,kCAAkC,gBAAgB,OAAO;YACrE,eAAe,gBAAgB,OAAO;QACxC;QAEA,iDAAiD;QACjD,IAAI,CAAC,cAAc;YACjB,MAAM,mBAAmB,SAAS,gBAAgB,CAChD;YAEF,QAAQ,GAAG,CAAC,kCAAkC,iBAAiB,MAAM;YAErE,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,eAAe,gBAAgB,CAAC,EAAE;gBAClC,QAAQ,GAAG,CAAC,6CAA6C;YAC3D;QACF;QAEA,kDAAkD;QAClD,IAAI,CAAC,gBAAgB,gBAAgB,OAAO,EAAE;YAC5C,MAAM,wBAAwB,gBAAgB,OAAO,CAAC,aAAa,CACjE;YAEF,IAAI,uBAAuB;gBACzB,eAAe;gBACf,QAAQ,GAAG,CAAC,2CAA2C;YACzD;QACF;QAEA,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,gCAAgC;YAChC,MAAM,cAAc,IAAI,YAAY,eAAe;gBACjD,SAAS;gBACT,YAAY;gBACZ,QAAQ;oBAAE,WAAW,KAAK,GAAG;gBAAG;YAClC;YAEA,aAAa,aAAa,CAAC;YAC3B,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,QAAQ,KAAK,CACX;YAGF,iEAAiE;YACjE,MAAM,aAAa,SAAS,aAAa,CAAC;YAC1C,IAAI,YAAY;gBACd,QAAQ,GAAG,CAAC,wCAAwC;gBACpD,QAAQ,GAAG,CAAC;gBAEZ,MAAM,cAAc,IAAI,YAAY,eAAe;oBACjD,SAAS;oBACT,YAAY;oBACZ,QAAQ;wBAAE,WAAW,KAAK,GAAG;wBAAI,cAAc;oBAAK;gBACtD;gBAEA,WAAW,aAAa,CAAC;YAC3B;QACF;IACF;IAEA,iDAAiD;IACjD,MAAM,cAAc;QAClB,0DAA0D;QAC1D,IACE,OAAO,OAAO,CACZ,sEAEF;YACA,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;QACV,qBAAqB;;YAEpB,2BAAa,6LAAC,2IAAA,CAAA,iBAAc;;;;;0BAE7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA+B;;;;;;oBAE5C,0BACC,6LAAC;wBACC,KAAK;wBACL,WAAU;;0CAEV,6LAAC,yJAAA,CAAA,uBAAoB;gCACnB,WAAW;gCACX,WAAW;gCACX,YAAY;gCACZ,mBAAmB;gCACnB,gBAAgB;;;;;;0CAIlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;+BAKH,CAAC,0BACH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;+BAI5B;;;;;;;;;;;;;AAIZ;GA3VM;;QAagB,yLAAA,CAAA,iBAAc;QACjB,4JAAA,CAAA,cAAW;QAOxB,8KAAA,CAAA,WAAQ;;;KArBR", "debugId": null}}, {"offset": {"line": 6890, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/modals/ConfirmationModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Modal from \"./Modal\";\r\n\r\nconst ConfirmationModal = ({\r\n  showModal,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  description,\r\n  confirmButtonText,\r\n  cancelButtonText,\r\n  confirmButtonClass,\r\n  children,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  description: React.ReactNode; // Accept ReactNode for flexible content\r\n  confirmButtonText: string;\r\n  cancelButtonText?: string;\r\n  confirmButtonClass?: string;\r\n  children?: React.ReactNode; // Additional content like warnings or icons\r\n}) => {\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={onClose}\r\n      className=\"p-6 rounded-md max-w-xl\"\r\n    >\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">{title}</h2>\r\n      <div className=\"text-neutral-700 mt-2\">{description}</div>\r\n      {children && <div className=\"mt-6 space-y-4\">{children}</div>}\r\n      <div className=\"flex justify-end gap-4 mt-6\">\r\n        <button className=\"btn-outline\" onClick={onClose} type=\"button\">\r\n          {cancelButtonText || \"Cancel\"}\r\n        </button>\r\n        <button\r\n          className={`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${confirmButtonClass}`}\r\n          onClick={onConfirm}\r\n          type=\"button\"\r\n        >\r\n          {confirmButtonText}\r\n        </button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ConfirmationModal };\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,EAWT;IACC,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,6LAAC;gBAAI,WAAU;0BAAyB;;;;;;YACvC,0BAAY,6LAAC;gBAAI,WAAU;0BAAkB;;;;;;0BAC9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;wBAAc,SAAS;wBAAS,MAAK;kCACpD,oBAAoB;;;;;;kCAEvB,6LAAC;wBACC,WAAW,CAAC,+IAA+I,EAAE,oBAAoB;wBACjL,SAAS;wBACT,MAAK;kCAEJ;;;;;;;;;;;;;;;;;;AAKX;KA5CM", "debugId": null}}, {"offset": {"line": 6977, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/modals/QuestionGroupModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { X } from \"lucide-react\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { createQuestionGroup, updateQuestionGroup } from \"@/lib/api/question-groups\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { ContextType } from \"@/types\";\r\n\r\ninterface QuestionGroupModalProps {\r\n  showModal: boolean;\r\n  setShowModal: (show: boolean) => void;\r\n  contextType: ContextType; // Kept for future use if needed\r\n  contextId: number;\r\n  existingGroup?: {\r\n    id: number;\r\n    title: string;\r\n    order: number;\r\n  };\r\n  questions: Question[];\r\n  questionGroups?: Array<{\r\n    id: number;\r\n    title: string;\r\n  }>;\r\n}\r\n\r\nconst QuestionGroupModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  contextType: _contextType, // Renamed to avoid unused variable warning\r\n  contextId,\r\n  existingGroup,\r\n  questions,\r\n  questionGroups = [],\r\n}: QuestionGroupModalProps) => {\r\n  const [title, setTitle] = useState(\"\");\r\n  const [selectedQuestionIds, setSelectedQuestionIds] = useState<number[]>([]);\r\n\r\n  const dispatch = useDispatch();\r\n  const queryClient = useQueryClient();\r\n\r\n  // Reset form when modal opens/closes or when editing a different group\r\n  useEffect(() => {\r\n    if (showModal) {\r\n      if (existingGroup) {\r\n        setTitle(existingGroup.title);\r\n\r\n        // Fetch questions that belong to this group\r\n        const questionsInGroup = questions.filter(q => q.questionGroupId === existingGroup.id);\r\n        setSelectedQuestionIds(questionsInGroup.map(q => q.id));\r\n      } else {\r\n        setTitle(\"\");\r\n        setSelectedQuestionIds([]);\r\n      }\r\n    }\r\n  }, [showModal, existingGroup, questions]);\r\n\r\n  // Make all questions available for selection, regardless of their group status\r\n  // This allows moving questions between groups\r\n  const availableQuestions = questions;\r\n\r\n  // Create group mutation\r\n  const createGroupMutation = useMutation({\r\n    mutationFn: createQuestionGroup,\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"questionGroups\", contextId] });\r\n      queryClient.invalidateQueries({ queryKey: [\"questions\", contextId] });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question group created successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setShowModal(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error creating question group:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to create question group\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  // Update group mutation\r\n  const updateGroupMutation = useMutation({\r\n    mutationFn: updateQuestionGroup,\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"questionGroups\", contextId] });\r\n      queryClient.invalidateQueries({ queryKey: [\"questions\", contextId] });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question group updated successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setShowModal(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error updating question group:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to update question group\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!title.trim()) {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Group title is required\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    if (existingGroup) {\r\n     \r\n\r\n      updateGroupMutation.mutate({\r\n        id: existingGroup.id,\r\n        title,\r\n        order: existingGroup.order,\r\n        selectedQuestionIds,\r\n      });\r\n    } else {\r\n     \r\n\r\n      createGroupMutation.mutate({\r\n        title,\r\n        order: questionGroups.length + 1, // Set order to be after existing groups\r\n        projectId: contextId,\r\n        selectedQuestionIds,\r\n      });\r\n    }\r\n  };\r\n\r\n  if (!showModal) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40\">\r\n      <div className=\"bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto p-4\">\r\n        <div className=\"flex justify-between items-center p-4 \">\r\n          <h2 className=\"text-xl font-semibold\">\r\n            {existingGroup ? \"Edit Question Group\" : \"Create Question Group\"}\r\n          </h2>\r\n          <button\r\n            onClick={() => setShowModal(false)}\r\n            className=\"text-gray-500 hover:text-gray-700 cursor-pointer\"\r\n          >\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"p-4\">\r\n          <div className=\"group label-input-group \">\r\n            <label htmlFor=\"title\">\r\n              Group Title\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"title\"\r\n              value={title}\r\n              onChange={(e) => setTitle(e.target.value)}\r\n              className=\" input-field w-full\"\r\n              placeholder=\"Enter group title\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n\r\n\r\n          <div className=\"mt-8 label-input-group\">\r\n            <label>\r\n              Select Questions for this Group\r\n            </label>\r\n            <div className=\"border border-neutral-300 rounded-md p-2 max-h-60 overflow-y-auto\">\r\n              {availableQuestions.length > 0 ? (\r\n                availableQuestions.map((question) => {\r\n                  // Find the group this question belongs to (if any)\r\n                  const questionGroup = question.questionGroupId\r\n                    ? questionGroups.find(g => g.id === question.questionGroupId)\r\n                    : null;\r\n\r\n                  return (\r\n                    <div key={question.id} className=\"flex gap-2 items-center mb-3 p-2 border-b border-neutral-300\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id={`question-${question.id}`}\r\n                        checked={selectedQuestionIds.includes(question.id)}\r\n                        onChange={(e) => {\r\n                          if (e.target.checked) {\r\n                            setSelectedQuestionIds([...selectedQuestionIds, question.id]);\r\n                          } else {\r\n                            setSelectedQuestionIds(selectedQuestionIds.filter(id => id !== question.id));\r\n                          }\r\n                        }}\r\n                        className=\"mr-2 cursor-pointer w-5 h-5\"\r\n                      />\r\n                      <div>\r\n                        <label htmlFor={`question-${question.id}`} className=\"text-sm\">\r\n                          {question.label}\r\n                        </label>\r\n\r\n                        {/* Show which group the question belongs to */}\r\n                        {questionGroup && questionGroup.id !== (existingGroup?.id || -1) && (\r\n                          <div className=\"text-xs text-neutral-700 mt-1\">\r\n                            Currently in group: <span className=\"font-medium text-amber-600\">{questionGroup.title}</span>\r\n                            <span className=\"ml-1 text-neutral-700\">(will be moved to this group)</span>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })\r\n              ) : (\r\n                <p className=\"text-gray-500 text-sm p-2\">\r\n                  No available questions. Please add some questions first.\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex justify-end space-x-2 mt-4\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setShowModal(false)}\r\n              className=\"btn-outline\"\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              type=\"submit\"\r\n              className=\"px-4 py-2 btn-primary\"\r\n              disabled={createGroupMutation.isPending || updateGroupMutation.isPending}\r\n            >\r\n              {createGroupMutation.isPending || updateGroupMutation.isPending\r\n                ? \"Saving...\"\r\n                : existingGroup\r\n                ? \"Update Group\"\r\n                : \"Create Group\"}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { QuestionGroupModal };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AA4BA,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,YAAY,EACZ,aAAa,YAAY,EACzB,SAAS,EACT,aAAa,EACb,SAAS,EACT,iBAAiB,EAAE,EACK;;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE3E,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,uEAAuE;IACvE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,WAAW;gBACb,IAAI,eAAe;oBACjB,SAAS,cAAc,KAAK;oBAE5B,4CAA4C;oBAC5C,MAAM,mBAAmB,UAAU,MAAM;yEAAC,CAAA,IAAK,EAAE,eAAe,KAAK,cAAc,EAAE;;oBACrF,uBAAuB,iBAAiB,GAAG;wDAAC,CAAA,IAAK,EAAE,EAAE;;gBACvD,OAAO;oBACL,SAAS;oBACT,uBAAuB,EAAE;gBAC3B;YACF;QACF;uCAAG;QAAC;QAAW;QAAe;KAAU;IAExC,+EAA+E;IAC/E,8CAA8C;IAC9C,MAAM,qBAAqB;IAE3B,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;mEAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB;qBAAU;gBAAC;gBACxE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa;qBAAU;gBAAC;gBACnE,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,aAAa;YACf;;QACA,OAAO;mEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;mEAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB;qBAAU;gBAAC;gBACxE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa;qBAAU;gBAAC;gBACnE,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,aAAa;YACf;;QACA,OAAO;mEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF;QACF;QAEA,IAAI,eAAe;YAGjB,oBAAoB,MAAM,CAAC;gBACzB,IAAI,cAAc,EAAE;gBACpB;gBACA,OAAO,cAAc,KAAK;gBAC1B;YACF;QACF,OAAO;YAGL,oBAAoB,MAAM,CAAC;gBACzB;gBACA,OAAO,eAAe,MAAM,GAAG;gBAC/B,WAAW;gBACX;YACF;QACF;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,gBAAgB,wBAAwB;;;;;;sCAE3C,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;8CAAQ;;;;;;8CAGvB,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAM;;;;;;8CAGP,6LAAC;oCAAI,WAAU;8CACZ,mBAAmB,MAAM,GAAG,IAC3B,mBAAmB,GAAG,CAAC,CAAC;wCACtB,mDAAmD;wCACnD,MAAM,gBAAgB,SAAS,eAAe,GAC1C,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,eAAe,IAC1D;wCAEJ,qBACE,6LAAC;4CAAsB,WAAU;;8DAC/B,6LAAC;oDACC,MAAK;oDACL,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;oDAC7B,SAAS,oBAAoB,QAAQ,CAAC,SAAS,EAAE;oDACjD,UAAU,CAAC;wDACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4DACpB,uBAAuB;mEAAI;gEAAqB,SAAS,EAAE;6DAAC;wDAC9D,OAAO;4DACL,uBAAuB,oBAAoB,MAAM,CAAC,CAAA,KAAM,OAAO,SAAS,EAAE;wDAC5E;oDACF;oDACA,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAM,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;4DAAE,WAAU;sEAClD,SAAS,KAAK;;;;;;wDAIhB,iBAAiB,cAAc,EAAE,KAAK,CAAC,eAAe,MAAM,CAAC,CAAC,mBAC7D,6LAAC;4DAAI,WAAU;;gEAAgC;8EACzB,6LAAC;oEAAK,WAAU;8EAA8B,cAAc,KAAK;;;;;;8EACrF,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;2CAvBtC,SAAS,EAAE;;;;;oCA6BzB,mBAEA,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;;;;;;sCAO/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,UAAU,oBAAoB,SAAS,IAAI,oBAAoB,SAAS;8CAEvE,oBAAoB,SAAS,IAAI,oBAAoB,SAAS,GAC3D,cACA,gBACA,iBACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GArOM;;QAYa,4JAAA,CAAA,cAAW;QACR,yLAAA,CAAA,iBAAc;QAuBN,iLAAA,CAAA,cAAW;QAyBX,iLAAA,CAAA,cAAW;;;KA7DnC", "debugId": null}}, {"offset": {"line": 7369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/modals/DeleteQuestionGroupModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { X } from \"lucide-react\";\r\n\r\ninterface DeleteQuestionGroupModalProps {\r\n  showModal: boolean;\r\n  setShowModal: (show: boolean) => void;\r\n  onConfirmDelete: () => void;\r\n  onConfirmDeleteWithQuestions: () => void;\r\n  isDeleting: boolean;\r\n}\r\n\r\nconst DeleteQuestionGroupModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  onConfirmDelete,\r\n  onConfirmDeleteWithQuestions,\r\n  isDeleting,\r\n}: DeleteQuestionGroupModalProps) => {\r\n  if (!showModal) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40\">\r\n      <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md\">\r\n        <div className=\"flex justify-between items-center p-4 border-b\">\r\n          <h2 className=\"text-xl font-semibold\">Delete Question Group</h2>\r\n          <button\r\n            onClick={() => setShowModal(false)}\r\n            className=\"text-gray-500 hover:text-gray-700\"\r\n            disabled={isDeleting}\r\n          >\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"p-4\">\r\n          <p className=\"mb-4\">\r\n            How would you like to delete this question group?\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            <button\r\n              onClick={onConfirmDelete}\r\n              className=\"w-full px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600\"\r\n              disabled={isDeleting}\r\n            >\r\n              {isDeleting ? \"Deleting...\" : \"Delete Group Only (Keep Questions)\"}\r\n            </button>\r\n            <p className=\"text-sm text-gray-600 mb-4\">\r\n              This will remove the group but keep all questions. Questions will be available to add to other groups.\r\n            </p>\r\n\r\n            <button\r\n              onClick={onConfirmDeleteWithQuestions}\r\n              className=\"w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600\"\r\n              disabled={isDeleting}\r\n            >\r\n              {isDeleting ? \"Deleting...\" : \"Delete Group and All Questions\"}\r\n            </button>\r\n            <p className=\"text-sm text-gray-600 mb-4\">\r\n              This will permanently delete the group and all questions inside it.\r\n            </p>\r\n\r\n            <button\r\n              onClick={() => setShowModal(false)}\r\n              className=\"w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\r\n              disabled={isDeleting}\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { DeleteQuestionGroupModal };\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAaA,MAAM,2BAA2B,CAAC,EAChC,SAAS,EACT,YAAY,EACZ,eAAe,EACf,4BAA4B,EAC5B,UAAU,EACoB;IAC9B,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwB;;;;;;sCACtC,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;4BACV,UAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAO;;;;;;sCAIpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,UAAU;8CAET,aAAa,gBAAgB;;;;;;8CAEhC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,UAAU;8CAET,aAAa,gBAAgB;;;;;;8CAEhC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,UAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KA/DM", "debugId": null}}, {"offset": {"line": 7514, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-builder/LibraryQuestionsSidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { fetchQuestionBlockQuestions } from \"@/lib/api/form-builder\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { Search, X } from \"lucide-react\";\r\nimport Spinner from \"../general/Spinner\";\r\n\r\ninterface LibraryQuestionsSidebarProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onAddQuestions: (questions: Question[]) => void;\r\n}\r\n\r\nconst LibraryQuestionsSidebar: React.FC<LibraryQuestionsSidebarProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onAddQuestions,\r\n}) => {\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [selectedQuestions, setSelectedQuestions] = useState<Question[]>([]);\r\n  const [expandDetails, setExpandDetails] = useState(true);\r\n\r\n  // Fetch question block questions\r\n  const {\r\n    data: libraryQuestions,\r\n    isLoading,\r\n    isError,\r\n  } = useQuery<Question[]>({\r\n    queryKey: [\"libraryQuestions\"],\r\n    queryFn: () => fetchQuestionBlockQuestions(),\r\n    enabled: isOpen, // Only fetch when sidebar is open\r\n  });\r\n\r\n  // Filter questions based on search term\r\n  const filteredQuestions = libraryQuestions\r\n    ? libraryQuestions.filter((question) =>\r\n        question.label.toLowerCase().includes(searchTerm.toLowerCase())\r\n      )\r\n    : [];\r\n\r\n  // Handle question selection\r\n  const toggleQuestionSelection = (question: Question) => {\r\n    if (selectedQuestions.some((q) => q.id === question.id)) {\r\n      setSelectedQuestions(selectedQuestions.filter((q) => q.id !== question.id));\r\n    } else {\r\n      setSelectedQuestions([...selectedQuestions, question]);\r\n    }\r\n  };\r\n\r\n  // Add selected questions to the form\r\n  const handleAddQuestions = () => {\r\n    onAddQuestions(selectedQuestions);\r\n    setSelectedQuestions([]);\r\n    onClose();\r\n  };\r\n\r\n  // Reset selections when sidebar closes\r\n  useEffect(() => {\r\n    if (!isOpen) {\r\n      setSelectedQuestions([]);\r\n      setSearchTerm(\"\");\r\n    }\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 flex justify-end\">\r\n      {/* Backdrop */}\r\n      <div\r\n        className=\"absolute inset-0 bg-neutral-900/50\"\r\n        onClick={onClose}\r\n      ></div>\r\n\r\n      {/* Sidebar */}\r\n      <div className=\"relative w-full max-w-md bg-neutral-50 h-full overflow-auto shadow-xl\">\r\n        <div className=\"p-4 border-b border-neutral-200\">\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <h2 className=\"text-xl font-bold\">Search Library</h2>\r\n            <button\r\n              onClick={onClose}\r\n              className=\"self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Search input */}\r\n          <div className=\"relative mb-4\">\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Search...\"\r\n              className=\"input-field w-full p-2 pl-10\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n            />\r\n            <Search className=\"absolute left-3 top-2.5 \" size={18} />\r\n          </div>\r\n\r\n          {/* Tags filter (placeholder) */}\r\n          {/* <div className=\"mb-4\">\r\n            <button className=\"w-full p-2 text-left border border-gray-300 rounded-md flex justify-between items-center\">\r\n              <span className=\"text-gray-500\">Search Tags</span>\r\n              <span>▼</span>\r\n            </button>\r\n          </div> */}\r\n\r\n          {/* Collection filter (placeholder) */}\r\n          {/* <div className=\"mb-4\">\r\n            <button className=\"w-full p-2 text-left border border-gray-300 rounded-md flex justify-between items-center\">\r\n              <span className=\"text-gray-500\">Select Collection Name</span>\r\n              <span>▼</span>\r\n            </button>\r\n          </div> */}\r\n\r\n          {/* Results count and expand toggle */}\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <span>\r\n              {filteredQuestions.length} asset{filteredQuestions.length !== 1 ? \"s\" : \"\"} found\r\n            </span>\r\n            <label className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={expandDetails}\r\n                onChange={() => setExpandDetails(!expandDetails)}\r\n                className=\"mr-2\"\r\n              />\r\n              expand details\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Question list */}\r\n        <div className=\"p-4\">\r\n          {isLoading ? (\r\n            <div className=\"flex justify-center p-8\">\r\n              <Spinner />\r\n            </div>\r\n          ) : isError ? (\r\n            <div className=\"text-red-500 p-4 text-center\">\r\n              Error loading library questions\r\n            </div>\r\n          ) : filteredQuestions.length === 0 ? (\r\n            <div className=\"text-neutral-700 p-4 text-center\">\r\n              No questions found\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-2\">\r\n              {filteredQuestions.map((question) => (\r\n                <div\r\n                  key={question.id}\r\n                  className=\"border border-neutral-500 rounded-md p-3\"\r\n                >\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={selectedQuestions.some((q) => q.id === question.id)}\r\n                      onChange={() => toggleQuestionSelection(question)}\r\n                      className=\"mr-3 h-5 w-5 cursor-pointer\"\r\n                    />\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"font-medium\">{question.label}</div>\r\n                      {expandDetails && (\r\n                        <div className=\"text-sm text-neutral-700 mt-1\">\r\n                          Type: {String(question.inputType)}\r\n                          {question.hint && <div>Hint: {question.hint}</div>}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Action buttons */}\r\n        <div className=\"border-t border-gray-200 p-4 sticky bottom-0 bg-neutral-50\">\r\n          <div className=\"flex justify-between\">\r\n            <button\r\n              onClick={onClose}\r\n              className=\"btn-outline\"\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              onClick={handleAddQuestions}\r\n              disabled={selectedQuestions.length === 0}\r\n              className={`px-4 py-2 rounded-md ${\r\n                selectedQuestions.length > 0\r\n                  ? \"btn-primary\"\r\n                  : \"bg-gray-200 text-gray-500 pointer-events-none\"\r\n              }`}\r\n            >\r\n              Add Selected ({selectedQuestions.length})\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LibraryQuestionsSidebar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AACA;;;AAPA;;;;;;AAeA,MAAM,0BAAkE,CAAC,EACvE,MAAM,EACN,OAAO,EACP,cAAc,EACf;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,iCAAiC;IACjC,MAAM,EACJ,MAAM,gBAAgB,EACtB,SAAS,EACT,OAAO,EACR,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UAAU;YAAC;SAAmB;QAC9B,OAAO;gDAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD;;QACzC,SAAS;IACX;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,mBACtB,iBAAiB,MAAM,CAAC,CAAC,WACvB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAE9D,EAAE;IAEN,4BAA4B;IAC5B,MAAM,0BAA0B,CAAC;QAC/B,IAAI,kBAAkB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE,GAAG;YACvD,qBAAqB,kBAAkB,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE;QAC3E,OAAO;YACL,qBAAqB;mBAAI;gBAAmB;aAAS;QACvD;IACF;IAEA,qCAAqC;IACrC,MAAM,qBAAqB;QACzB,eAAe;QACf,qBAAqB,EAAE;QACvB;IACF;IAEA,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,CAAC,QAAQ;gBACX,qBAAqB,EAAE;gBACvB,cAAc;YAChB;QACF;4CAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAKb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;kDAE/C,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;wCAA2B,MAAM;;;;;;;;;;;;0CAoBrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CACE,kBAAkB,MAAM;4CAAC;4CAAO,kBAAkB,MAAM,KAAK,IAAI,MAAM;4CAAG;;;;;;;kDAE7E,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,IAAM,iBAAiB,CAAC;gDAClC,WAAU;;;;;;4CACV;;;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBAAI,WAAU;kCACZ,0BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,UAAO;;;;;;;;;mCAER,wBACF,6LAAC;4BAAI,WAAU;sCAA+B;;;;;mCAG5C,kBAAkB,MAAM,KAAK,kBAC/B,6LAAC;4BAAI,WAAU;sCAAmC;;;;;iDAIlD,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;oCAEC,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,kBAAkB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE;gDAC3D,UAAU,IAAM,wBAAwB;gDACxC,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAe,SAAS,KAAK;;;;;;oDAC3C,+BACC,6LAAC;wDAAI,WAAU;;4DAAgC;4DACtC,OAAO,SAAS,SAAS;4DAC/B,SAAS,IAAI,kBAAI,6LAAC;;oEAAI;oEAAO,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;mCAf9C,SAAS,EAAE;;;;;;;;;;;;;;;kCA2B1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU,kBAAkB,MAAM,KAAK;oCACvC,WAAW,CAAC,qBAAqB,EAC/B,kBAAkB,MAAM,GAAG,IACvB,gBACA,iDACJ;;wCACH;wCACgB,kBAAkB,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtD;GA5LM;;QAcA,8KAAA,CAAA,WAAQ;;;KAdR;uCA8LS", "debugId": null}}, {"offset": {"line": 7875, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/components/form-builder/FormBuilder.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport {\r\n  Dnd<PERSON>ontext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent,\r\n} from \"@dnd-kit/core\";\r\nimport {\r\n  SortableContext,\r\n  verticalListSortingStrategy,\r\n  arrayMove,\r\n} from \"@dnd-kit/sortable\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { QuestionItem } from \"@/components/form-builder/QuestionItem\";\r\nimport { QuestionGroupItem } from \"@/components/form-builder/QuestionGroupItem\";\r\nimport {\r\n  Eye,\r\n  FileDown,\r\n  FileUp,\r\n  PlusCircle,\r\n  Settings,\r\n  BookOpen,\r\n  FolderPlus,\r\n} from \"lucide-react\";\r\nimport { AddQuestionModal } from \"../modals/AddQuestionModal\";\r\nimport { EditQuestionModal } from \"../modals/EditQuestionModal\";\r\nimport { EditTableQuestionModal } from \"../modals/EditTableQuestionModal\";\r\nimport { ConfirmationModal } from \"../modals/ConfirmationModal\";\r\nimport { QuestionGroupModal } from \"../modals/QuestionGroupModal\";\r\nimport { DeleteQuestionGroupModal } from \"../modals/DeleteQuestionGroupModal\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport {\r\n  deleteQuestion,\r\n  duplicateQuestion,\r\n  addQuestion,\r\n  updateQuestionPositions,\r\n} from \"@/lib/api/form-builder\";\r\nimport {\r\n  fetchQuestionGroups,\r\n  createQuestionGroup,\r\n  updateQuestionGroup,\r\n  deleteQuestionGroup,\r\n  deleteQuestionAndGroup,\r\n  // removeQuestionFromGroup - will be used in future implementation\r\n} from \"@/lib/api/question-groups\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { ContextType } from \"@/types\";\r\nimport { LoadingOverlay } from \"../general/LoadingOverlay\";\r\nimport LibraryQuestionsSidebar from \"./LibraryQuestionsSidebar\";\r\nimport { ProjectPermissionFlags } from \"@/types\";\r\nconst FormBuilder = ({\r\n  setIsPreviewMode,\r\n  questions,\r\n  contextType,\r\n  contextId,\r\n  permissions,\r\n}: {\r\n  setIsPreviewMode: React.Dispatch<React.SetStateAction<boolean>>;\r\n  questions: Question[];\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  permissions: ProjectPermissionFlags;\r\n}) => {\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor, {\r\n      activationConstraint: {\r\n        distance: 8,\r\n      },\r\n    }),\r\n    useSensor(KeyboardSensor)\r\n  );\r\n\r\n  const editFormPermission = permissions.manageProject || permissions.editForm;\r\n\r\n  // Question modals state\r\n  const [showAddQuestionModal, setShowAddQuestionModal] =\r\n    useState<boolean>(false);\r\n  const [showEditQuestionModal, setShowEditQuestionModal] =\r\n    useState<boolean>(false);\r\n  const [showEditTableQuestionModal, setShowEditTableQuestionModal] =\r\n    useState<boolean>(false);\r\n  const [showLibrarySidebar, setShowLibrarySidebar] = useState<boolean>(false);\r\n  const [isAddingQuestions, setIsAddingQuestions] = useState<boolean>(false);\r\n  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);\r\n  const [showDeleteConfirmationModal, setShowDeleteConfirmationModal] =\r\n    useState<boolean>(false);\r\n\r\n  // Question group modals state\r\n  const [showAddGroupModal, setShowAddGroupModal] = useState<boolean>(false);\r\n  const [showEditGroupModal, setShowEditGroupModal] = useState<boolean>(false);\r\n  const [showDeleteGroupModal, setShowDeleteGroupModal] =\r\n    useState<boolean>(false);\r\n  const [currentGroupId, setCurrentGroupId] = useState<number | null>(null);\r\n\r\n  // Question selection state\r\n  const [selectedQuestionIds, setSelectedQuestionIds] = useState<number[]>([]);\r\n  const [isEditingGroupName, setIsEditingGroupName] = useState<number | null>(\r\n    null\r\n  );\r\n  const [editingGroupName, setEditingGroupName] = useState<string>(\"\");\r\n  const [selectionMode, setSelectionMode] = useState<boolean>(false);\r\n\r\n  // State for tracking when questions are being added\r\n  const [isProcessingGroup, setIsProcessingGroup] = useState(false);\r\n\r\n  const dispatch = useDispatch();\r\n  const queryClient = useQueryClient();\r\n\r\n  const questionsQueryKey =\r\n    contextType === \"project\"\r\n      ? [\"questions\", contextId]\r\n      : contextType === \"template\"\r\n      ? [\"templateQuestions\", contextId]\r\n      : [\"questionBlockQuestions\", contextId];\r\n\r\n  const groupsQueryKey = [\"questionGroups\", contextId];\r\n\r\n  // Fetch question groups\r\n  const { data: questionGroups = [], isLoading: isLoadingGroups } = useQuery({\r\n    queryKey: groupsQueryKey,\r\n    queryFn: () => fetchQuestionGroups({ projectId: contextId }),\r\n    enabled: contextType === \"project\", // Only fetch for projects, not templates or question blocks\r\n  });\r\n\r\n  // Group questions by their group ID and sort by position within each group\r\n  const groupedQuestions = questionGroups.reduce(\r\n    (acc: Record<number, Question[]>, group: QuestionGroup) => {\r\n      acc[group.id] = questions\r\n        .filter((q) => q.questionGroupId === group.id)\r\n        .sort((a, b) => a.position - b.position);\r\n      return acc;\r\n    },\r\n    {} as Record<number, Question[]>\r\n  );\r\n\r\n  // Get ungrouped questions\r\n  const ungroupedQuestions = questions.filter((q) => !q.questionGroupId);\r\n\r\n  // Create a unified list of form items (groups and individual questions) for dynamic ordering\r\n  const createUnifiedFormItems = () => {\r\n    const items: Array<{\r\n      type: 'group' | 'question';\r\n      data: QuestionGroup | Question;\r\n      order: number;\r\n      originalPosition?: number; // Track original position for better sorting\r\n    }> = [];\r\n\r\n    // Only add question groups for projects (not templates or question blocks)\r\n    if (contextType === \"project\") {\r\n      questionGroups.forEach((group: QuestionGroup) => {\r\n        // For groups, find the minimum position of questions in the group\r\n        // This ensures the group appears where the first question was originally positioned\r\n        const groupQuestions = questions\r\n          .filter(q => q.questionGroupId === group.id)\r\n          .sort((a, b) => a.position - b.position);\r\n        const minQuestionPosition = groupQuestions.length > 0\r\n          ? Math.min(...groupQuestions.map(q => q.position))\r\n          : group.order;\r\n\r\n        items.push({\r\n          type: 'group',\r\n          data: group,\r\n          order: minQuestionPosition, // Use the position of the first question in the group\r\n          originalPosition: minQuestionPosition\r\n        });\r\n      });\r\n    }\r\n\r\n    // Add ungrouped questions (or all questions if not in project context)\r\n    const questionsToAdd = contextType === \"project\" ? ungroupedQuestions : questions;\r\n    questionsToAdd.forEach((question: Question) => {\r\n      items.push({\r\n        type: 'question',\r\n        data: question,\r\n        order: question.position,\r\n        originalPosition: question.position\r\n      });\r\n    });\r\n\r\n    // Sort by order/position, with a secondary sort by type to ensure consistent ordering\r\n    // when groups and questions have the same position\r\n    return items.sort((a, b) => {\r\n      if (a.order === b.order) {\r\n        // If positions are equal, prioritize based on original question positions\r\n        // This helps maintain the original flow when grouping questions\r\n        return (a.originalPosition || a.order) - (b.originalPosition || b.order);\r\n      }\r\n      return a.order - b.order;\r\n    });\r\n  };\r\n\r\n  const unifiedFormItems = createUnifiedFormItems();\r\n\r\n  // Force refresh of question groups if they're empty but we have questions with questionGroupId\r\n  React.useEffect(() => {\r\n    const hasGroupedQuestions = questions.some(\r\n      (q) => q.questionGroupId !== null && q.questionGroupId !== undefined\r\n    );\r\n    const hasNoGroups = questionGroups.length === 0;\r\n\r\n    if (hasGroupedQuestions && hasNoGroups && contextType === \"project\") {\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n    }\r\n  }, [questions, questionGroups, contextType, queryClient, groupsQueryKey]);\r\n\r\n  // Question mutations\r\n  const deleteQuestionMutation = useMutation({\r\n    mutationFn: deleteQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question deleted successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to delete question. Please try again\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n    onSettled: () => {\r\n      setShowDeleteConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  const duplicateQuestionMutation = useMutation({\r\n    mutationFn: duplicateQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question duplicated successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to duplicate question. Please try again\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n    onSettled: () => {\r\n      setShowDeleteConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  // Question group mutations\r\n  const deleteGroupMutation = useMutation({\r\n    mutationFn: deleteQuestionGroup,\r\n    onSuccess: (data, variables) => {\r\n\r\n      // Find the group that was deleted\r\n      const deletedGroup = questionGroups.find(\r\n        (g: QuestionGroup) => g.id === variables.id\r\n      );\r\n\r\n      if (deletedGroup) {\r\n        // Find questions that belonged to this group\r\n        const questionsInGroup = questions.filter(\r\n          (q) => q.questionGroupId === deletedGroup.id\r\n        );\r\n\r\n        if (questionsInGroup.length > 0) {\r\n          // Update the local state to mark these questions as ungrouped\r\n          const updatedQuestions = questions.map((q) =>\r\n            q.questionGroupId === deletedGroup.id\r\n              ? { ...q, questionGroupId: undefined }\r\n              : q\r\n          );\r\n\r\n          // Update the questions in the local state\r\n          queryClient.setQueryData(questionsQueryKey, updatedQuestions);\r\n\r\n          // Remove the deleted group from the local state\r\n          const updatedGroups = questionGroups.filter(\r\n            (g: QuestionGroup) => g.id !== variables.id\r\n          );\r\n          queryClient.setQueryData(groupsQueryKey, updatedGroups);\r\n        }\r\n      }\r\n\r\n      // Then invalidate queries to get the latest data from the server\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question group deleted successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      setShowDeleteGroupModal(false);\r\n      setIsProcessingGroup(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error deleting question group:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to delete question group. Please try again\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setIsProcessingGroup(false);\r\n    },\r\n  });\r\n\r\n  const deleteGroupWithQuestionsMutation = useMutation({\r\n    mutationFn: deleteQuestionAndGroup,\r\n    onSuccess: (data, variables) => {\r\n\r\n      // Find the group that was deleted\r\n      const deletedGroup = questionGroups.find(\r\n        (g: QuestionGroup) => g.id === variables.id\r\n      );\r\n\r\n      if (deletedGroup) {\r\n        // Remove the deleted group from the local state\r\n        const updatedGroups = questionGroups.filter(\r\n          (g: QuestionGroup) => g.id !== variables.id\r\n        );\r\n        queryClient.setQueryData(groupsQueryKey, updatedGroups);\r\n\r\n        // Remove questions that belonged to this group from the local state\r\n        const updatedQuestions = questions.filter(\r\n          (q) => q.questionGroupId !== deletedGroup.id\r\n        );\r\n        queryClient.setQueryData(questionsQueryKey, updatedQuestions);\r\n      }\r\n\r\n      // Then invalidate queries to get the latest data from the server\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question group and its questions deleted successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      setShowDeleteGroupModal(false);\r\n      setIsProcessingGroup(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error deleting question group and questions:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message:\r\n            \"Failed to delete question group and questions. Please try again\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setIsProcessingGroup(false);\r\n    },\r\n  });\r\n\r\n  // Note: We'll need a mutation for removing questions from groups in the future\r\n  // This functionality will be implemented when needed\r\n\r\n  // Question position update mutation\r\n  const updatePositionsMutation = useMutation({\r\n    mutationFn: updateQuestionPositions,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question order updated successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Failed to update question positions:\", error);\r\n      console.error(\"Error response:\", error.response?.data);\r\n      dispatch(\r\n        showNotification({\r\n          message: `Failed to update question order: ${error.response?.data?.message || error.message || \"Please try again\"}`,\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  // Question handlers\r\n  const handleDelete = () => {\r\n    if (currentQuestion && currentQuestion.id) {\r\n      deleteQuestionMutation.mutate({\r\n        contextType,\r\n        id: currentQuestion?.id,\r\n        projectId: contextId,\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (!over || active.id === over.id) {\r\n      return;\r\n    }\r\n\r\n    // Only handle drag and drop in project context\r\n    if (contextType !== \"project\") {\r\n      return;\r\n    }\r\n\r\n    // Find the active and over questions\r\n    const activeQuestion = questions.find((q) => q.id === active.id);\r\n    const overQuestion = questions.find((q) => q.id === over.id);\r\n\r\n    if (!activeQuestion || !overQuestion) {\r\n      return;\r\n    }\r\n\r\n    // Handle reordering within the same group or ungrouped questions\r\n    const isSameGroup = activeQuestion.questionGroupId === overQuestion.questionGroupId;\r\n\r\n    if (!isSameGroup) {\r\n      return;\r\n    }\r\n\r\n    // Get questions in the same context (same group or ungrouped)\r\n    const contextQuestions = questions\r\n      .filter((q) => q.questionGroupId === activeQuestion.questionGroupId)\r\n      .sort((a, b) => a.position - b.position);\r\n\r\n    const oldIndex = contextQuestions.findIndex((q) => q.id === active.id);\r\n    const newIndex = contextQuestions.findIndex((q) => q.id === over.id);\r\n\r\n    if (oldIndex === -1 || newIndex === -1) {\r\n      return;\r\n    }\r\n\r\n    // Reorder the questions array\r\n    const reorderedQuestions = arrayMove(contextQuestions, oldIndex, newIndex);\r\n\r\n    // Calculate new positions for all affected questions\r\n    const questionPositions = reorderedQuestions.map((question, index) => ({\r\n      id: Number(question.id), // Ensure it's a number\r\n      position: index + 1, // Start positions from 1\r\n    }));\r\n\r\n\r\n    // Update positions in the backend\r\n    updatePositionsMutation.mutate({\r\n      contextType,\r\n      contextId,\r\n      questionPositions,\r\n    });\r\n  };\r\n\r\n  // Group handlers\r\n  const handleEditGroup = (groupId: number) => {\r\n    setCurrentGroupId(groupId);\r\n    setShowEditGroupModal(true);\r\n  };\r\n\r\n  const handleDeleteGroup = (groupId: number) => {\r\n    setCurrentGroupId(groupId);\r\n    setShowDeleteGroupModal(true);\r\n  };\r\n\r\n  const handleConfirmDeleteGroup = () => {\r\n    if (currentGroupId) {\r\n      setIsProcessingGroup(true);\r\n      deleteGroupMutation.mutate({ id: currentGroupId });\r\n    }\r\n  };\r\n\r\n  const handleConfirmDeleteGroupWithQuestions = () => {\r\n    if (currentGroupId) {\r\n      setIsProcessingGroup(true);\r\n      deleteGroupWithQuestionsMutation.mutate({ id: currentGroupId });\r\n    }\r\n  };\r\n\r\n  const handleAddQuestionToGroup = (groupId: number) => {\r\n    setCurrentGroupId(groupId);\r\n    setShowEditGroupModal(true);\r\n  };\r\n\r\n  // Handle toggling question selection\r\n  const toggleQuestionSelection = (questionId: number) => {\r\n    setSelectedQuestionIds((prev) =>\r\n      prev.includes(questionId)\r\n        ? prev.filter((id) => id !== questionId)\r\n        : [...prev, questionId]\r\n    );\r\n  };\r\n\r\n  // Create group mutation\r\n  const createGroupMutation = useMutation({\r\n    mutationFn: createQuestionGroup,\r\n    onSuccess: (data, variables) => {\r\n\r\n      // Update local state immediately for a smoother UI experience\r\n      // This will show the grouped questions before the server refetch completes\r\n      const newGroupId = data.data?.questionGroup?.id;\r\n\r\n      if (newGroupId && variables.selectedQuestionIds) {\r\n\r\n        // Update the questionGroupId for selected questions in the local state\r\n        const updatedQuestions = questions.map((q) =>\r\n          variables.selectedQuestionIds?.includes(q.id)\r\n            ? { ...q, questionGroupId: newGroupId }\r\n            : q\r\n        );\r\n\r\n        // Update the local state with the new questions\r\n        queryClient.setQueryData(questionsQueryKey, updatedQuestions);\r\n\r\n        // Also update the groups in the local state\r\n        const newGroup = {\r\n          id: newGroupId,\r\n          title: variables.title,\r\n          order: variables.order,\r\n          projectId: variables.projectId,\r\n          createdAt: new Date().toISOString(),\r\n          updatedAt: new Date().toISOString(),\r\n          question: updatedQuestions.filter(\r\n            (q) => q.questionGroupId === newGroupId\r\n          ),\r\n        };\r\n\r\n        queryClient.setQueryData(groupsQueryKey, [...questionGroups, newGroup]);\r\n      }\r\n\r\n      // Then invalidate queries to get the latest data from the server\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question group created successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      // Clear selection after creating group\r\n      setSelectedQuestionIds([]);\r\n      setSelectionMode(false);\r\n      setIsProcessingGroup(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error creating question group:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to create question group\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setIsProcessingGroup(false);\r\n    },\r\n  });\r\n\r\n  // Handle creating a group from selected questions\r\n  const handleCreateGroupFromSelected = () => {\r\n    if (selectedQuestionIds.length === 0) {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Please select at least one question to create a group\",\r\n          type: \"warning\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    setIsProcessingGroup(true);\r\n\r\n    // Calculate the group order based on the minimum position of selected questions\r\n    // This ensures the group appears in the same position as the first selected question\r\n    const selectedQuestions = questions.filter(q => selectedQuestionIds.includes(q.id));\r\n    const minPosition = selectedQuestions.length > 0\r\n      ? Math.min(...selectedQuestions.map(q => q.position))\r\n      : questionGroups.length + 1;\r\n\r\n    // Create a new group with the selected questions\r\n    createGroupMutation.mutate({\r\n      title: \"New Group\",\r\n      order: minPosition,\r\n      projectId: contextId,\r\n      selectedQuestionIds,\r\n    });\r\n  };\r\n\r\n  // Handle inline editing of group name\r\n  const startEditingGroupName = (groupId: number, currentName: string) => {\r\n    setIsEditingGroupName(groupId);\r\n    setEditingGroupName(currentName);\r\n  };\r\n\r\n  // Handle canceling the editing of group name\r\n  const cancelEditingGroupName = () => {\r\n    setIsEditingGroupName(null);\r\n    setEditingGroupName(\"\");\r\n  };\r\n\r\n  // Update group mutation\r\n  const updateGroupMutation = useMutation({\r\n    mutationFn: updateQuestionGroup,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Group name updated successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setIsEditingGroupName(null);\r\n      setEditingGroupName(\"\");\r\n      setIsProcessingGroup(false);\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to update group name\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setIsProcessingGroup(false);\r\n    },\r\n  });\r\n\r\n  const saveGroupName = (groupId: number) => {\r\n    if (!editingGroupName.trim()) {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Group name cannot be empty\",\r\n          type: \"warning\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    setIsProcessingGroup(true);\r\n\r\n    const group = questionGroups.find((g: QuestionGroup) => g.id === groupId);\r\n    if (!group) return;\r\n\r\n    // Update the group name in the local state immediately for better UX\r\n    const updatedGroups = questionGroups.map((g: QuestionGroup) =>\r\n      g.id === groupId ? { ...g, title: editingGroupName } : g\r\n    );\r\n    queryClient.setQueryData(groupsQueryKey, updatedGroups);\r\n\r\n    // Then send the update to the server\r\n    updateGroupMutation.mutate({\r\n      id: groupId,\r\n      title: editingGroupName,\r\n      order: group.order,\r\n    });\r\n  };\r\n\r\n  // Mutation for adding a single question\r\n  const addQuestionMutation = useMutation({\r\n    mutationFn: addQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error adding question:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to add a question. Please try again.\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  // Function to handle adding questions from the library\r\n  const handleAddQuestionsFromLibrary = async (\r\n    selectedQuestions: Question[]\r\n  ) => {\r\n    if (selectedQuestions.length === 0) return;\r\n\r\n    setIsAddingQuestions(true);\r\n\r\n    try {\r\n      // Calculate the starting position for new questions\r\n      const maxPosition = questions.length > 0 ? Math.max(...questions.map(q => q.position)) : 0;\r\n\r\n      // Process each selected question sequentially\r\n      for (let i = 0; i < selectedQuestions.length; i++) {\r\n        const question = selectedQuestions[i];\r\n        const dataToSend = {\r\n          label: question.label,\r\n          isRequired: question.isRequired,\r\n          hint: question.hint || \"\",\r\n          placeholder: question.placeholder || \"\",\r\n          inputType: String(question.inputType), // Convert to string to ensure compatibility\r\n          questionOptions: question.questionOptions || [],\r\n        };\r\n\r\n        await addQuestion({\r\n          contextType,\r\n          contextId,\r\n          dataToSend,\r\n          position: maxPosition + i + 1, // Add questions at the end\r\n        });\r\n      }\r\n\r\n      // Refresh the questions list\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: `${selectedQuestions.length} question(s) added successfully`,\r\n          type: \"success\",\r\n        })\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error adding questions:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to add questions from library\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    } finally {\r\n      setIsAddingQuestions(false);\r\n    }\r\n  };\r\n  return (\r\n    <div className=\"min-h-[60vh] relative\">\r\n      {(deleteQuestionMutation.isPending ||\r\n        duplicateQuestionMutation.isPending ||\r\n        deleteGroupMutation.isPending ||\r\n        deleteGroupWithQuestionsMutation.isPending ||\r\n        updatePositionsMutation.isPending ||\r\n        isAddingQuestions) && <LoadingOverlay />}\r\n\r\n      {/* For adding new questions */}\r\n      <AddQuestionModal\r\n        showModal={showAddQuestionModal}\r\n        setShowModal={setShowAddQuestionModal}\r\n        contextType={contextType}\r\n        contextId={contextId}\r\n        position={questions.length > 0 ? Math.max(...questions.map(q => q.position)) + 1 : 1}\r\n      />\r\n\r\n      {/* for editing existing questions, it requires a question object as props */}\r\n      {currentQuestion && currentQuestion.inputType !== \"table\" && (\r\n        <EditQuestionModal\r\n          showModal={showEditQuestionModal}\r\n          setShowModal={setShowEditQuestionModal}\r\n          contextType={contextType}\r\n          question={currentQuestion}\r\n          contextId={contextId}\r\n        />\r\n      )}\r\n\r\n      {/* for editing table questions specifically */}\r\n      {currentQuestion && currentQuestion.inputType === \"table\" && (\r\n        <EditTableQuestionModal\r\n          showModal={showEditTableQuestionModal}\r\n          setShowModal={setShowEditTableQuestionModal}\r\n          contextType={contextType}\r\n          question={currentQuestion}\r\n          contextId={contextId}\r\n        />\r\n      )}\r\n\r\n      {/* For adding/editing question groups */}\r\n      <QuestionGroupModal\r\n        showModal={showAddGroupModal}\r\n        setShowModal={setShowAddGroupModal}\r\n        contextType={contextType}\r\n        contextId={contextId}\r\n        questions={questions}\r\n        questionGroups={questionGroups}\r\n      />\r\n\r\n      {/* For editing existing question groups */}\r\n      {currentGroupId && (\r\n        <QuestionGroupModal\r\n          showModal={showEditGroupModal}\r\n          setShowModal={setShowEditGroupModal}\r\n          contextType={contextType}\r\n          contextId={contextId}\r\n          existingGroup={questionGroups.find(\r\n            (g: QuestionGroup) => g.id === currentGroupId\r\n          )}\r\n          questions={questions}\r\n          questionGroups={questionGroups}\r\n        />\r\n      )}\r\n\r\n      {/* Delete question confirmation modal */}\r\n      <ConfirmationModal\r\n        showModal={showDeleteConfirmationModal}\r\n        onClose={() => setShowDeleteConfirmationModal(false)}\r\n        onConfirm={handleDelete}\r\n        title=\"Delete Question\"\r\n        description=\"Are you sure you want to delete this question? This action cannot be undone.\"\r\n        confirmButtonText=\"Delete\"\r\n        cancelButtonText=\"Cancel\"\r\n        confirmButtonClass=\"btn-danger\"\r\n      />\r\n\r\n      {/* Delete group confirmation modal */}\r\n      <DeleteQuestionGroupModal\r\n        showModal={showDeleteGroupModal}\r\n        setShowModal={setShowDeleteGroupModal}\r\n        onConfirmDelete={handleConfirmDeleteGroup}\r\n        onConfirmDeleteWithQuestions={handleConfirmDeleteGroupWithQuestions}\r\n        isDeleting={\r\n          deleteGroupMutation.isPending ||\r\n          deleteGroupWithQuestionsMutation.isPending\r\n        }\r\n      />\r\n\r\n      {/* Library questions sidebar */}\r\n      <LibraryQuestionsSidebar\r\n        isOpen={showLibrarySidebar}\r\n        onClose={() => setShowLibrarySidebar(false)}\r\n        onAddQuestions={handleAddQuestionsFromLibrary}\r\n      />\r\n\r\n      {/* Header with actions */}\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <div className=\"flex items-center\">\r\n          <h1 className=\"heading-text mr-4\">Form Builder</h1>\r\n\r\n          {/* Create Group button */}\r\n          {selectedQuestionIds.length > 0 ? (\r\n            <button\r\n              className=\"btn-primary flex items-center gap-2\"\r\n              onClick={handleCreateGroupFromSelected}\r\n              disabled={isProcessingGroup}\r\n            >\r\n              <FolderPlus size={16} />\r\n              Create Group ({selectedQuestionIds.length})\r\n            </button>\r\n          ) : (\r\n            <div className=\"flex gap-2\">\r\n              <button\r\n                className=\"btn-outline flex items-center gap-2\"\r\n                onClick={() => {\r\n                  // Toggle selection mode\r\n                  if (!selectionMode) {\r\n                    setSelectionMode(true);\r\n                  } else {\r\n                    setSelectionMode(false);\r\n                    setSelectedQuestionIds([]);\r\n                  }\r\n                }}\r\n              >\r\n                <FolderPlus size={16} />\r\n                {selectionMode ? \"Cancel Selection\" : \"Select Questions\"}\r\n              </button>\r\n\r\n              <button\r\n                className=\"btn-outline flex items-center gap-2\"\r\n                onClick={() => setShowAddGroupModal(true)}\r\n              >\r\n                <FolderPlus size={16} />\r\n                Create Empty Group\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            className=\"btn-outline p-2\"\r\n            onClick={() => setIsPreviewMode(true)}\r\n            title=\"Preview Form\"\r\n          >\r\n            <Eye size={16} />\r\n          </button>\r\n          <button\r\n            className=\"btn-outline p-2\"\r\n            onClick={() => setShowLibrarySidebar(true)}\r\n            title=\"Question Library\"\r\n          >\r\n            <BookOpen size={16} />\r\n          </button>\r\n          {/* <button\r\n            className=\"btn-outline p-2\"\r\n            onClick={() => {}}\r\n            title=\"Export Form\"\r\n          >\r\n            <FileDown size={16} />\r\n          </button>\r\n          <button\r\n            className=\"btn-outline p-2\"\r\n            onClick={() => {}}\r\n            title=\"Import Form\"\r\n          >\r\n            <FileUp size={16} />\r\n          </button>\r\n          <button className=\"btn-outline p-2\" title=\"Settings\">\r\n            <Settings size={16} />\r\n          </button> */}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main content area */}\r\n      <div className=\"section shadow-none border border-neutral-400\">\r\n        <DndContext\r\n          sensors={sensors}\r\n          collisionDetection={closestCenter}\r\n          onDragEnd={handleDragEnd}\r\n        >\r\n          <SortableContext\r\n            items={questions.map((question) => question.id)}\r\n            strategy={verticalListSortingStrategy}\r\n          >\r\n            <div className=\"space-y-4\">\r\n              {questions.length === 0 ? (\r\n                // No questions at all\r\n                <div className=\"text-center py-16 px-4\">\r\n                  <h3 className=\"heading-text text-muted-foreground\">\r\n                    No questions yet\r\n                  </h3>\r\n                  <p className=\"mt-1 text-sm sub-text\">\r\n                    Get started by adding your first question\r\n                  </p>\r\n                  <div className=\"p-4 flex justify-center\">\r\n                    <button\r\n                      onClick={() => setShowAddQuestionModal(true)}\r\n                      className=\"btn-primary\"\r\n                      disabled={!editFormPermission}\r\n                    >\r\n                      <PlusCircle size={16} />\r\n                      Add First Question\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                // Render unified form items (groups and individual questions) in order\r\n                unifiedFormItems.map((item) => {\r\n                  if (item.type === 'group') {\r\n                    const group = item.data as QuestionGroup;\r\n                    const groupQuestions = questions\r\n                      .filter((q) => q.questionGroupId === group.id)\r\n                      .sort((a, b) => a.position - b.position);\r\n\r\n                    return (\r\n                      <div key={`group-${group.id}`} className=\"mb-4\">\r\n                        <QuestionGroupItem\r\n                          id={group.id}\r\n                          title={group.title}\r\n                          questions={groupQuestions}\r\n                          onEditGroup={handleEditGroup}\r\n                          onDeleteGroup={handleDeleteGroup}\r\n                          onAddQuestionToGroup={handleAddQuestionToGroup}\r\n                          onEditQuestion={(question) => {\r\n                            setCurrentQuestion(question);\r\n                            if (question.inputType === \"table\") {\r\n                              setShowEditTableQuestionModal(true);\r\n                            } else {\r\n                              setShowEditQuestionModal(true);\r\n                            }\r\n                          }}\r\n                          onDeleteQuestion={(question) => {\r\n                            setCurrentQuestion(question);\r\n                            setShowDeleteConfirmationModal(true);\r\n                          }}\r\n                          onDuplicateQuestion={(question) => {\r\n                            setCurrentQuestion(question);\r\n                            duplicateQuestionMutation.mutate({\r\n                              id: question.id,\r\n                              contextType,\r\n                              contextId,\r\n                            });\r\n                          }}\r\n                          onReorderQuestions={(questionPositions) => {\r\n                            updatePositionsMutation.mutate({\r\n                              contextType,\r\n                              contextId,\r\n                              questionPositions,\r\n                            });\r\n                          }}\r\n                          isEditing={isEditingGroupName === group.id}\r\n                          onStartEditing={startEditingGroupName}\r\n                          onSaveGroupName={saveGroupName}\r\n                          onCancelEditing={cancelEditingGroupName}\r\n                          editingName={editingGroupName}\r\n                          onEditingNameChange={setEditingGroupName}\r\n                          selectionMode={selectionMode}\r\n                        />\r\n                      </div>\r\n                    );\r\n                  } else {\r\n                    const question = item.data as Question;\r\n                    return (\r\n                      <div key={`question-${question.id}`} className=\"mb-4\">\r\n                        <QuestionItem\r\n                          question={question}\r\n                          onEdit={() => {\r\n                            setCurrentQuestion(question);\r\n                            if (question.inputType === \"table\") {\r\n                              setShowEditTableQuestionModal(true);\r\n                            } else {\r\n                              setShowEditQuestionModal(true);\r\n                            }\r\n                          }}\r\n                          onDelete={() => {\r\n                            setCurrentQuestion(question);\r\n                            setShowDeleteConfirmationModal(true);\r\n                          }}\r\n                          onDuplicate={() => {\r\n                            setCurrentQuestion(question);\r\n                            duplicateQuestionMutation.mutate({\r\n                              id: question.id,\r\n                              contextType,\r\n                              contextId,\r\n                            });\r\n                          }}\r\n                          selectionMode={selectionMode}\r\n                          isSelected={selectedQuestionIds.includes(question.id)}\r\n                          onToggleSelect={() =>\r\n                            toggleQuestionSelection(question.id)\r\n                          }\r\n                        />\r\n                      </div>\r\n                    );\r\n                  }\r\n                })\r\n              )}\r\n            </div>\r\n          </SortableContext>\r\n        </DndContext>\r\n      </div>\r\n\r\n      {/* Bottom action bar */}\r\n      {questions.length > 0 && (\r\n        <div className=\"sticky bottom-0 p-4 flex justify-center\">\r\n          <button\r\n            className={`btn-primary  max-w-md flex items-center justify-center gap-2 ${\r\n              !editFormPermission && \"text-gray-400 cursor-not-allowed\"\r\n            }`}\r\n            onClick={() => setShowAddQuestionModal(true)}\r\n            disabled={!editFormPermission}\r\n          >\r\n            <PlusCircle size={16} />\r\n            Add Question\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { FormBuilder };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAMA;AAQA;AACA;AAEA;AACA;;;AAtDA;;;;;;;;;;;;;;;;;;;;AAwDA,MAAM,cAAc,CAAC,EACnB,gBAAgB,EAChB,SAAS,EACT,WAAW,EACX,SAAS,EACT,WAAW,EAOZ;;IACC,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa,EAAE;QACvB,sBAAsB;YACpB,UAAU;QACZ;IACF,IACA,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,iBAAc;IAG1B,MAAM,qBAAqB,YAAY,aAAa,IAAI,YAAY,QAAQ;IAE5E,wBAAwB;IACxB,MAAM,CAAC,sBAAsB,wBAAwB,GACnD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,uBAAuB,yBAAyB,GACrD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,4BAA4B,8BAA8B,GAC/D,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,6BAA6B,+BAA+B,GACjE,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEpB,8BAA8B;IAC9B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,sBAAsB,wBAAwB,GACnD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,2BAA2B;IAC3B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzD;IAEF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAE5D,oDAAoD;IACpD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,oBACJ,gBAAgB,YACZ;QAAC;QAAa;KAAU,GACxB,gBAAgB,aAChB;QAAC;QAAqB;KAAU,GAChC;QAAC;QAA0B;KAAU;IAE3C,MAAM,iBAAiB;QAAC;QAAkB;KAAU;IAEpD,wBAAwB;IACxB,MAAM,EAAE,MAAM,iBAAiB,EAAE,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACzE,UAAU;QACV,OAAO;oCAAE,IAAM,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAE,WAAW;gBAAU;;QAC1D,SAAS,gBAAgB;IAC3B;IAEA,2EAA2E;IAC3E,MAAM,mBAAmB,eAAe,MAAM,CAC5C,CAAC,KAAiC;QAChC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,UACb,MAAM,CAAC,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE,EAC5C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QACzC,OAAO;IACT,GACA,CAAC;IAGH,0BAA0B;IAC1B,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,eAAe;IAErE,6FAA6F;IAC7F,MAAM,yBAAyB;QAC7B,MAAM,QAKD,EAAE;QAEP,2EAA2E;QAC3E,IAAI,gBAAgB,WAAW;YAC7B,eAAe,OAAO,CAAC,CAAC;gBACtB,kEAAkE;gBAClE,oFAAoF;gBACpF,MAAM,iBAAiB,UACpB,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,KAAK,MAAM,EAAE,EAC1C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;gBACzC,MAAM,sBAAsB,eAAe,MAAM,GAAG,IAChD,KAAK,GAAG,IAAI,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KAC9C,MAAM,KAAK;gBAEf,MAAM,IAAI,CAAC;oBACT,MAAM;oBACN,MAAM;oBACN,OAAO;oBACP,kBAAkB;gBACpB;YACF;QACF;QAEA,uEAAuE;QACvE,MAAM,iBAAiB,gBAAgB,YAAY,qBAAqB;QACxE,eAAe,OAAO,CAAC,CAAC;YACtB,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,MAAM;gBACN,OAAO,SAAS,QAAQ;gBACxB,kBAAkB,SAAS,QAAQ;YACrC;QACF;QAEA,sFAAsF;QACtF,mDAAmD;QACnD,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG;YACpB,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;gBACvB,0EAA0E;gBAC1E,gEAAgE;gBAChE,OAAO,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK;YACzE;YACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;QAC1B;IACF;IAEA,MAAM,mBAAmB;IAEzB,+FAA+F;IAC/F,6JAAA,CAAA,UAAK,CAAC,SAAS;iCAAC;YACd,MAAM,sBAAsB,UAAU,IAAI;6DACxC,CAAC,IAAM,EAAE,eAAe,KAAK,QAAQ,EAAE,eAAe,KAAK;;YAE7D,MAAM,cAAc,eAAe,MAAM,KAAK;YAE9C,IAAI,uBAAuB,eAAe,gBAAgB,WAAW;gBACnE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;YAC3D;QACF;gCAAG;QAAC;QAAW;QAAgB;QAAa;QAAa;KAAe;IAExE,qBAAqB;IACrB,MAAM,yBAAyB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACzC,YAAY,gIAAA,CAAA,iBAAc;QAC1B,SAAS;+DAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;QACA,OAAO;+DAAE;gBACP,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;QACA,SAAS;+DAAE;gBACT,+BAA+B;YACjC;;IACF;IAEA,MAAM,4BAA4B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC5C,YAAY,gIAAA,CAAA,oBAAiB;QAC7B,SAAS;kEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;QACA,OAAO;kEAAE;gBACP,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;QACA,SAAS;kEAAE;gBACT,+BAA+B;YACjC;;IACF;IAEA,2BAA2B;IAC3B,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;4DAAE,CAAC,MAAM;gBAEhB,kCAAkC;gBAClC,MAAM,eAAe,eAAe,IAAI;iFACtC,CAAC,IAAqB,EAAE,EAAE,KAAK,UAAU,EAAE;;gBAG7C,IAAI,cAAc;oBAChB,6CAA6C;oBAC7C,MAAM,mBAAmB,UAAU,MAAM;yFACvC,CAAC,IAAM,EAAE,eAAe,KAAK,aAAa,EAAE;;oBAG9C,IAAI,iBAAiB,MAAM,GAAG,GAAG;wBAC/B,8DAA8D;wBAC9D,MAAM,mBAAmB,UAAU,GAAG;6FAAC,CAAC,IACtC,EAAE,eAAe,KAAK,aAAa,EAAE,GACjC;oCAAE,GAAG,CAAC;oCAAE,iBAAiB;gCAAU,IACnC;;wBAGN,0CAA0C;wBAC1C,YAAY,YAAY,CAAC,mBAAmB;wBAE5C,gDAAgD;wBAChD,MAAM,gBAAgB,eAAe,MAAM;0FACzC,CAAC,IAAqB,EAAE,EAAE,KAAK,UAAU,EAAE;;wBAE7C,YAAY,YAAY,CAAC,gBAAgB;oBAC3C;gBACF;gBAEA,iEAAiE;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAE5D,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAGF,wBAAwB;gBACxB,qBAAqB;YACvB;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,qBAAqB;YACvB;;IACF;IAEA,MAAM,mCAAmC,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACnD,YAAY,mIAAA,CAAA,yBAAsB;QAClC,SAAS;yEAAE,CAAC,MAAM;gBAEhB,kCAAkC;gBAClC,MAAM,eAAe,eAAe,IAAI;8FACtC,CAAC,IAAqB,EAAE,EAAE,KAAK,UAAU,EAAE;;gBAG7C,IAAI,cAAc;oBAChB,gDAAgD;oBAChD,MAAM,gBAAgB,eAAe,MAAM;mGACzC,CAAC,IAAqB,EAAE,EAAE,KAAK,UAAU,EAAE;;oBAE7C,YAAY,YAAY,CAAC,gBAAgB;oBAEzC,oEAAoE;oBACpE,MAAM,mBAAmB,UAAU,MAAM;sGACvC,CAAC,IAAM,EAAE,eAAe,KAAK,aAAa,EAAE;;oBAE9C,YAAY,YAAY,CAAC,mBAAmB;gBAC9C;gBAEA,iEAAiE;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAE5D,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAGF,wBAAwB;gBACxB,qBAAqB;YACvB;;QACA,OAAO;yEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,gDAAgD;gBAC9D,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SACE;oBACF,MAAM;gBACR;gBAEF,qBAAqB;YACvB;;IACF;IAEA,+EAA+E;IAC/E,qDAAqD;IAErD,oCAAoC;IACpC,MAAM,0BAA0B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,YAAY,gIAAA,CAAA,0BAAuB;QACnC,SAAS;gEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;QACA,OAAO;gEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,QAAQ,KAAK,CAAC,mBAAmB,MAAM,QAAQ,EAAE;gBACjD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,CAAC,iCAAiC,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI,oBAAoB;oBACnH,MAAM;gBACR;YAEJ;;IACF;IAEA,oBAAoB;IACpB,MAAM,eAAe;QACnB,IAAI,mBAAmB,gBAAgB,EAAE,EAAE;YACzC,uBAAuB,MAAM,CAAC;gBAC5B;gBACA,IAAI,iBAAiB;gBACrB,WAAW;YACb;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,CAAC,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YAClC;QACF;QAEA,+CAA+C;QAC/C,IAAI,gBAAgB,WAAW;YAC7B;QACF;QAEA,qCAAqC;QACrC,MAAM,iBAAiB,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE;QAC/D,MAAM,eAAe,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;QAE3D,IAAI,CAAC,kBAAkB,CAAC,cAAc;YACpC;QACF;QAEA,iEAAiE;QACjE,MAAM,cAAc,eAAe,eAAe,KAAK,aAAa,eAAe;QAEnF,IAAI,CAAC,aAAa;YAChB;QACF;QAEA,8DAA8D;QAC9D,MAAM,mBAAmB,UACtB,MAAM,CAAC,CAAC,IAAM,EAAE,eAAe,KAAK,eAAe,eAAe,EAClE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAEzC,MAAM,WAAW,iBAAiB,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE;QACrE,MAAM,WAAW,iBAAiB,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;QAEnE,IAAI,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;YACtC;QACF;QAEA,8BAA8B;QAC9B,MAAM,qBAAqB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,UAAU;QAEjE,qDAAqD;QACrD,MAAM,oBAAoB,mBAAmB,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;gBACrE,IAAI,OAAO,SAAS,EAAE;gBACtB,UAAU,QAAQ;YACpB,CAAC;QAGD,kCAAkC;QAClC,wBAAwB,MAAM,CAAC;YAC7B;YACA;YACA;QACF;IACF;IAEA,iBAAiB;IACjB,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;QAClB,sBAAsB;IACxB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,wBAAwB;IAC1B;IAEA,MAAM,2BAA2B;QAC/B,IAAI,gBAAgB;YAClB,qBAAqB;YACrB,oBAAoB,MAAM,CAAC;gBAAE,IAAI;YAAe;QAClD;IACF;IAEA,MAAM,wCAAwC;QAC5C,IAAI,gBAAgB;YAClB,qBAAqB;YACrB,iCAAiC,MAAM,CAAC;gBAAE,IAAI;YAAe;QAC/D;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,kBAAkB;QAClB,sBAAsB;IACxB;IAEA,qCAAqC;IACrC,MAAM,0BAA0B,CAAC;QAC/B,uBAAuB,CAAC,OACtB,KAAK,QAAQ,CAAC,cACV,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO,cAC3B;mBAAI;gBAAM;aAAW;IAE7B;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;4DAAE,CAAC,MAAM;gBAEhB,8DAA8D;gBAC9D,2EAA2E;gBAC3E,MAAM,aAAa,KAAK,IAAI,EAAE,eAAe;gBAE7C,IAAI,cAAc,UAAU,mBAAmB,EAAE;oBAE/C,uEAAuE;oBACvE,MAAM,mBAAmB,UAAU,GAAG;yFAAC,CAAC,IACtC,UAAU,mBAAmB,EAAE,SAAS,EAAE,EAAE,IACxC;gCAAE,GAAG,CAAC;gCAAE,iBAAiB;4BAAW,IACpC;;oBAGN,gDAAgD;oBAChD,YAAY,YAAY,CAAC,mBAAmB;oBAE5C,4CAA4C;oBAC5C,MAAM,WAAW;wBACf,IAAI;wBACJ,OAAO,UAAU,KAAK;wBACtB,OAAO,UAAU,KAAK;wBACtB,WAAW,UAAU,SAAS;wBAC9B,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;wBACjC,UAAU,iBAAiB,MAAM;4EAC/B,CAAC,IAAM,EAAE,eAAe,KAAK;;oBAEjC;oBAEA,YAAY,YAAY,CAAC,gBAAgB;2BAAI;wBAAgB;qBAAS;gBACxE;gBAEA,iEAAiE;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAE5D,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAGF,uCAAuC;gBACvC,uBAAuB,EAAE;gBACzB,iBAAiB;gBACjB,qBAAqB;YACvB;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,qBAAqB;YACvB;;IACF;IAEA,kDAAkD;IAClD,MAAM,gCAAgC;QACpC,IAAI,oBAAoB,MAAM,KAAK,GAAG;YACpC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF;QACF;QAEA,qBAAqB;QAErB,gFAAgF;QAChF,qFAAqF;QACrF,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,IAAK,oBAAoB,QAAQ,CAAC,EAAE,EAAE;QACjF,MAAM,cAAc,kBAAkB,MAAM,GAAG,IAC3C,KAAK,GAAG,IAAI,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KACjD,eAAe,MAAM,GAAG;QAE5B,iDAAiD;QACjD,oBAAoB,MAAM,CAAC;YACzB,OAAO;YACP,OAAO;YACP,WAAW;YACX;QACF;IACF;IAEA,sCAAsC;IACtC,MAAM,wBAAwB,CAAC,SAAiB;QAC9C,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,6CAA6C;IAC7C,MAAM,yBAAyB;QAC7B,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;4DAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,sBAAsB;gBACtB,oBAAoB;gBACpB,qBAAqB;YACvB;;QACA,OAAO;4DAAE;gBACP,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,qBAAqB;YACvB;;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,iBAAiB,IAAI,IAAI;YAC5B,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF;QACF;QAEA,qBAAqB;QAErB,MAAM,QAAQ,eAAe,IAAI,CAAC,CAAC,IAAqB,EAAE,EAAE,KAAK;QACjE,IAAI,CAAC,OAAO;QAEZ,qEAAqE;QACrE,MAAM,gBAAgB,eAAe,GAAG,CAAC,CAAC,IACxC,EAAE,EAAE,KAAK,UAAU;gBAAE,GAAG,CAAC;gBAAE,OAAO;YAAiB,IAAI;QAEzD,YAAY,YAAY,CAAC,gBAAgB;QAEzC,qCAAqC;QACrC,oBAAoB,MAAM,CAAC;YACzB,IAAI;YACJ,OAAO;YACP,OAAO,MAAM,KAAK;QACpB;IACF;IAEA,wCAAwC;IACxC,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,gIAAA,CAAA,cAAW;QACvB,SAAS;4DAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;YAC9D;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;IACF;IAEA,uDAAuD;IACvD,MAAM,gCAAgC,OACpC;QAEA,IAAI,kBAAkB,MAAM,KAAK,GAAG;QAEpC,qBAAqB;QAErB,IAAI;YACF,oDAAoD;YACpD,MAAM,cAAc,UAAU,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YAEzF,8CAA8C;YAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,MAAM,EAAE,IAAK;gBACjD,MAAM,WAAW,iBAAiB,CAAC,EAAE;gBACrC,MAAM,aAAa;oBACjB,OAAO,SAAS,KAAK;oBACrB,YAAY,SAAS,UAAU;oBAC/B,MAAM,SAAS,IAAI,IAAI;oBACvB,aAAa,SAAS,WAAW,IAAI;oBACrC,WAAW,OAAO,SAAS,SAAS;oBACpC,iBAAiB,SAAS,eAAe,IAAI,EAAE;gBACjD;gBAEA,MAAM,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD,EAAE;oBAChB;oBACA;oBACA;oBACA,UAAU,cAAc,IAAI;gBAC9B;YACF;YAEA,6BAA6B;YAC7B,YAAY,iBAAiB,CAAC;gBAAE,UAAU;YAAkB;YAE5D,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS,GAAG,kBAAkB,MAAM,CAAC,+BAA+B,CAAC;gBACrE,MAAM;YACR;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;QAEJ,SAAU;YACR,qBAAqB;QACvB;IACF;IACA,qBACE,6LAAC;QAAI,WAAU;;YACZ,CAAC,uBAAuB,SAAS,IAChC,0BAA0B,SAAS,IACnC,oBAAoB,SAAS,IAC7B,iCAAiC,SAAS,IAC1C,wBAAwB,SAAS,IACjC,iBAAiB,mBAAK,6LAAC,2IAAA,CAAA,iBAAc;;;;;0BAGvC,6LAAC,4IAAA,CAAA,mBAAgB;gBACf,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,WAAW;gBACX,UAAU,UAAU,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,IAAI;;;;;;YAIpF,mBAAmB,gBAAgB,SAAS,KAAK,yBAChD,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,UAAU;gBACV,WAAW;;;;;;YAKd,mBAAmB,gBAAgB,SAAS,KAAK,yBAChD,6LAAC,kJAAA,CAAA,yBAAsB;gBACrB,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,UAAU;gBACV,WAAW;;;;;;0BAKf,6LAAC,8IAAA,CAAA,qBAAkB;gBACjB,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,gBAAgB;;;;;;YAIjB,gCACC,6LAAC,8IAAA,CAAA,qBAAkB;gBACjB,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,WAAW;gBACX,eAAe,eAAe,IAAI,CAChC,CAAC,IAAqB,EAAE,EAAE,KAAK;gBAEjC,WAAW;gBACX,gBAAgB;;;;;;0BAKpB,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,SAAS,IAAM,+BAA+B;gBAC9C,WAAW;gBACX,OAAM;gBACN,aAAY;gBACZ,mBAAkB;gBAClB,kBAAiB;gBACjB,oBAAmB;;;;;;0BAIrB,6LAAC,oJAAA,CAAA,2BAAwB;gBACvB,WAAW;gBACX,cAAc;gBACd,iBAAiB;gBACjB,8BAA8B;gBAC9B,YACE,oBAAoB,SAAS,IAC7B,iCAAiC,SAAS;;;;;;0BAK9C,6LAAC,4JAAA,CAAA,UAAuB;gBACtB,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,gBAAgB;;;;;;0BAIlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoB;;;;;;4BAGjC,oBAAoB,MAAM,GAAG,kBAC5B,6LAAC;gCACC,WAAU;gCACV,SAAS;gCACT,UAAU;;kDAEV,6LAAC,qNAAA,CAAA,aAAU;wCAAC,MAAM;;;;;;oCAAM;oCACT,oBAAoB,MAAM;oCAAC;;;;;;qDAG5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,SAAS;4CACP,wBAAwB;4CACxB,IAAI,CAAC,eAAe;gDAClB,iBAAiB;4CACnB,OAAO;gDACL,iBAAiB;gDACjB,uBAAuB,EAAE;4CAC3B;wCACF;;0DAEA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;4CACjB,gBAAgB,qBAAqB;;;;;;;kDAGxC,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB;;0DAEpC,6LAAC,qNAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;;kCAOhC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,iBAAiB;gCAChC,OAAM;0CAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,MAAM;;;;;;;;;;;0CAEb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,sBAAsB;gCACrC,OAAM;0CAEN,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAuBtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8JAAA,CAAA,aAAU;oBACT,SAAS;oBACT,oBAAoB,8JAAA,CAAA,gBAAa;oBACjC,WAAW;8BAEX,cAAA,6LAAC,sKAAA,CAAA,kBAAe;wBACd,OAAO,UAAU,GAAG,CAAC,CAAC,WAAa,SAAS,EAAE;wBAC9C,UAAU,sKAAA,CAAA,8BAA2B;kCAErC,cAAA,6LAAC;4BAAI,WAAU;sCACZ,UAAU,MAAM,KAAK,IACpB,sBAAsB;0CACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDAGrC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,wBAAwB;4CACvC,WAAU;4CACV,UAAU,CAAC;;8DAEX,6LAAC,qNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;;;;uCAM9B,uEAAuE;4BACvE,iBAAiB,GAAG,CAAC,CAAC;gCACpB,IAAI,KAAK,IAAI,KAAK,SAAS;oCACzB,MAAM,QAAQ,KAAK,IAAI;oCACvB,MAAM,iBAAiB,UACpB,MAAM,CAAC,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE,EAC5C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;oCAEzC,qBACE,6LAAC;wCAA8B,WAAU;kDACvC,cAAA,6LAAC,sJAAA,CAAA,oBAAiB;4CAChB,IAAI,MAAM,EAAE;4CACZ,OAAO,MAAM,KAAK;4CAClB,WAAW;4CACX,aAAa;4CACb,eAAe;4CACf,sBAAsB;4CACtB,gBAAgB,CAAC;gDACf,mBAAmB;gDACnB,IAAI,SAAS,SAAS,KAAK,SAAS;oDAClC,8BAA8B;gDAChC,OAAO;oDACL,yBAAyB;gDAC3B;4CACF;4CACA,kBAAkB,CAAC;gDACjB,mBAAmB;gDACnB,+BAA+B;4CACjC;4CACA,qBAAqB,CAAC;gDACpB,mBAAmB;gDACnB,0BAA0B,MAAM,CAAC;oDAC/B,IAAI,SAAS,EAAE;oDACf;oDACA;gDACF;4CACF;4CACA,oBAAoB,CAAC;gDACnB,wBAAwB,MAAM,CAAC;oDAC7B;oDACA;oDACA;gDACF;4CACF;4CACA,WAAW,uBAAuB,MAAM,EAAE;4CAC1C,gBAAgB;4CAChB,iBAAiB;4CACjB,iBAAiB;4CACjB,aAAa;4CACb,qBAAqB;4CACrB,eAAe;;;;;;uCAzCT,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;;;;;gCA6CjC,OAAO;oCACL,MAAM,WAAW,KAAK,IAAI;oCAC1B,qBACE,6LAAC;wCAAoC,WAAU;kDAC7C,cAAA,6LAAC,iJAAA,CAAA,eAAY;4CACX,UAAU;4CACV,QAAQ;gDACN,mBAAmB;gDACnB,IAAI,SAAS,SAAS,KAAK,SAAS;oDAClC,8BAA8B;gDAChC,OAAO;oDACL,yBAAyB;gDAC3B;4CACF;4CACA,UAAU;gDACR,mBAAmB;gDACnB,+BAA+B;4CACjC;4CACA,aAAa;gDACX,mBAAmB;gDACnB,0BAA0B,MAAM,CAAC;oDAC/B,IAAI,SAAS,EAAE;oDACf;oDACA;gDACF;4CACF;4CACA,eAAe;4CACf,YAAY,oBAAoB,QAAQ,CAAC,SAAS,EAAE;4CACpD,gBAAgB,IACd,wBAAwB,SAAS,EAAE;;;;;;uCA1B/B,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;;;;;gCA+BvC;4BACF;;;;;;;;;;;;;;;;;;;;;YAQT,UAAU,MAAM,GAAG,mBAClB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,6DAA6D,EACvE,CAAC,sBAAsB,oCACvB;oBACF,SAAS,IAAM,wBAAwB;oBACvC,UAAU,CAAC;;sCAEX,6LAAC,qNAAA,CAAA,aAAU;4BAAC,MAAM;;;;;;wBAAM;;;;;;;;;;;;;;;;;;AAOpC;GA3+BM;;QAaY,8JAAA,CAAA,aAAU;QA0CT,4JAAA,CAAA,cAAW;QACR,yLAAA,CAAA,iBAAc;QAYgC,8KAAA,CAAA,WAAQ;QAwF3C,iLAAA,CAAA,cAAW;QAwBR,iLAAA,CAAA,cAAW;QAyBjB,iLAAA,CAAA,cAAW;QA4DE,iLAAA,CAAA,cAAW;QAsDpB,iLAAA,CAAA,cAAW;QAmIf,iLAAA,CAAA,cAAW;QA2GX,iLAAA,CAAA,cAAW;QAwDX,iLAAA,CAAA,cAAW;;;KArmBnC", "debugId": null}}, {"offset": {"line": 9010, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/kobo/data-analysis-tool-frontend/app/%28main%29/project/%5BhashedId%5D/form-builder/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FormPreview } from \"@/components/form-preview\";\r\nimport { useState } from \"react\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { fetchQuestions } from \"@/lib/api/form-builder\";\r\nimport { fetchQuestionGroups } from \"@/lib/api/question-groups\";\r\nimport Spinner from \"@/components/general/Spinner\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { FormBuilder } from \"@/components/form-builder/FormBuilder\";\r\nimport { useProjectPermissions } from \"@/hooks/useProjectPermissions\";\r\nimport { fetchProjectById } from \"@/lib/api/projects\";\r\nimport { Project } from \"@/types\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\n\r\nexport default function FormBuilderPage() {\r\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\r\n\r\n  const { hashedId } = useParams();\r\n  const { user } = useAuth();\r\n  const hashedIdString = hashedId as string;\r\n\r\n  const projectId = Number(decode(hashedIdString));\r\n\r\n  const {\r\n    data: questionsData,\r\n    isLoading: questionsLoading,\r\n    isError: questionsError,\r\n    error: questionsErrorMessage,\r\n  } = useQuery<Question[]>({\r\n    queryKey: [\"questions\", projectId],\r\n    queryFn: () => fetchQuestions({ projectId: projectId! }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  // Fetch question groups for the project\r\n  const { data: questionGroups = [] } = useQuery<QuestionGroup[]>({\r\n    queryKey: [\"questionGroups\", projectId],\r\n    queryFn: () => fetchQuestionGroups({ projectId: projectId! }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  const {\r\n    data: projectData,\r\n    isLoading: projectLoading,\r\n    isError: projectError,\r\n  } = useQuery<Project>({\r\n    queryKey: [\"projects\", user?.id, projectId],\r\n    queryFn: () => fetchProjectById({ projectId: projectId! }),\r\n    enabled: !!projectId && !!user?.id,\r\n  });\r\n\r\n  const permissions = useProjectPermissions({\r\n    projectData,\r\n    user,\r\n  });\r\n\r\n  if (!hashedId || projectId === null) {\r\n    return (\r\n      <div className=\"error-message\">\r\n        <h1 className=\"text-red-500\">Error: Invalid Project ID (hashedId).</h1>\r\n        <p className=\"text-neutral-700\">\r\n          Please make sure the URL contains a valid project identifier.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (questionsLoading || !questionsData) {\r\n    return <Spinner />;\r\n  }\r\n  if (questionsError) {\r\n    return (\r\n      <p className=\"text-sm text-red-500\">\r\n        Error loading form information. Please refresh the page.\r\n      </p>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      {isPreviewMode ? (\r\n        <FormPreview\r\n          questions={questionsData}\r\n          questionGroups={questionGroups}\r\n          contextType=\"project\"\r\n          onClose={() => setIsPreviewMode(false)}\r\n          hashedId={hashedIdString} // Pass hashedId\r\n        />\r\n      ) : (\r\n        <FormBuilder\r\n          setIsPreviewMode={setIsPreviewMode}\r\n          questions={questionsData}\r\n          contextType=\"project\"\r\n          contextId={projectId}\r\n          permissions={permissions}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAfA;;;;;;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,iBAAiB;IAEvB,MAAM,YAAY,OAAO,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,MAAM,EACJ,MAAM,aAAa,EACnB,WAAW,gBAAgB,EAC3B,SAAS,cAAc,EACvB,OAAO,qBAAqB,EAC7B,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UAAU;YAAC;YAAa;SAAU;QAClC,OAAO;wCAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QACtD,SAAS,CAAC,CAAC;IACb;IAEA,wCAAwC;IACxC,MAAM,EAAE,MAAM,iBAAiB,EAAE,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAmB;QAC9D,UAAU;YAAC;YAAkB;SAAU;QACvC,OAAO;wCAAE,IAAM,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QAC3D,SAAS,CAAC,CAAC;IACb;IAEA,MAAM,EACJ,MAAM,WAAW,EACjB,WAAW,cAAc,EACzB,SAAS,YAAY,EACtB,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAW;QACpB,UAAU;YAAC;YAAY,MAAM;YAAI;SAAU;QAC3C,OAAO;wCAAE,IAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QACxD,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM;IAClC;IAEA,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE;QACxC;QACA;IACF;IAEA,IAAI,CAAC,YAAY,cAAc,MAAM;QACnC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAe;;;;;;8BAC7B,6LAAC;oBAAE,WAAU;8BAAmB;;;;;;;;;;;;IAKtC;IAEA,IAAI,oBAAoB,CAAC,eAAe;QACtC,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IACjB;IACA,IAAI,gBAAgB;QAClB,qBACE,6LAAC;YAAE,WAAU;sBAAuB;;;;;;IAIxC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,8BACC,6LAAC,oJAAA,CAAA,cAAW;YACV,WAAW;YACX,gBAAgB;YAChB,aAAY;YACZ,SAAS,IAAM,iBAAiB;YAChC,UAAU;;;;;iCAGZ,6LAAC,gJAAA,CAAA,cAAW;YACV,kBAAkB;YAClB,WAAW;YACX,aAAY;YACZ,WAAW;YACX,aAAa;;;;;;;;;;;AAKvB;GArFwB;;QAGD,qIAAA,CAAA,YAAS;QACb,oHAAA,CAAA,UAAO;QAUpB,8KAAA,CAAA,WAAQ;QAO0B,8KAAA,CAAA,WAAQ;QAU1C,8KAAA,CAAA,WAAQ;QAMQ,iIAAA,CAAA,wBAAqB;;;KArCnB", "debugId": null}}]}